"use client";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

import { AuthFormFooter, AuthFormHeader } from "@/components/auth/auth-form-sections";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useAuth } from "@/composables/useStore";
import * as AuthService from "@/services/AuthService";

const RegisterPage = () => {
    const { setUser } = useAuth();
    const [firstname, setFirstname] = useState("");
    const [lastname, setLastname] = useState("");
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [gender, setGender] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const [formErrors, setFormErrors] = useState({
        firstname: "",
        lastname: "",
        email: "",
        password: "",
    });

    const router = useRouter();

    const validateForm = () => {
        const errors = { firstname: "", lastname: "", email: "", password: "" };
        let isValid = true;

        if (!email) {
            errors.email = "Email is required";
            isValid = false;
        } else if (!/\S+@\S+\.\S+/.test(email)) {
            errors.email = "Email address is invalid";
            isValid = false;
        }

        if (!password) {
            errors.password = "Password is required";
            isValid = false;
        } else if (password.length < 8) {
            errors.password = "Password must be at least 8 characters";
            isValid = false;
        }

        setFormErrors(errors);

        return isValid;
    };

    const handleSubmit = async (e: { preventDefault: () => void }) => {
        e.preventDefault();
        if (!validateForm()) {
            return;
        }
        setIsLoading(true);

        const response = await AuthService.register({
            first_name: firstname,
            last_name: lastname,
            email,
            password,
            gender,
        });

        if (!response.success) {
            for (const error of response.errors) {
                toast.error(error.message);
            }
        }

        if (response.success && response.data) {
            setUser(response.data);
            router.push("/auth/2fa");
        }

        setIsLoading(false);
    };

    return (
        <div className="flex min-h-screen">
            <form className="w-full flex items-center justify-center p-6 sm:p-12" onSubmit={handleSubmit}>
                <div className="w-full max-w-md">
                    <AuthFormHeader page="register" />

                    <div className="space-y-6">
                        <div className="flex flex-col lg:flex-row gap-3 lg:items-center">
                            <Input
                                required
                                autoComplete="first-name"
                                className="p-6"
                                error={formErrors.firstname}
                                id="firstname"
                                label="First name"
                                name="firstname"
                                placeholder="Firstname"
                                type="text"
                                value={firstname}
                                onChange={(e) => setFirstname(e.target.value)}
                            />

                            <Input
                                required
                                autoComplete="family-name"
                                className="p-6"
                                error={formErrors.lastname}
                                id="lastname"
                                label="Last name"
                                name="lastname"
                                placeholder="Lastname"
                                type="text"
                                value={lastname}
                                onChange={(e) => setLastname(e.target.value)}
                            />
                        </div>

                        <RadioGroup name="gender" value={gender} onValueChange={setGender}>
                            <div className="flex items-center gap-3">
                                <RadioGroupItem id="r1" value="FEMALE" />
                                <Label htmlFor="r1">Female</Label>
                            </div>
                            <div className="flex items-center gap-3">
                                <RadioGroupItem id="r2" value="MALE" />
                                <Label htmlFor="r2">Male</Label>
                            </div>
                        </RadioGroup>

                        <div>
                            <label className="block text-sm font-medium mb-1" htmlFor="email">
                                Email Address
                            </label>
                            <div className="relative">
                                <Input
                                    required
                                    autoComplete="email"
                                    className={`${formErrors.email ? "border-red-300" : ""} p-6`}
                                    id="email"
                                    name="email"
                                    placeholder="<EMAIL>"
                                    type="email"
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                />
                            </div>
                            {formErrors.email && <p className="mt-1 text-sm text-red-600">{formErrors.email}</p>}
                        </div>

                        <Input
                            required
                            autoComplete="current-password"
                            className="p-6"
                            error={formErrors.password}
                            id="password"
                            label="Password"
                            name="password"
                            placeholder="Enter password"
                            type="password"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                        />

                        <Button className="w-full" loading={isLoading} loadingText="Creating..." type="submit">
                            Create account
                        </Button>
                    </div>

                    <AuthFormFooter page="register" />
                </div>
            </form>
        </div>
    );
};

export default RegisterPage;
