"use client";
import { Arrow<PERSON>ef<PERSON>, Mail } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

import { AuthFormFooter } from "@/components/auth/auth-form-sections";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import * as AuthService from "@/services/AuthService";

const PasswordResetPage = () => {
    const [email, setEmail] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const [isSubmitted, setIsSubmitted] = useState(false);

    const router = useRouter();

    const validateEmail = (email: string) => {
        return /\S+@\S+\.\S+/.test(email);
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!email) {
            toast.error("Please enter your email address");

            return;
        }

        if (!validateEmail(email)) {
            toast.error("Please enter a valid email address");

            return;
        }

        setIsLoading(true);

        try {
            const response = await AuthService.requestPasswordRequest(email);

            if (response.errors.length) {
                for (const error of response.errors) {
                    toast.error(error.message);
                }
            }

            if (response.success) {
                toast.success("Password reset request was successful");
            }

            setIsSubmitted(true);
        } catch (error) {
            toast.error("Something went wrong. Please try again.");
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="flex min-h-screen">
            <div className="w-full flex items-center justify-center p-6 sm:p-12">
                <div className="w-full max-w-md">
                    {!isSubmitted ? (
                        <>
                            <div className="text-center mb-8">
                                <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                                    <Mail className="w-8 h-8 text-primary" />
                                </div>

                                <h2 className="text-2xl font-semibold mb-2">Reset Your Password</h2>

                                <p className="text-muted-foreground">
                                    Enter your email address and we will send you a link to reset your password
                                </p>
                            </div>

                            <form className="space-y-6" onSubmit={handleSubmit}>
                                <Input
                                    required
                                    className="mt-1"
                                    id="email"
                                    label="Email Address"
                                    placeholder="<EMAIL>"
                                    type="email"
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                />

                                <Button
                                    className="w-full"
                                    loading={isLoading}
                                    loadingText="Sending Reset Link..."
                                    type="submit"
                                >
                                    Send Reset Link
                                </Button>

                                <div className="text-center">
                                    <Button
                                        className="text-muted-foreground hover:text-foreground"
                                        type="button"
                                        variant="ghost"
                                        onClick={() => router.push("/auth/login")}
                                    >
                                        <ArrowLeft className="w-4 h-4 mr-2" />
                                        Back to Sign In
                                    </Button>
                                </div>
                            </form>
                        </>
                    ) : (
                        <>
                            <div className="text-center mb-8">
                                <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                                    <Mail className="w-8 h-8 text-green-600" />
                                </div>

                                <h2 className="text-2xl font-semibold mb-2">Check Your Email</h2>

                                <p className="text-muted-foreground mb-4">
                                    If an account with that email address exists, we have sent you a password reset
                                    link.
                                </p>

                                <p className="text-sm text-muted-foreground">
                                    Please check your email and follow the instructions to reset your password.
                                    Don&apos;t forget to check your spam folder.
                                </p>
                            </div>

                            <div className="space-y-4">
                                <Button
                                    className="w-full"
                                    variant="outline"
                                    onClick={() => {
                                        setIsSubmitted(false);
                                        setEmail("");
                                    }}
                                >
                                    Try Different Email
                                </Button>

                                <div className="text-center">
                                    <Button
                                        className="text-muted-foreground hover:text-foreground"
                                        type="button"
                                        variant="ghost"
                                        onClick={() => router.push("/auth/login")}
                                    >
                                        <ArrowLeft className="w-4 h-4 mr-2" />
                                        Back to Sign In
                                    </Button>
                                </div>
                            </div>
                        </>
                    )}

                    <AuthFormFooter page="password-reset" />
                </div>
            </div>
        </div>
    );
};

export default PasswordResetPage;
