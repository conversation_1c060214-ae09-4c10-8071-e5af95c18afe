"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Plus, Settings, ArrowLeft } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { WorkflowTemplatesList } from "@/components/workflows/WorkflowTemplatesList";
import { CreateTemplateModal } from "@/components/workflows/CreateTemplateModal";
import { WorkflowTemplateCode, WorkflowTemplateOption } from "@/types";

const WORKFLOW_TEMPLATE_OPTIONS: WorkflowTemplateOption[] = [
    {
        code: "ORGANIZATION_REGISTRATION",
        name: "Organization Registration",
        description: "Approval workflow for new organization registrations",
        icon: "🏢",
    },
    {
        code: "LICENCE_RENEWAL",
        name: "License Renewal",
        description: "Approval workflow for license renewal applications",
        icon: "📋",
    },
    {
        code: "PERMIT_APPLICATION",
        name: "Permit Application",
        description: "Approval workflow for permit applications",
        icon: "📄",
    },
];

export default function WorkflowSettingsPage() {
    const router = useRouter();
    const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
    const [selectedTemplateType, setSelectedTemplateType] = useState<WorkflowTemplateCode | null>(null);

    const handleCreateTemplate = (templateType: WorkflowTemplateCode) => {
        setSelectedTemplateType(templateType);
        setIsCreateModalOpen(true);
    };

    const handleTemplateCreated = () => {
        setIsCreateModalOpen(false);
        setSelectedTemplateType(null);
        // Refresh the templates list
    };

    return (
        <div className="flex flex-col space-y-6 p-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                    <Button
                        className="flex items-center space-x-2"
                        size="sm"
                        variant="ghost"
                        onClick={() => router.push("/settings")}
                    >
                        <ArrowLeft className="h-4 w-4" />
                        <span>Back to Settings</span>
                    </Button>
                </div>
            </div>

            <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tight">Workflow Management</h1>
                <p className="text-muted-foreground">Configure approval workflows for different business processes</p>
            </div>

            {/* Quick Actions */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {WORKFLOW_TEMPLATE_OPTIONS.map((option) => (
                    <Card key={option.code} className="cursor-pointer hover:shadow-md transition-shadow">
                        <CardHeader className="pb-3">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-3">
                                    <div className="text-2xl">{option.icon}</div>
                                    <div>
                                        <CardTitle className="text-lg">{option.name}</CardTitle>
                                        <CardDescription className="text-sm">{option.description}</CardDescription>
                                    </div>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent className="pt-0">
                            <div className="flex items-center justify-between">
                                <Badge className="text-xs" variant="outline">
                                    {option.code}
                                </Badge>
                                <Button
                                    className="flex items-center space-x-1"
                                    size="sm"
                                    onClick={() => handleCreateTemplate(option.code)}
                                >
                                    <Plus className="h-3 w-3" />
                                    <span>Create</span>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                ))}
            </div>

            {/* Existing Templates */}
            <div className="space-y-4">
                <div className="flex items-center justify-between">
                    <h2 className="text-xl font-semibold">Existing Workflow Templates</h2>
                    <Button className="flex items-center space-x-2" size="sm" variant="outline">
                        <Settings className="h-4 w-4" />
                        <span>Manage All</span>
                    </Button>
                </div>

                <WorkflowTemplatesList />
            </div>

            {/* Create Template Modal */}
            <CreateTemplateModal
                isOpen={isCreateModalOpen}
                templateType={selectedTemplateType}
                onClose={() => setIsCreateModalOpen(false)}
                onTemplateCreated={handleTemplateCreated}
            />
        </div>
    );
}
