/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import { useState, useEffect, useCallback } from "react";
import { Plus, Search } from "lucide-react";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { DataTable } from "@/components/ui/data-table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { FeeDto, FeeFilter, FeeCreateRequest, FeeUpdateRequest, LoadableItemDto } from "@/types";
import * as FeeService from "@/services/FeeService";
import * as SettingsService from "@/services/SettingsService";
import { FeeForm } from "@/components/fees/FeeForm";
import { DeleteConfirmationDialog, useDeleteConfirmation } from "@/components/ui/delete-confirmation-dialog";

interface FeesPageProps {}

export default function FeesPage({}: FeesPageProps) {
    const [fees, setFees] = useState<FeeDto[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [searchTerm, setSearchTerm] = useState("");
    const [statusFilter, setStatusFilter] = useState<string>("all");
    const [categoryFilter, setCategoryFilter] = useState<string>("all");
    const [showCreateDialog, setShowCreateDialog] = useState(false);
    const [selectedFee, setSelectedFee] = useState<FeeDto | null>(null);
    const [formLoading, setFormLoading] = useState(false);

    // Delete confirmation dialog
    const { isOpen: isDeleteOpen, openDeleteDialog, closeDeleteDialog, deleteConfig } = useDeleteConfirmation();

    // Pagination
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const [totalItems, setTotalItems] = useState(0);

    // Reference data
    const [categories, setCategories] = useState<LoadableItemDto[]>([]);

    // Load reference data
    const loadReferenceData = useCallback(async () => {
        try {
            const categoriesRes = await SettingsService.fetchLoadableItems({ type: "FEE_CATEGORY", size: 100 });

            if (categoriesRes.success) {
                setCategories(categoriesRes.data || []);
            }
        } catch (err) {
            setError("Failed to load reference data");
        }
    }, []);

    // Load fees
    const loadFees = useCallback(async () => {
        setLoading(true);
        setError(null);

        try {
            const filter: FeeFilter = {
                page: currentPage,
                size: pageSize,
                name: searchTerm || undefined,
                status: statusFilter !== "all" ? (statusFilter as any) : undefined,
                fee_category_id: categoryFilter !== "all" ? categoryFilter : undefined,
            };

            const response = await FeeService.fetchFees(filter);

            if (response.success && response.data) {
                const transformedFees = response.data.map(FeeService.transformFeeForTable);

                setFees(transformedFees);
                setTotalItems(response.total || 0);
            } else {
                setError(response.errors?.[0]?.message || "Failed to load fees");
            }
        } catch (err) {
            setError("An unexpected error occurred");
        } finally {
            setLoading(false);
        }
    }, [currentPage, pageSize, searchTerm, statusFilter, categoryFilter]);

    // Effects
    useEffect(() => {
        loadReferenceData();
    }, [loadReferenceData]);

    useEffect(() => {
        loadFees();
    }, [loadFees]);

    // Handlers
    const handleSearch = (value: string) => {
        setSearchTerm(value);
        setCurrentPage(1); // Reset to first page when searching
    };

    const handleStatusFilter = (value: string) => {
        setStatusFilter(value);
        setCurrentPage(1);
    };

    const handleCategoryFilter = (value: string) => {
        setCategoryFilter(value);
        setCurrentPage(1);
    };

    const handleCreateFee = () => {
        setSelectedFee(null);
        setShowCreateDialog(true);
    };

    const handleEditFee = (fee: FeeDto) => {
        setSelectedFee(fee);
        setShowCreateDialog(true);
    };

    const handleDeleteFee = (fee: FeeDto) => {
        if (!fee) {
            return;
        }
        setSelectedFee(fee);
        openDeleteDialog({
            onConfirm: async () => {
                try {
                    setLoading(true);
                    const response = await FeeService.deleteFee(fee.id);

                    if (response.success) {
                        toast.success("Fee deleted successfully");
                        closeDeleteDialog();
                        loadFees(); // Reload the list
                    } else {
                        toast.error(response.errors?.[0]?.message || "Failed to delete fee");
                    }
                    setLoading(false);
                } catch (err) {
                    setLoading(false);
                    alert("An unexpected error occurred");
                }
            },
            confirmationText: fee.name,
            itemName: fee.name,
            title: "Delete Fee",
            description: `This will permanently delete the fee "${fee.name}" and all associated data. This action cannot be undone.`,
        });
    };

    const handleActivateFee = async (fee: FeeDto) => {
        if (fee.status !== "SCHEDULED") {
            return;
        }

        try {
            const response = await FeeService.activateFee(fee.id);

            if (response.success) {
                loadFees(); // Reload the list
            } else {
                alert(response.errors?.[0]?.message || "Failed to activate fee");
            }
        } catch (err) {
            alert("An unexpected error occurred");
        }
    };

    const handleFormSubmit = async (data: FeeCreateRequest | FeeUpdateRequest) => {
        setFormLoading(true);

        try {
            let response;

            if (selectedFee) {
                // Update existing fee
                response = await FeeService.updateFee(selectedFee.id, data as FeeUpdateRequest);
            } else {
                // Create new fee
                response = await FeeService.createFee(data as FeeCreateRequest);
            }

            if (response.success) {
                toast.success("Fee saved successfully");
                setShowCreateDialog(false);
                setSelectedFee(null);
                loadFees(); // Reload the list
            } else {
                throw new Error(response.errors?.[0]?.message || "Failed to save fee");
            }
        } catch (err: any) {
            toast.error(err.message);
            throw err; // Re-throw to let the form handle the error
        } finally {
            setFormLoading(false);
        }
    };

    const handleFormCancel = () => {
        setShowCreateDialog(false);
        setSelectedFee(null);
    };

    const getStatusBadgeVariant = (status: string) => {
        switch (status) {
            case "ACTIVE":
                return "default";
            case "SCHEDULED":
                return "secondary";
            case "EXPIRED":
                return "outline";
            case "DRAFT":
                return "outline";
            default:
                return "outline";
        }
    };

    // Table columns
    const columns = [
        {
            accessorKey: "name",
            header: "Fee Name",
            cell: ({ row }: any) => <div className="font-medium">{row.original.name}</div>,
        },
        {
            accessorKey: "categoryName",
            header: "Category",
            cell: ({ row }: any) => <div className="text-sm text-muted-foreground">{row.original.categoryName}</div>,
        },
        {
            accessorKey: "currencyDisplay",
            header: "Amount",
            cell: ({ row }: any) => <div className="font-mono font-medium">{row.original.currencyDisplay}</div>,
        },
        {
            accessorKey: "status",
            header: "Status",
            cell: ({ row }: any) => (
                <Badge variant={getStatusBadgeVariant(row.original.status)}>{row.original.status}</Badge>
            ),
        },
        {
            accessorKey: "incomeRangeDisplay",
            header: "Income Range",
            cell: ({ row }: any) => (
                <div className="text-sm text-muted-foreground">{row.original.incomeRangeDisplay}</div>
            ),
        },
        {
            accessorKey: "effectivePeriodDisplay",
            header: "Effective Period",
            cell: ({ row }: any) => (
                <div className="text-sm text-muted-foreground">{row.original.effectivePeriodDisplay}</div>
            ),
        },
        {
            id: "actions",
            header: "Actions",
            cell: ({ row }: any) => (
                <div className="flex items-center gap-2">
                    <Button size="sm" variant="ghost" onClick={() => handleEditFee(row.original)}>
                        Edit
                    </Button>
                    {row.original.status === "SCHEDULED" && (
                        <Button size="sm" variant="ghost" onClick={() => handleActivateFee(row.original)}>
                            Activate
                        </Button>
                    )}
                    <Button
                        className="text-destructive hover:text-destructive"
                        size="sm"
                        variant="ghost"
                        onClick={() => handleDeleteFee(row.original)}
                    >
                        Delete
                    </Button>
                </div>
            ),
        },
    ];

    if (loading && fees.length === 0) {
        return (
            <div className="space-y-6 p-6">
                <div className="flex justify-between items-center">
                    <div>
                        <Skeleton className="h-8 w-48" />
                        <Skeleton className="h-4 w-96 mt-2" />
                    </div>
                    <Skeleton className="h-10 w-32" />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    {[...Array(4)].map((_, i) => (
                        <Card key={i} className="p-6">
                            <Skeleton className="h-4 w-24 mb-2" />
                            <Skeleton className="h-8 w-16" />
                        </Card>
                    ))}
                </div>

                <Card className="p-6">
                    <Skeleton className="h-64 w-full" />
                </Card>
            </div>
        );
    }

    return (
        <div className="space-y-6 p-6">
            {/* Page Header */}
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-2xl font-semibold tracking-tight">Fee Management</h1>
                    <p className="text-sm text-muted-foreground">
                        Manage organizational fees, pricing structures, and income-based calculations
                    </p>
                </div>
                <div className="flex items-center gap-3">
                    <Button onClick={handleCreateFee}>
                        <Plus className="h-4 w-4 mr-2" />
                        Add Fee
                    </Button>
                </div>
            </div>
            {/* Delete Confirmation Dialog */}
            <DeleteConfirmationDialog
                confirmationText={deleteConfig?.confirmationText || ""}
                description={deleteConfig?.description}
                itemName={deleteConfig?.itemName}
                loading={loading}
                open={isDeleteOpen}
                title={deleteConfig?.title}
                variant={deleteConfig?.variant}
                onConfirm={() => deleteConfig?.onConfirm()}
                onOpenChange={closeDeleteDialog}
            />
            {/* Filters */}
            <Card className="p-6">
                <div className="flex flex-col sm:flex-row gap-4">
                    <div className="flex-1">
                        <div className="relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                            <Input
                                className="pl-10"
                                placeholder="Search fees by name..."
                                value={searchTerm}
                                onChange={(e) => handleSearch(e.target.value)}
                            />
                        </div>
                    </div>
                    <Select value={statusFilter} onValueChange={handleStatusFilter}>
                        <SelectTrigger className="w-full sm:w-48">
                            <SelectValue placeholder="Filter by status" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">All Statuses</SelectItem>
                            <SelectItem value="ACTIVE">Active</SelectItem>
                            <SelectItem value="SCHEDULED">Scheduled</SelectItem>
                            <SelectItem value="EXPIRED">Expired</SelectItem>
                            <SelectItem value="DRAFT">Draft</SelectItem>
                        </SelectContent>
                    </Select>
                    <Select value={categoryFilter} onValueChange={handleCategoryFilter}>
                        <SelectTrigger className="w-full sm:w-48">
                            <SelectValue placeholder="Filter by category" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">All Categories</SelectItem>
                            {categories.map((category) => (
                                <SelectItem key={category.id} value={category.id}>
                                    {category.display_value}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>
            </Card>

            {/* Fees Table */}
            <DataTable
                columns={columns}
                data={fees}
                enableFiltering={false} // We handle filtering above
                enablePagination={true}
                enableSorting={true}
                pageSize={pageSize}
                searchKey="name"
                searchPlaceholder="Search fees..."
            />

            {/* Create/Edit Dialog */}
            <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
                <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>{selectedFee ? "Edit Fee" : "Create New Fee"}</DialogTitle>
                    </DialogHeader>
                    <FeeForm
                        fee={selectedFee}
                        loading={formLoading}
                        onCancel={handleFormCancel}
                        onSubmit={handleFormSubmit}
                    />
                </DialogContent>
            </Dialog>
        </div>
    );
}
