"use client"

import { <PERSON><PERSON> } from "@heroui/alert";
import { <PERSON><PERSON> } from "@heroui/button";
import { Card } from "@heroui/card";
import { Input } from "@heroui/input";
import {
	Modal,
	ModalBody,
	ModalContent,
	ModalHeader,
	useDisclosure,
} from "@heroui/modal";
import { addToast } from "@heroui/toast";
import { useEffect, useRef, useState } from "react";

import { Dialog, DialogSelection } from "@/components/dialog";
import { AddCircleOutline, MagniferLinear } from "@/components/icons";
import UserForm from "@/components/users/user-form";
import UsersTable from "@/components/users/user-table";
import { useAuth } from "@/composables/useStore";
import * as UserService from "@/services/UserService";
import { UserDto, UserRequest } from "@/types";
import { getTotalPages } from "@/utils/common";

export default function UsersIndexPage() {
  const { isAuthenticated } = useAuth();
  const [users, setUsers] = useState<UserDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>("");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const { isOpen, onOpen, onOpenChange } = useDisclosure();
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState<number>(0);
  const [formMode, setFormMode] = useState<"create" | "edit">("create");
  const [selectedUser, setSelectedUser] = useState<UserDto | null>(null);
  const [showDeleteDialog, setDeleteDialog] = useState<boolean>(false);

  const fetchedRef = useRef(false);
  const debounceTimeout = useRef<NodeJS.Timeout | null>(null);

  const fetchUsers = async (filter: Partial<UserRequest>) => {
    fetchedRef.current = true;
    setLoading(true);
    setError("");

    try {
      const response = await UserService.fetchAll({
        ...filter,
        search: searchQuery,
      });

      if (response.success && response.data) {
        setUsers(response.data);
        setPage(filter.page || 1);
        setTotal(getTotalPages(response.total || 15, 15));
      } else {
        setError("Failed to retrieve users");
        addToast({
          title: "Error",
          color: "danger",
          description: "Failed to retrieve users",
        });
      }
    } catch (err) {
      setError("An error occurred while fetching users");
      addToast({
        title: "Error",
        color: "danger",
        description: "An error occurred while fetching users",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isAuthenticated && !fetchedRef.current) {
      fetchUsers({ page: 1, size: 15 });
    }
  }, [isAuthenticated]);

  useEffect(() => {
    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
    }

    debounceTimeout.current = setTimeout(() => {
      fetchUsers({ page: 1, size: 15 });
    }, 300);

    return () => {
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current);
      }
    };
  }, [searchQuery]);

  function onAdded(data: UserDto) {
    users.unshift(data);
    onOpenChange();
  }

  function onEditing(user: UserDto) {
    setFormMode("edit");
    setSelectedUser(user);
    onOpen();
  }

  function onUpdated(data: UserDto) {
    users.forEach((user) => {
      if (user.user_id === data.user_id) {
        Object.assign(user, data);
      }
    });

    setUsers(users);
  }

  function onDeleting(user: UserDto) {
    setDeleteDialog(true);
    setSelectedUser(user);
  }

  async function deleteUser(state: DialogSelection) {
    if (state === "denied" || !selectedUser) {
      return;
    }

    const response = await UserService.deleteUser(selectedUser.user_id);

    if (response.data) {
      fetchUsers({ page, size: 15 });
    }

    if (response.errors.length) {
      for (const error of response.errors) {
        addToast({
          title: "Deleting Error",
          description: error.message,
          color: "danger",
        });
      }
    }

    setDeleteDialog(false);
  }

  return (
    <section className="w-full p-6 gap-4">
      <Alert
        className="mb-3"
        color="danger"
        description={error}
        isVisible={!!error}
        title="Error"
        variant="faded"
        onClose={() => setError("")}
      />

      <Card className="shadow-none">
        <h1 className="text-2xl font-bold mb-6">Users</h1>

        <div className="flex flex-col md:flex-row md:justify-between gap-4 mb-6">
          <div className="max-w-[250px]">
            <Input
              placeholder="Search by name or username"
              startContent={<MagniferLinear height="1.2em" />}
              value={searchQuery}
              onValueChange={(value) => setSearchQuery(value)}
            />
          </div>

          <Button
            color="primary"
            startContent={<AddCircleOutline height="1.2em" />}
            onPress={onOpen}
          >
            Add User
          </Button>
        </div>
        <UsersTable
          isLoading={loading}
          page={page}
          pages={total}
          users={users}
          onDelete={onDeleting}
          onEdit={onEditing}
          onPage={(e) => fetchUsers({ page: e })}
        />
      </Card>

      <Modal isOpen={isOpen} placement="top-center" onOpenChange={onOpenChange}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                {formMode === "create" ? "Create User" : "Edit User"}
              </ModalHeader>
              <ModalBody>
                <UserForm
                  formMode={formMode}
                  user={selectedUser}
                  onAdd={onAdded}
                  onClose={onClose}
                  onUpdate={onUpdated}
                />
              </ModalBody>
            </>
          )}
        </ModalContent>
      </Modal>

      <Dialog
        description="Are you sure you want to delete this user?"
        show={showDeleteDialog}
        title="Delete user"
        onSelection={deleteUser}
      />
    </section>
  );
}
