"use client";

import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";

import UsersView from "@/components/users/users-view";
import * as userService from "@/services/UserService";

const UsersIndexPage = () => {
    const {
        data: users = [],
        isLoading,
        error,
    } = useQuery({
        queryKey: ["users", { page: 1, size: 20 }],
        queryFn: async () => {
            const response = await userService.fetchAll({ page: 1, size: 20, is_external: false });

            if (response.errors && response.errors.length > 0) {
                for (const error of response.errors) {
                    toast.error(error.message);
                }
            }

            return response.data || [];
        },
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        retry: 2,
        refetchOnWindowFocus: false,
    });

    if (error) {
        toast.error("Failed to fetch users");
    }

    return (
        <div>
            <UsersView
                isLoading={isLoading}
                users={users}
                onDelete={(user) => console.log("Delete:", user)}
                onEdit={(user) => console.log("Edit:", user)}
            />
        </div>
    );
};

export default UsersIndexPage;
