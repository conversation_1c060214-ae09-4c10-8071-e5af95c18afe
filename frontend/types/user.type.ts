import { Pagination } from "./common.type";
import { RoleDto } from "./role.type";

export type Gender = "FEMALE" | "MALE";

export interface UserDto {
    id: string;
    first_name: string;
    last_name: string;
    middle_name: string;
    handle: string;
    email: string;
    account_status: string;
    is_external: boolean;
    phone: string;
    role_id: string;
    role: RoleDto | null;
}

export interface UserRequest {
    first_name: string;
    last_name: string;
    email: string;
    password: string;
    gender: string;
    search?: string;
}

export interface TwoFactorVerificationRequest {
    email?: string;
    code?: string;
    token?: string;
}

export type UserFilter = {} & Pagination & UserDto;
