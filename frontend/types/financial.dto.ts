import { OrganizationDto } from "./organization.dto";

export type InvoiceStatus = "PENDING" | "PAID" | "PARTIALLY_PAID";

export type PaymentStatus = "SUCCESS" | "FAILED";

export interface InvoiceDocumentDto {
    id: string;
    invoice_id: string;
    document_id: string;
    description: string;
    filename: string;
    location: string;
    original_name: string;
    size: string;
}

export interface InvoiceDto {
    id: string;
    reference_number: string;
    status: InvoiceStatus;
    total_amount: number;
    due_date: Date;
    description: string;
    organization_id: string;
    created_at: Date;
    updated_at: Date;
    invoice_documents: InvoiceDocumentDto[];
    organization?: OrganizationDto;
}

export interface PaymentDto {
    id: string;
    amount: number;
    organization_id: string;
    invoice_id: string;
    transaction_number: string;
    payment_mode_id: string;
    status: PaymentStatus;
    paid_by: string;
    organization?: OrganizationDto;
    invoice?: InvoiceDto;
    payment_mode: string;
}
