import { ReactNode, SVGProps } from "react";

export type IconSvgProps = SVGProps<SVGSVGElement> & {
    size?: number;
};

export interface LayoutProps {
    children: ReactNode;
}

export * from "./activity.dto";
export * from "./auth.dto";
export * from "./common.type";
export * from "./department.dto";
export * from "./fee.dto";
export * from "./financial.dto";
export * from "./role.type";
export * from "./session.dto";
export * from "./settings.dto";
export * from "./user.type";
