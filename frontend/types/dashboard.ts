export interface DashboardStats {
    totalUsers: number;
    totalOrganizations: number;
    pendingApplications: number;
    approvedApplications: number;
    totalRevenue: number;
    activeWorkflows: number;
    monthlyGrowth: {
      users: number;
      organizations: number;
      applications: number;
      revenue: number;
    };
  }
  
  export interface RecentActivity {
    id: string;
    type: 'application' | 'organization' | 'user' | 'payment' | 'workflow';
    title: string;
    description: string;
    timestamp: string;
    status?: 'pending' | 'approved' | 'rejected' | 'completed';
    user?: string;
    avatar?: string;
  }
  
  export interface ApplicationPipeline {
    stage: string;
    count: number;
    percentage: number;
    color: 'blue' | 'yellow' | 'green' | 'red';
  }
  
  export interface User {
    name: string;
    role: string;
    lastLogin?: string;
  }