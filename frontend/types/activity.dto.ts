export type ActivityVisibility = "ALL" | "LIMITED";

export interface ActivityDto {
    id: string;
    title: string;
    category_id: string;
    visibility: ActivityVisibility;
    venue: string;
    district_id: string;
    longitude: string;
    latitude: string;
    map_pin: string;
    start_date: string;
    end_date: string;
    summary: string;
    created_at: string;
    updated_at: string;
}

export interface ActivityRequest {
    id?: string;
    title: string;
    category_id: string;
    visibility: ActivityVisibility;
    venue: string;
    district_id: string;
    longitude?: string;
    latitude?: string;
    map_pin?: string;
    start_date: Date;
    end_date: Date;
    summary: string;
}

export interface ActivityFilter {
    title?: string;
    venue?: string;
}
