import { AxiosResponse } from "axios";
import { HttpResponse } from "@/types";

export function httpResponse<T>(
    response: AxiosResponse | any,
): HttpResponse<T> {
    if (!response) {
        return {
            data: undefined,
            success: false,
            errors: [],
            status: 500,
            total: 0,
            page: 0,
            size: 0,
        };
    }

    const hasDetail = response.data && Object.keys(response.data).includes("detail");
    const sourceData = hasDetail ? response.data.detail : response.data;

    return {
        data: sourceData?.data,
        success: sourceData?.success ?? false,
        errors: sourceData?.errors ?? [],
        status: response.status,
        total: sourceData?.total ?? 0,
        page: sourceData?.page ?? 0,
        size: sourceData?.size ?? 0,
    };
}

export function jsonToQueryParams(json: Record<string, any>): string {
    return Object.keys(json)
        .filter(key => json[key] !== undefined && json[key] !== null)
        .map(
            (key) =>
                `${encodeURIComponent(key)}=${encodeURIComponent(json[key])}`,
        )
        .join("&");
}

export function getTotalPages(
    totalCount: number,
    itemsPerPage: number,
): number {
    if (itemsPerPage <= 0) {
        throw new Error("itemsPerPage must be greater than 0");
    }

    return Math.ceil(totalCount / itemsPerPage);
}
