{"name": "next-app-template", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev -p 4000 --turbopack", "build": "next build", "start": "next start", "lint": "eslint --fix"}, "dependencies": {"@headlessui/react": "^2.2.0", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.1.1", "@inertiajs/react": "^2.0.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.2.7", "@react-aria/ssr": "3.9.8", "@react-aria/visually-hidden": "3.8.22", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.84.1", "@tanstack/react-table": "^8.21.3", "@tiptap/core": "^3.0.7", "@tiptap/extension-link": "^3.0.7", "@tiptap/extension-text-align": "^3.0.7", "@tiptap/extension-underline": "^3.0.7", "@tiptap/react": "^3.0.7", "@tiptap/starter-kit": "^3.0.7", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "framer-motion": "11.13.1", "input-otp": "^1.4.2", "intl-messageformat": "10.7.16", "lucide-react": "^0.509.0", "next": "15.3.1", "next-themes": "0.4.6", "prosemirror-state": "^1.4.3", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.60.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.76", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/compat": "1.2.8", "@eslint/eslintrc": "3.3.1", "@eslint/js": "9.25.1", "@next/eslint-plugin-next": "15.3.1", "@react-types/shared": "3.29.0", "@types/node": "22.15.3", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@typescript-eslint/eslint-plugin": "8.31.1", "@typescript-eslint/parser": "8.31.1", "autoprefixer": "10.4.21", "eslint": "9.25.1", "eslint-config-next": "15.3.1", "eslint-config-prettier": "10.1.2", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "5.2.6", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-unused-imports": "4.1.4", "globals": "16.0.0", "postcss": "8.5.3", "prettier": "3.5.3", "tailwind-variants": "0.3.0", "tailwindcss": "3.4.16", "typescript": "5.6.3"}}