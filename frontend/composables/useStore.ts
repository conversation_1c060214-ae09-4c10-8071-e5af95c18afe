import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

import { siteConfig } from "@/config/site";
import { AuthDto } from "@/types";

interface AppState {
    session: AuthDto | null;
    isAuthenticated: boolean;
    isLoading: boolean; // ADD THIS

    sidebarExpanded: boolean;
    darkMode: boolean;
    currentPage: string;

    setUser: (session: AuthDto | null) => void;
    destroySession: () => void;
    initializeAuth: () => void; // ADD THIS
    toggleSidebar: () => void;
    setSidebarExpanded: (expanded: boolean) => void;
    toggleDarkMode: () => void;
    setCurrentPage: (page: string) => void;
}

export const useStore = create<AppState>()(
    persist(
        (set, get) => ({
            session: null,
            isAuthenticated: false,
            isLoading: true,

            sidebarExpanded: true,
            darkMode: false,
            currentPage: "",

            setUser: (session) =>
                set({
                    session,
                    isAuthenticated: !!session,
                    isLoading: false,
                }),

            destroySession: () =>
                set({
                    session: null,
                    isAuthenticated: false,
                    isLoading: false,
                }),

            initializeAuth: () => {
                const state = get();
                if (state.session) {
                    set({ isAuthenticated: true, isLoading: false });
                } else {
                    set({ isAuthenticated: false, isLoading: false });
                }
            },

            toggleSidebar: () =>
                set((state) => ({
                    sidebarExpanded: !state.sidebarExpanded,
                })),

            setSidebarExpanded: (expanded) =>
                set({
                    sidebarExpanded: expanded,
                }),

            toggleDarkMode: () =>
                set((state) => ({
                    darkMode: !state.darkMode,
                })),

            setCurrentPage: (page) =>
                set(
                    {
                        currentPage: page,
                    },
                    false,
                ),
        }),
        {
            name: siteConfig.APP_STATE,
            storage: createJSONStorage(() => localStorage),
            partialize: (state) => ({
                session: state.session,
                isAuthenticated: state.isAuthenticated,
                sidebarExpanded: state.sidebarExpanded,
                darkMode: state.darkMode,
                currentPage: state.currentPage,
            }),
        },
    ),
);

export const useAuth = () => {
    const { session, isAuthenticated, isLoading, destroySession, setUser, initializeAuth } = useStore();

    return {
        session,
        isAuthenticated,
        isLoading,
        destroySession,
        setUser,
        initializeAuth,
    };
};

export const useUI = () => {
    const {
        sidebarExpanded,
        currentPage,
        darkMode,
        toggleSidebar,
        setSidebarExpanded,
        toggleDarkMode,
        setCurrentPage,
    } = useStore();

    return {
        sidebarExpanded,
        currentPage,
        darkMode,
        toggleSidebar,
        setSidebarExpanded,
        toggleDarkMode,
        setCurrentPage,
    };
};
