"use client";

import { ReactNode, useEffect } from "react";

import { AppSidebar } from "@/components/app-sidebar";
import { Separator } from "@/components/ui/separator";
import { SidebarInset, SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { useAuth } from "@/composables/useStore";
import ProtectedRoute from "@/guards/ProtectedRoute";

interface Props {
    children: ReactNode;
}

export default function DefaultLayout({ children }: Props) {
    const { initializeAuth } = useAuth();

    useEffect(() => {
        initializeAuth();
    }, [initializeAuth]);

    return (
        <ProtectedRoute
            node={
                <div className="min-h-dvh w-full">
                    <SidebarProvider>
                        <AppSidebar />
                        <SidebarInset>
                            <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
                                <div className="flex items-center gap-2 px-4">
                                    <SidebarTrigger className="-ml-1" />
                                    <Separator
                                        className="mr-2 data-[orientation=vertical]:h-4"
                                        orientation="vertical"
                                    />
                                    {/* <Breadcrumb>
                                        <BreadcrumbList>
                                            <BreadcrumbItem className="hidden md:block">
                                                <BreadcrumbLink href="#">
                                                    Building Your Application
                                                </BreadcrumbLink>
                                            </BreadcrumbItem>
                                            <BreadcrumbSeparator className="hidden md:block" />
                                            <BreadcrumbItem>
                                                <BreadcrumbPage>
                                                    Data Fetching
                                                </BreadcrumbPage>
                                            </BreadcrumbItem>
                                        </BreadcrumbList>
                                    </Breadcrumb> */}
                                </div>
                            </header>
                            <div className="flex flex-1 flex-col gap-4 p-4 pt-0">{children}</div>
                        </SidebarInset>
                    </SidebarProvider>
                </div>
            }
        />
    );
}
