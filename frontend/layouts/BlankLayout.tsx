"use client";

import { useEffect } from "react";

import { useAuth } from "@/composables/useStore";
import ProtectedRoute from "@/guards/ProtectedRoute";
import { LayoutProps } from "@/types";

export default function BlankLayout({ children }: LayoutProps) {
    const { initializeAuth } = useAuth();

    useEffect(() => {
        initializeAuth();
    }, [initializeAuth]);

    return <ProtectedRoute node={<div className="min-h-dvh w-full">{children}</div>} />;
}
