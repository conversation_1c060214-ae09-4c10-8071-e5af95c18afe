import axios from "axios";

import api from "./api.config";

import { getRefreshToken, startSession } from "@/services/SessionService";

const axiosInstance = axios.create({
    withCredentials: true,
    headers: {
        "Content-Type": "application/json",
    },
});

let navigateFunction: ((path: string) => void) | null = null;

export const setNavigateFunction = (navigate: ((path: string) => void) | null) => {
    navigateFunction = navigate;
};

axiosInstance.interceptors.response.use(
    (response) => response,
    async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && originalRequest.url === api.v1.auth.refreshToken) {
            redirectToAuth();

            return Promise.reject(error);
        }

        if (error.response?.status === 401 && !originalRequest._retry) {
            originalRequest._retry = true;

            try {
                const refreshToken = getRefreshToken();

                if (!refreshToken) {
                    redirectToAuth();

                    return Promise.reject(error);
                }

                const response = await axiosInstance.post(api.v1.auth.refreshToken, {
                    refresh_token: refreshToken,
                });

                if (response.status === 201) {
                    startSession(response.data);

                    return axiosInstance(originalRequest);
                }

                redirectToAuth();

                return Promise.reject(error);
            } catch (refreshError) {
                redirectToAuth();

                return Promise.reject(refreshError);
            }
        }

        return Promise.reject(error);
    },
);

const redirectToAuth = () => {
    // if (navigateFunction) {
    //     navigateFunction("/auth");
    // } else {
    //     window.location.href = "/auth";
    // }
};

export default axiosInstance;
