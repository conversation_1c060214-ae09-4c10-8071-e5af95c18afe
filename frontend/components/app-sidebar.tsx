"use client";

import {
    AwardIcon,
    BanIcon,
    Bot,
    BriefcaseIcon,
    Building2Icon,
    CalendarIcon,
    ChartLineIcon,
    CogIcon,
    CompassIcon,
    CreditCardIcon,
    DatabaseIcon,
    FileTextIcon,
    GalleryVerticalEnd,
    GroupIcon,
    HomeIcon,
    LucideIcon,
    ScaleIcon,
    Settings2,
    TicketCheckIcon,
    Users2Icon,
} from "lucide-react";
import * as React from "react";

import { AccountSwitcher } from "./account-switcher";
import { NavAdmin } from "./nav-admin";

import { NavMain } from "@/components/nav-main";
import { NavUser } from "@/components/nav-user";
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarRail } from "@/components/ui/sidebar";
import { useAuth } from "@/composables/useStore";

type SidebarNavItem = {
    title: string;
    url: string;
    items?: Array<{ title: string; url: string; icon?: LucideIcon }>;
    icon?: LucideIcon;
};

type AdminLink = {
    name: string;
    url: string;
    icon: LucideIcon;
};

type SidebarProps = {
    accounts: Array<{ name: string; logo: React.ElementType }>;
    navMain: SidebarNavItem[];
    adminLinks: AdminLink[];
};

const data: SidebarProps = {
    accounts: [],
    navMain: [
        {
            title: "Dashboard",
            url: "/dashboard",
            icon: HomeIcon,
            items: [],
        },
        {
            title: "NGO Registration",
            url: "#",
            icon: Bot,
            items: [
                {
                    title: "Registration",
                    icon: FileTextIcon,
                    url: "/registration_request",
                },
                {
                    title: "NGO Approvals",
                    icon: TicketCheckIcon,
                    url: "/registration_approvals",
                },
                {
                    title: "Certificates",
                    icon: AwardIcon,
                    url: "/registration_certificate",
                }
            ],
        },
        {
            title: "Reporting",
            url: "#",
            icon: ChartLineIcon,
            items: [
                {
                    title: "Report Submission",
                    icon: FileTextIcon,
                    url: "/licensing_renewal",
                },
                {
                    title: "Reporting Approvals",
                    icon: TicketCheckIcon,
                    url: "/licensing_approvals",
                },
                {
                    title: "Certificates",
                    icon: AwardIcon,
                    url: "/licensing_certificate",
                }
            ],
        },
        {
            title: "TEP",
            url: "#",
            icon: FileTextIcon,
            items: [
                {
                    title: "TEP Requests",
                    icon: FileTextIcon,
                    url: "/tep_request",
                },
                {
                    title: "TEP Approvals",
                    icon: TicketCheckIcon,
                    url: "/tep_approvals",
                },
                {
                    title: "Certificates",
                    icon: AwardIcon,
                    url: "/tep_certificate",
                }
            ],
        },
        {
            title: "NGO Card",
            url: "#",
            icon: CreditCardIcon,
            items: [
                {
                    title: "Search",
                    icon: DatabaseIcon,
                    url: "/ngo_card",
                },
            ],
        },
        {
            title: "Case Management",
            url: "#",
            icon: ScaleIcon,
            items: [
                {
                    title: "Complaints",
                    icon: FileTextIcon,
                    url: "/complaint_lodge",
                }
            ],
        },
        {
            title: "Activities",
            url: "#",
            icon: CalendarIcon,
            items: [
                {
                    title: "Activities",
                    icon: CalendarIcon,
                    url: "/activity",
                },
            ],
        },
        {
            title: "Finance",
            url: "#",
            icon: BriefcaseIcon,
            items: [
                {
                    title: "Payments",
                    icon: BriefcaseIcon,
                    url: "/payment",
                },
                {
                    title: "Approval Requests",
                    icon: FileTextIcon,
                    url: "/payment_request",
                },
                {
                    title: "Invoices",
                    icon: FileTextIcon,
                    url: "/invoice",
                },
                {
                    title: "Fees",
                    icon: BriefcaseIcon,
                    url: "/finance/fees",
                },
            ],
        },
        {
            title: "Projects",
            url: "#",
            icon: CompassIcon,
            items: [
                {
                    title: "Project Creation",
                    icon: CompassIcon,
                    url: "/project",
                },
                {
                    title: "Data Capture",
                    icon: FileTextIcon,
                    url: "/project_data",
                },
            ],
        },
        {
            title: "Reports",
            url: "/report",
            icon: FileTextIcon,
            items: [],
        },
        {
            title: "Settings",
            url: "#",
            icon: Settings2,
            items: [
                {
                    title: "General",
                    url: "#",
                },
            ],
        },
    ],
    adminLinks: [
        {
            name: "Users",
            url: "/management/users",
            icon: Users2Icon,
        },
        {
            name: "Roles",
            url: "/management/roles",
            icon: GroupIcon,
        },
        {
            name: "Notices",
            url: "/management/notices",
            icon: FileTextIcon,
        },
        {
            name: "Districts",
            url: "/management/districts",
            icon: DatabaseIcon,
        },
        {
            name: "Zones",
            url: "/management/zones",
            icon: DatabaseIcon,
        },
        {
            name: "Regions",
            url: "/management/regions",
            icon: DatabaseIcon,
        },
        {
            name: "Audit Trail",
            url: "/management/audit_trail",
            icon: FileTextIcon,
        },
        {
            name: "System",
            url: "/management/system",
            icon: CogIcon,
        },
        {
            name: "Security",
            url: "/management/security",
            icon: Settings2,
        },
        {
            name: "NGO Settings",
            url: "/management/settings",
            icon: Building2Icon,
        },
        {
            name: "Complaint Categories",
            url: "/management/complaint_category",
            icon: ScaleIcon,
        },
        {
            name: "NGO Card Sharing",
            url: "/management/ngo_card_sharing",
            icon: CreditCardIcon,
        },
        {
            name: "Activity Categories",
            url: "/management/activity_category",
            icon: CalendarIcon,
        },
        {
            name: "Data Sharing",
            url: "/management/data_sharing",
            icon: DatabaseIcon,
        },
        {
            name: "Templates",
            url: "/management/template",
            icon: FileTextIcon,
        },
    ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
    const { session } = useAuth();

    data.accounts.push({
        name: `${session?.name}`,
        logo: GalleryVerticalEnd,
    });

    return (
        <Sidebar collapsible="icon" {...props}>
            <SidebarHeader>
                <AccountSwitcher accounts={data.accounts} />
            </SidebarHeader>
            <SidebarContent>
                <NavMain items={data.navMain} />
                <NavAdmin links={data.adminLinks} />
            </SidebarContent>
            {session ? (
                <SidebarFooter>
                    <NavUser session={session} />
                </SidebarFooter>
            ) : (
                ""
            )}
            <SidebarRail />
        </Sidebar>
    );
}
