"use client";

import React from "react";
import { 
  <PERSON><PERSON><PERSON>, 
  Eye, 
  Edit, 
  Trash2, 
  MoreHorizontal, 
  Users, 
  Clock,
  CheckCircle,
  XCircle
} from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { TemplateDto } from "@/types";

interface WorkflowCardProps {
  template: TemplateDto;
  onView?: (template: TemplateDto) => void;
  onEdit?: (template: TemplateDto) => void;
  onManageStages?: (template: TemplateDto) => void;
  onDelete?: (template: TemplateDto) => void;
}

export function WorkflowCard({
  template,
  onView,
  onEdit,
  onManageStages,
  onDelete,
}: WorkflowCardProps) {
  const getTemplateIcon = (code: string) => {
    switch (code) {
      case "ORGANIZATION_REGISTRATION":
        return "🏢";
      case "LICENCE_RENEWAL":
        return "📋";
      case "PERMIT_APPLICATION":
        return "📄";
      default:
        return "⚙️";
    }
  };

  const getStatusBadge = (isActive: boolean) => {
    return isActive ? (
      <Badge variant="default" className="flex items-center space-x-1">
        <CheckCircle className="h-3 w-3" />
        <span>Active</span>
      </Badge>
    ) : (
      <Badge variant="secondary" className="flex items-center space-x-1">
        <XCircle className="h-3 w-3" />
        <span>Inactive</span>
      </Badge>
    );
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <div className="text-2xl">{getTemplateIcon(template.code)}</div>
            <div className="flex-1">
              <CardTitle className="text-lg line-clamp-1">{template.name}</CardTitle>
              <CardDescription className="text-sm line-clamp-2">
                {template.description || "No description provided"}
              </CardDescription>
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onView && (
                <DropdownMenuItem onClick={() => onView(template)}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </DropdownMenuItem>
              )}
              {onEdit && (
                <DropdownMenuItem onClick={() => onEdit(template)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Template
                </DropdownMenuItem>
              )}
              {onManageStages && (
                <DropdownMenuItem onClick={() => onManageStages(template)}>
                  <Settings className="h-4 w-4 mr-2" />
                  Manage Stages
                </DropdownMenuItem>
              )}
              {onDelete && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={() => onDelete(template)}
                    className="text-destructive"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Badge variant="outline" className="text-xs">
              {template.code}
            </Badge>
            {getStatusBadge(template.is_active)}
          </div>
          
          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
            <div className="flex items-center space-x-1">
              <Clock className="h-3 w-3" />
              <span>{template.stages.length} stages</span>
            </div>
            <div className="flex items-center space-x-1">
              <Users className="h-3 w-3" />
              <span>
                {template.stages.reduce((acc, stage) => acc + stage.roles.length, 0)} roles
              </span>
            </div>
          </div>

          {onManageStages && (
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              onClick={() => onManageStages(template)}
            >
              <Settings className="h-3 w-3 mr-2" />
              Manage Stages
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
