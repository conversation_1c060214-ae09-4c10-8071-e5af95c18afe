"use client";

import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { Loader2, X, Plus, Trash2 } from "lucide-react";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/badge";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

import * as roleService from "@/services/RoleService";
import * as settingsService from "@/services/SettingsService";
import { TemplateStageDto, StageFormData, ActionMode, RoleDto, LoadableItemDto } from "@/types";

const stageSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters"),
  description: z.string().optional(),
  position: z.number().min(1, "Position must be at least 1"),
  selectedRoles: z.array(z.string()).min(1, "At least one role must be selected"),
  selectedTriggers: z.array(z.object({
    trigger_id: z.string(),
    action_mode: z.enum(["BEFORE", "AFTER"]),
  })),
});

type StageFormData = z.infer<typeof stageSchema>;

interface StageConfigurationModalProps {
  isOpen: boolean;
  onClose: () => void;
  stage: TemplateStageDto | null;
  templateId: string;
  nextPosition: number;
  onSubmit: (data: StageFormData) => Promise<void>;
}

export function StageConfigurationModal({
  isOpen,
  onClose,
  stage,
  templateId,
  nextPosition,
  onSubmit,
}: StageConfigurationModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<StageFormData>({
    resolver: zodResolver(stageSchema),
    defaultValues: {
      name: "",
      description: "",
      position: nextPosition,
      selectedRoles: [],
      selectedTriggers: [],
    },
  });

  // Fetch available roles
  const {
    data: roles = [],
    isLoading: rolesLoading,
  } = useQuery({
    queryKey: ["roles"],
    queryFn: async () => {
      const response = await roleService.fetchAll({ page: 1, size: 100 });
      
      if (response.errors && response.errors.length > 0) {
        for (const error of response.errors) {
          toast.error(error.message);
        }
        return [];
      }
      
      return response.data || [];
    },
  });

  // Fetch available triggers (functions)
  const {
    data: triggers = [],
    isLoading: triggersLoading,
  } = useQuery({
    queryKey: ["triggers"],
    queryFn: async () => {
      const response = await settingsService.fetchLoadableItems({ 
        type: "FUNCTION",
        page: 1, 
        size: 100 
      });
      
      if (response.errors && response.errors.length > 0) {
        for (const error of response.errors) {
          toast.error(error.message);
        }
        return [];
      }
      
      return response.data || [];
    },
  });

  // Update form when stage changes
  useEffect(() => {
    if (stage) {
      form.reset({
        name: stage.name,
        description: stage.description || "",
        position: stage.position,
        selectedRoles: stage.roles.map(r => r.role_id),
        selectedTriggers: stage.triggers.map(t => ({
          trigger_id: t.trigger_id,
          action_mode: t.action_mode,
        })),
      });
    } else {
      form.reset({
        name: "",
        description: "",
        position: nextPosition,
        selectedRoles: [],
        selectedTriggers: [],
      });
    }
  }, [stage, nextPosition, form]);

  const handleSubmit = async (data: StageFormData) => {
    setIsSubmitting(true);
    try {
      await onSubmit(data);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      form.reset();
      onClose();
    }
  };

  const addTrigger = () => {
    const currentTriggers = form.getValues("selectedTriggers");
    form.setValue("selectedTriggers", [
      ...currentTriggers,
      { trigger_id: "", action_mode: "BEFORE" as ActionMode },
    ]);
  };

  const removeTrigger = (index: number) => {
    const currentTriggers = form.getValues("selectedTriggers");
    form.setValue("selectedTriggers", currentTriggers.filter((_, i) => i !== index));
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {stage ? "Edit Stage" : "Add New Stage"}
          </DialogTitle>
          <DialogDescription>
            Configure the stage details, assign roles, and set up triggers.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Basic Information</h3>
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Stage Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter stage name"
                          {...field}
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="position"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Position</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="1"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value))}
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter stage description"
                        {...field}
                        disabled={isSubmitting}
                        rows={3}
                      />
                    </FormControl>
                    <FormDescription>
                      Optional description of what happens in this stage
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Role Assignment */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Role Assignment</h3>
              <FormField
                control={form.control}
                name="selectedRoles"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Assigned Roles</FormLabel>
                    <FormDescription>
                      Select the roles that can approve this stage
                    </FormDescription>
                    <div className="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto border rounded-md p-3">
                      {rolesLoading ? (
                        <div className="col-span-2 text-center py-4">Loading roles...</div>
                      ) : (
                        roles.map((role) => (
                          <div key={role.role_id} className="flex items-center space-x-2">
                            <Checkbox
                              id={role.role_id}
                              checked={field.value.includes(role.role_id)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  field.onChange([...field.value, role.role_id]);
                                } else {
                                  field.onChange(field.value.filter(id => id !== role.role_id));
                                }
                              }}
                              disabled={isSubmitting}
                            />
                            <Label htmlFor={role.role_id} className="text-sm">
                              {role.name}
                            </Label>
                          </div>
                        ))
                      )}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Triggers */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Triggers</h3>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addTrigger}
                  disabled={isSubmitting}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Trigger
                </Button>
              </div>

              <FormField
                control={form.control}
                name="selectedTriggers"
                render={({ field }) => (
                  <FormItem>
                    <FormDescription>
                      Configure triggers that execute before or after this stage
                    </FormDescription>
                    <div className="space-y-3">
                      {field.value.map((trigger, index) => (
                        <Card key={index}>
                          <CardContent className="pt-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label>Trigger Function</Label>
                                <Select
                                  value={trigger.trigger_id}
                                  onValueChange={(value) => {
                                    const newTriggers = [...field.value];
                                    newTriggers[index].trigger_id = value;
                                    field.onChange(newTriggers);
                                  }}
                                  disabled={isSubmitting || triggersLoading}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select trigger" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {triggers.map((triggerOption) => (
                                      <SelectItem key={triggerOption.id} value={triggerOption.id}>
                                        {triggerOption.display_value}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>
                              <div>
                                <Label>Action Mode</Label>
                                <div className="flex items-center space-x-2">
                                  <Select
                                    value={trigger.action_mode}
                                    onValueChange={(value: ActionMode) => {
                                      const newTriggers = [...field.value];
                                      newTriggers[index].action_mode = value;
                                      field.onChange(newTriggers);
                                    }}
                                    disabled={isSubmitting}
                                  >
                                    <SelectTrigger>
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="BEFORE">Before</SelectItem>
                                      <SelectItem value="AFTER">After</SelectItem>
                                    </SelectContent>
                                  </Select>
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => removeTrigger(index)}
                                    disabled={isSubmitting}
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                      {field.value.length === 0 && (
                        <div className="text-center py-8 text-muted-foreground">
                          No triggers configured. Click "Add Trigger" to add one.
                        </div>
                      )}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {stage ? "Update Stage" : "Create Stage"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
