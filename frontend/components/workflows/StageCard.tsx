"use client";

import React from "react";
import { 
  <PERSON>ripVertical,
  Edit,
  Trash2,
  <PERSON>,
  Zap,
  CheckCircle,
  XCircle
} from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

import { TemplateStageDto } from "@/types";

interface StageCardProps {
  stage: TemplateStageDto;
  isDragging?: boolean;
  dragHandleProps?: any;
  onEdit?: (stage: TemplateStageDto) => void;
  onDelete?: (stage: TemplateStageDto) => void;
  showDragHandle?: boolean;
}

export function StageCard({
  stage,
  isDragging = false,
  dragHandleProps,
  onEdit,
  onDelete,
  showDragHandle = true,
}: StageCardProps) {
  const getStatusBadge = (isActive: boolean) => {
    return isActive ? (
      <Badge variant="default" className="text-xs flex items-center space-x-1">
        <CheckCircle className="h-2 w-2" />
        <span>Active</span>
      </Badge>
    ) : (
      <Badge variant="secondary" className="text-xs flex items-center space-x-1">
        <XCircle className="h-2 w-2" />
        <span>Inactive</span>
      </Badge>
    );
  };

  return (
    <Card className={`${isDragging ? "shadow-lg" : ""} transition-shadow`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {showDragHandle && (
              <div
                {...dragHandleProps}
                className="cursor-grab active:cursor-grabbing"
              >
                <GripVertical className="h-5 w-5 text-muted-foreground" />
              </div>
            )}
            <div>
              <CardTitle className="text-lg">
                {stage.position}. {stage.name}
              </CardTitle>
              <CardDescription>
                {stage.description || "No description provided"}
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {onEdit && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onEdit(stage)}
              >
                <Edit className="h-4 w-4" />
              </Button>
            )}
            {onDelete && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDelete(stage)}
                className="text-destructive hover:text-destructive"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            {getStatusBadge(stage.is_active)}
          </div>
          
          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
            <div className="flex items-center space-x-1">
              <Users className="h-3 w-3" />
              <span>{stage.roles.length} roles</span>
            </div>
            <div className="flex items-center space-x-1">
              <Zap className="h-3 w-3" />
              <span>{stage.triggers.length} triggers</span>
            </div>
          </div>

          {/* Role Details */}
          {stage.roles.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Assigned Roles:</h4>
              <div className="flex flex-wrap gap-1">
                {stage.roles.slice(0, 3).map((roleAssignment) => (
                  <Badge key={roleAssignment.id} variant="outline" className="text-xs">
                    {roleAssignment.role?.name || "Unknown Role"}
                  </Badge>
                ))}
                {stage.roles.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{stage.roles.length - 3} more
                  </Badge>
                )}
              </div>
            </div>
          )}

          {/* Trigger Details */}
          {stage.triggers.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Triggers:</h4>
              <div className="flex flex-wrap gap-1">
                {stage.triggers.slice(0, 2).map((trigger) => (
                  <Badge key={trigger.id} variant="secondary" className="text-xs">
                    {trigger.action_mode}: {trigger.trigger || "Unknown"}
                  </Badge>
                ))}
                {stage.triggers.length > 2 && (
                  <Badge variant="secondary" className="text-xs">
                    +{stage.triggers.length - 2} more
                  </Badge>
                )}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
