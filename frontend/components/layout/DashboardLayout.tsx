// =============================================================================
// DASHBOARD LAYOUT COMPONENT
// =============================================================================
import {
    ArrowRightIcon,
    Building2Icon,
    ChartBarIcon,
    CogIcon,
    FileTextIcon,
    HomeIcon,
    UserCircleIcon,
    UsersIcon,
} from "lucide-react";
import { useState } from "react";

import { NavBar } from "../ui.old/NavBar";
import { Button } from "../ui/button";

interface DashboardLayoutProps {
    children: React.ReactNode;
    currentUser?: {
        name: string;
        email: string;
        avatar?: string;
    };
    onLogout?: () => void;
    currentPath?: string;
}

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({
    children,
    currentUser,
    onLogout,
    currentPath = "",
}) => {
    const [sidebarOpen, setSidebarOpen] = useState(false);

    const navigation = [
        {
            label: "Dashboard",
            href: "/dashboard",
            icon: HomeIcon,
            current: currentPath === "/dashboard",
        },
        {
            label: "Users",
            href: "/users",
            icon: UsersIcon,
            current: currentPath.startsWith("/users"),
        },
        {
            label: "Organizations",
            href: "/organizations",
            icon: Building2Icon,
            current: currentPath.startsWith("/organizations"),
        },
        {
            label: "Applications",
            href: "/applications",
            icon: FileTextIcon,
            current: currentPath.startsWith("/applications"),
            badge: 3,
        },
        {
            label: "Reports",
            href: "/reports",
            icon: ChartBarIcon,
            current: currentPath.startsWith("/reports"),
        },
        {
            label: "Settings",
            href: "/settings",
            icon: CogIcon,
            current: currentPath.startsWith("/settings"),
        },
    ];

    const userMenuItems = [
        { label: "Your Profile", onClick: () => {}, icon: UserCircleIcon },
        { label: "Settings", onClick: () => {}, icon: CogIcon },
        {
            label: "Sign out",
            onClick: onLogout || (() => {}),
            icon: ArrowRightIcon,
        },
    ];

    return (
        <div className="min-h-screen bg-gray-50">
            <NavBar
                brandName="Ngora"
                navigation={navigation}
                notificationCount={5}
                userAvatar={currentUser?.avatar}
                userMenuItems={userMenuItems}
                userName={currentUser?.name || "User"}
                onMenuClick={() => setSidebarOpen(!sidebarOpen)}
            />

            {/* Mobile sidebar */}
            {sidebarOpen && (
                <div className="fixed inset-0 z-40 md:hidden">
                    <Button
                        className="fixed inset-0 bg-gray-600 bg-opacity-75"
                        variant="ghost"
                        onClick={() => setSidebarOpen(false)}
                    />
                    <div className="relative flex-1 flex flex-col max-w-xs w-full bg-white">
                        <div className="flex-1 h-0 pt-5 pb-4 overflow-y-auto">
                            <nav className="mt-5 px-2 space-y-1">
                                {navigation.map((item, index) => (
                                    <a
                                        key={index}
                                        className={`group flex items-center px-2 py-2 text-base font-medium rounded-md ${
                                            item.current
                                                ? "bg-blue-100 text-blue-900"
                                                : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                                        }`}
                                        href={item.href}
                                    >
                                        <item.icon className="mr-4 h-6 w-6" />
                                        {item.label}
                                        {item.badge && (
                                            <span className="ml-auto bg-red-100 text-red-600 text-xs rounded-full px-2 py-1">
                                                {item.badge}
                                            </span>
                                        )}
                                    </a>
                                ))}
                            </nav>
                        </div>
                    </div>
                </div>
            )}

            {/* Desktop sidebar */}
            <div className="hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0 md:pt-16">
                <div className="flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white">
                    <div className="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
                        <nav className="mt-5 flex-1 px-2 space-y-1">
                            {navigation.map((item, index) => (
                                <a
                                    key={index}
                                    className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                                        item.current
                                            ? "bg-blue-100 text-blue-900"
                                            : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                                    }`}
                                    href={item.href}
                                >
                                    <item.icon className="mr-3 h-5 w-5" />
                                    {item.label}
                                    {item.badge && (
                                        <span className="ml-auto bg-red-100 text-red-600 text-xs rounded-full px-2 py-1">
                                            {item.badge}
                                        </span>
                                    )}
                                </a>
                            ))}
                        </nav>
                    </div>
                </div>
            </div>

            {/* Main content */}
            <div className="md:pl-64 flex flex-col flex-1">
                <main className="flex-1">
                    <div className="py-6">
                        <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
                            {children}
                        </div>
                    </div>
                </main>
            </div>
        </div>
    );
};
