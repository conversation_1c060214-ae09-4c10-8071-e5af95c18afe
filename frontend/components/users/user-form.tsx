import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import { z } from "zod";

import { UserFormData, userSchema } from "./user-schema";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import * as RoleService from "@/services/RoleService";
import * as UserService from "@/services/UserService";
import { RoleDto, UserDto } from "@/types";

interface UserFormProps {
    onClose: () => void;
    onAdd: (user: UserDto) => void;
    onUpdate: (user: UserDto) => void;
    user: UserDto | null;
    formMode: "create" | "edit";
}

export default function UserForm({ onClose, onAdd, user, formMode, onUpdate }: UserFormProps) {
    const [formData, setFormData] = useState<UserFormData>({
        first_name: "",
        last_name: "",
        middle_name: "",
        username: "",
        email: "",
        phone: "",
        password: "",
        role_id: "",
        department_id: "",
        is_external: false,
        gender: "NONE",
    });

    const [errors, setErrors] = useState<Record<string, string>>({});
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [roles, setRoles] = useState<RoleDto[]>([]);
    const debounceTimeout = useRef<NodeJS.Timeout | null>(null);

    useEffect(() => {
        if (user) {
            setFormData((prev) => ({
                ...prev,
                ...user,
                password: "temporaryPass",
                confirm_password: "temporaryPass",
            }));
        }
    }, [user]);

    const handleRoleInputChange = (value: string) => {
        if (debounceTimeout.current) {
            clearTimeout(debounceTimeout.current);
        }

        debounceTimeout.current = setTimeout(async () => {
            try {
                const results = await RoleService.fetchAll({ name: value });

                if (results.data) {
                    setRoles(results.data);
                }
            } catch (error) {
                console.error("Error fetching roles:", error);
            }
        }, 300);
    };

    const validateForm = () => {
        try {
            userSchema.parse(formData);
            setErrors({});

            return true;
        } catch (error) {
            if (error instanceof z.ZodError) {
                const formattedErrors: Record<string, string> = {};

                error.errors.forEach((err) => {
                    if (err.path[0]) {
                        formattedErrors[err.path[0].toString()] = err.message;
                    }
                });
                setErrors(formattedErrors);
            }

            return false;
        }
    };

    const handleChange = (field: keyof UserFormData, value: string) => {
        if (isSubmitting) validateForm();
        setFormData((prev) => ({ ...prev, [field]: value }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);

        if (!validateForm()) return;

        try {
            setIsLoading(true);
            const response =
                formMode === "create" ? await UserService.createUser(formData) : await UserService.updateUser(formData);

            if (response.success && response.data) {
                onClose();
            } else {
                toast.error(response.errors?.[0]?.message || "Failed to process request");
            }
        } catch (error) {
            toast.error("An error occurred while processing user");
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <form className="space-y-4" onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                    error={errors.first_name}
                    label="First Name"
                    value={formData.first_name}
                    onChange={(e) => handleChange("first_name", e.target.value)}
                />
                <Input
                    error={errors.last_name}
                    label="Last Name"
                    value={formData.last_name}
                    onChange={(e) => handleChange("last_name", e.target.value)}
                />
            </div>

            <Input
                error={errors.middle_name}
                label="Middle Name"
                value={formData.middle_name || ""}
                onChange={(e) => handleChange("middle_name", e.target.value)}
            />

            <Input
                error={errors.username}
                label="Username"
                value={formData.username}
                onChange={(e) => handleChange("username", e.target.value)}
            />

            <Input
                error={errors.email}
                label="Email"
                type="email"
                value={formData.email}
                onChange={(e) => handleChange("email", e.target.value)}
            />

            <Input
                error={errors.phone}
                label="Phone"
                type="tel"
                value={formData.phone || ""}
                onChange={(e) => handleChange("phone", e.target.value)}
            />

            {formMode === "create" && (
                <>
                    <Input
                        error={errors.password}
                        label="Password"
                        type="password"
                        value={formData.password || ""}
                        onChange={(e) => handleChange("password", e.target.value)}
                    />
                </>
            )}

            {/* Role input as select (customize or replace with Combobox later) */}
            <div>
                <label className="block text-sm font-medium" htmlFor="role_id">
                    Role
                </label>
                <select
                    className="mt-1 block w-full border rounded-md shadow-sm text-sm focus:ring focus:ring-ring/30"
                    id="role_id"
                    value={formData.role_id || ""}
                    onChange={(e) => handleChange("role_id", e.target.value)}
                >
                    <option value="">Select a role</option>
                    {roles.map((role) => (
                        <option key={role.role_id} value={role.role_id}>
                            {role.name}
                        </option>
                    ))}
                </select>
                {errors.role_id && <p className="text-sm text-red-500 mt-1">{errors.role_id}</p>}
            </div>

            <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={onClose}>
                    Cancel
                </Button>
                <Button loading={isLoading} type="submit">
                    {formMode === "create" ? "Create User" : "Update User"}
                </Button>
            </div>
        </form>
    );
}
