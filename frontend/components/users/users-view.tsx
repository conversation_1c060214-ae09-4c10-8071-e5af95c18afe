import { ColumnDef } from "@tanstack/react-table";
import { EllipsisVertical, Grid3X3, Mail, Pen, Phone, Table as TableIcon, Trash2, User } from "lucide-react";
import { useMemo, useState } from "react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { DataTable } from "@/components/ui/data-table";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Skeleton } from "@/components/ui/skeleton";
import { UserDto } from "@/types";

type ViewMode = "table" | "card";

type UsersViewProps = {
    users: UserDto[];
    onEdit: (user: UserDto) => void;
    onDelete: (user: UserDto) => void;
    isLoading: boolean;
};

export default function UsersView({ users, isLoading, onEdit, onDelete }: UsersViewProps) {
    const [viewMode, setViewMode] = useState<ViewMode>("table");

    const getStatusBadge = (status: string) => {
        const statusConfig = {
            active: { variant: "default" as const, className: "bg-green-100 text-green-800 hover:bg-green-200" },
            inactive: { variant: "secondary" as const, className: "bg-gray-100 text-gray-800" },
            pending: { variant: "outline" as const, className: "bg-yellow-100 text-yellow-800 border-yellow-300" },
            suspended: { variant: "destructive" as const, className: "bg-red-100 text-red-800 hover:bg-red-200" },
        };

        const config = statusConfig[status?.toLowerCase() as keyof typeof statusConfig] || statusConfig.inactive;

        return (
            <Badge className={config.className} variant={config.variant}>
                {status || "Unknown"}
            </Badge>
        );
    };

    const formatName = (user: UserDto) => {
        const parts = [user.first_name, user.middle_name, user.last_name].filter(Boolean);

        return parts.join(" ") || "N/A";
    };

    const formatPhone = (phone: string | null) => {
        return phone || "N/A";
    };

    const getInitials = (user: UserDto) => {
        const firstName = user.first_name?.charAt(0) || "";
        const lastName = user.last_name?.charAt(0) || "";

        return (firstName + lastName).toUpperCase() || "U";
    };

    const columns: ColumnDef<UserDto>[] = useMemo(
        () => [
            {
                accessorKey: "user_info",
                header: "User",
                cell: ({ row }) => {
                    const user = row.original;

                    return (
                        <div className="flex items-center space-x-3">
                            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-semibold">
                                {getInitials(user)}
                            </div>
                            <div>
                                <div className="font-medium">{formatName(user)}</div>
                                <div className="text-sm text-muted-foreground">@{user.handle}</div>
                            </div>
                        </div>
                    );
                },
            },
            {
                accessorKey: "email",
                header: "Email",
                cell: ({ row }) => (
                    <div className="flex items-center space-x-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <span>{row.getValue("email")}</span>
                    </div>
                ),
            },
            {
                accessorKey: "phone",
                header: "Phone",
                cell: ({ row }) => (
                    <div className="flex items-center space-x-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span>{formatPhone(row.getValue("phone"))}</span>
                    </div>
                ),
            },
            {
                accessorKey: "role",
                header: "Role",
                cell: ({ row }) => {
                    const role = row.original.role;

                    return (
                        <Badge className="font-normal" variant="outline">
                            {role?.name || "No Role"}
                        </Badge>
                    );
                },
            },
            {
                accessorKey: "status",
                header: "Status",
                cell: ({ row }) => getStatusBadge(row.getValue("status")),
            },
            {
                id: "actions",
                header: () => <div className="text-right">Actions</div>,
                cell: ({ row }) => {
                    const user = row.original;

                    return (
                        <div className="text-right">
                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <Button className="h-8 w-8 p-0" variant="ghost">
                                        <EllipsisVertical className="h-4 w-4" />
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end" className="w-56">
                                    <DropdownMenuItem className="cursor-pointer" onClick={() => onEdit(user)}>
                                        <Pen className="mr-2 h-4 w-4" />
                                        Edit user
                                    </DropdownMenuItem>
                                    <DropdownMenuItem
                                        className="cursor-pointer text-destructive focus:text-destructive"
                                        onClick={() => onDelete(user)}
                                    >
                                        <Trash2 className="mr-2 h-4 w-4" />
                                        Delete user
                                    </DropdownMenuItem>
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </div>
                    );
                },
            },
        ],
        [onEdit, onDelete],
    );

    const ViewToggle = () => (
        <div className="flex items-center space-x-1 bg-muted p-1 rounded-lg">
            <Button
                className="h-8 px-3"
                size="sm"
                variant={viewMode === "table" ? "default" : "ghost"}
                onClick={() => setViewMode("table")}
            >
                <TableIcon className="h-4 w-4 mr-2" />
                Table
            </Button>
            <Button
                className="h-8 px-3"
                size="sm"
                variant={viewMode === "card" ? "default" : "ghost"}
                onClick={() => setViewMode("card")}
            >
                <Grid3X3 className="h-4 w-4 mr-2" />
                Cards
            </Button>
        </div>
    );

    const LoadingCardSkeleton = () => (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {Array.from({ length: 8 }).map((_, index) => (
                <Card key={index} className="hover:shadow-md transition-shadow">
                    <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                                <Skeleton className="h-12 w-12 rounded-full" />
                                <div className="space-y-2">
                                    <Skeleton className="h-4 w-24" />
                                    <Skeleton className="h-3 w-16" />
                                </div>
                            </div>
                            <Skeleton className="h-8 w-8 rounded" />
                        </div>
                    </CardHeader>
                    <CardContent className="space-y-3">
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-4 w-3/4" />
                        <div className="flex justify-between items-center">
                            <Skeleton className="h-6 w-16 rounded-full" />
                            <Skeleton className="h-6 w-20 rounded-full" />
                        </div>
                    </CardContent>
                </Card>
            ))}
        </div>
    );

    const UserCard = ({ user }: { user: UserDto }) => (
        <Card className="hover:shadow-md transition-all duration-200 hover:-translate-y-1">
            <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                        <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-semibold text-lg">
                            {getInitials(user)}
                        </div>
                        <div>
                            <h3 className="font-semibold text-lg leading-none">{formatName(user)}</h3>
                            <p className="text-sm text-muted-foreground mt-1">ID: {user.id}</p>
                        </div>
                    </div>
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button className="h-8 w-8 p-0" variant="ghost">
                                <EllipsisVertical className="h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-56">
                            <DropdownMenuItem className="cursor-pointer" onClick={() => onEdit(user)}>
                                <Pen className="mr-2 h-4 w-4" />
                                Edit user
                            </DropdownMenuItem>
                            <DropdownMenuItem
                                className="cursor-pointer text-destructive focus:text-destructive"
                                onClick={() => onDelete(user)}
                            >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete user
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </CardHeader>
            <CardContent className="space-y-4">
                <div className="space-y-3">
                    <div className="flex items-center space-x-2 text-sm">
                        <Mail className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                        <span className="truncate">{user.email}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm">
                        <Phone className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                        <span>{formatPhone(user.phone)}</span>
                    </div>
                </div>

                <div className="flex items-center justify-between pt-2 border-t">
                    <div className="flex flex-col space-y-1">
                        <span className="text-xs text-muted-foreground">Role</span>
                        <Badge className="text-xs w-fit" variant="outline">
                            {user.role?.name || "No Role"}
                        </Badge>
                    </div>
                    <div className="flex flex-col space-y-1 items-end">
                        <span className="text-xs text-muted-foreground">Status</span>
                        {getStatusBadge(user.account_status)}
                    </div>
                </div>
            </CardContent>
        </Card>
    );

    const CardView = () => (
        <div className="space-y-4">
            {isLoading ? (
                <LoadingCardSkeleton />
            ) : users.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-12 text-muted-foreground">
                    <User className="h-12 w-12 mb-4" />
                    <p className="text-lg font-medium">No users found</p>
                    <p className="text-sm">Try adjusting your search criteria</p>
                </div>
            ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    {users.map((user) => (
                        <UserCard key={user.id} user={user} />
                    ))}
                </div>
            )}
        </div>
    );

    if (isLoading && viewMode === "table") {
        return (
            <div className="space-y-4">
                <div className="flex items-center justify-between">
                    <div className="space-y-1">
                        <h2 className="text-2xl font-semibold tracking-tight">Users</h2>
                        <p className="text-sm text-muted-foreground">Manage your team members and their permissions</p>
                    </div>
                    <ViewToggle />
                </div>
                <DataTable
                    columns={columns}
                    data={[]}
                    enableFiltering={true}
                    enablePagination={true}
                    enableSorting={true}
                    pageSize={10}
                    searchKey="email"
                    searchPlaceholder="Search users by email..."
                />
            </div>
        );
    }

    return (
        <div className="space-y-4">
            {/* Header with View Toggle */}
            <div className="flex items-center justify-between">
                <div className="space-y-1">
                    <h2 className="text-2xl font-semibold tracking-tight">Users</h2>
                    <p className="text-sm text-muted-foreground">Manage your team members and their permissions</p>
                </div>
                <ViewToggle />
            </div>

            {/* Content */}
            {viewMode === "table" ? (
                <DataTable
                    columns={columns}
                    data={users}
                    enableFiltering={true}
                    enablePagination={true}
                    enableSorting={true}
                    pageSize={10}
                    searchKey="email"
                    searchPlaceholder="Search users by email..."
                />
            ) : (
                <CardView />
            )}
        </div>
    );
}
