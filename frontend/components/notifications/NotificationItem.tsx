// =============================================================================
// NOTIFICATION COMPONENT
// =============================================================================

import { InformationCircleIcon, ExclamationTriangleIcon, ExclamationCircleIcon } from "@heroicons/react/24/outline";
import { CheckCircleIcon } from "lucide-react";

interface Notification {
    id: string;
    title: string;
    message: string;
    type: 'info' | 'success' | 'warning' | 'error';
    read: boolean;
    created_at: string;
    action_url?: string;
  }
  
  interface NotificationItemProps {
    notification: Notification;
    onRead?: (id: string) => void;
    onDismiss?: (id: string) => void;
    onClick?: (notification: Notification) => void;
  }
  
  export const NotificationItem: React.FC<NotificationItemProps> = ({
    notification,
    onRead,
    onDismiss,
    onClick,
  }) => {
    const typeIcons = {
      info: InformationCircleIcon,
      success: CheckCircleIcon,
      warning: ExclamationTriangleIcon,
      error: ExclamationCircleIcon,
    };
  
    const typeColors = {
      info: 'text-blue-500',
      success: 'text-green-500',
      warning: 'text-yellow-500',
      error: 'text-red-500',
    };
  
    const Icon = typeIcons[notification.type];
  
    return (
      <div 
        className={`p-4 border-l-4 cursor-pointer hover:bg-gray-50 transition-colors ${
          notification.read ? 'bg-white border-gray-200' : 'bg-blue-50 border-blue-500'
        }`}
        onClick={() => onClick?.(notification)}
      >
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <Icon className={`h-5 w-5 ${typeColors[notification.type]}`} />
          </div>
          <div className="ml-3 flex-1">
            <div className="flex items-start justify-between">
              <div>
                <h4 className={`text-sm font-medium ${
                  notification.read ? 'text-gray-900' : 'text-gray-900 font-semibold'
                }`}>
                  {notification.title}
                </h4>
                <p className="mt-1 text-sm text-gray-600">{notification.message}</p>
                <p className="mt-1 text-xs text-gray-400">
                  {new Date(notification.created_at).toLocaleString()}
                </p>
              </div>
              <div className="flex space-x-2 ml-4">
                {!notification.read && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onRead?.(notification.id);
                    }}
                    className="text-xs text-blue-600 hover:text-blue-800"
                  >
                    Mark as read
                  </button>
                )}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onDismiss?.(notification.id);
                  }}
                  className="text-xs text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };