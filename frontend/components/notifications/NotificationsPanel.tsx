// =============================================================================
// NOTIFICATIONS PANEL
// =============================================================================

import { BellIcon } from "lucide-react";

import { EmptyState } from "../forms/EmptyState";
import { LoadingSkeleton } from "../forms/LoadingSkeleton";
import { Button } from "../ui.old/Button";
import { Card } from "../ui.old/Card";

import { NotificationItem } from "./NotificationItem";

type Notification = {
    id: string;
    title: string;
    message: string;
    type: "info" | "success" | "warning" | "error";
    read: boolean;
    created_at: string;
    // Add any other fields your NotificationItem expects
};

interface NotificationsPanelProps {
    notifications?: Notification[];
    loading?: boolean;
    onMarkAsRead?: (id: string) => void;
    onMarkAllAsRead?: () => void;
    onDismiss?: (id: string) => void;
    onNotificationClick?: (notification: Notification) => void;
}

export const NotificationsPanel: React.FC<NotificationsPanelProps> = ({
    notifications = [],
    loading = false,
    onMarkAsRead,
    onMarkAllAsRead,
    onDismiss,
    onNotificationClick,
}) => {
    const unreadCount = notifications.filter((n) => !n.read).length;

    if (loading) {
        return (
            <Card>
                <LoadingSkeleton columns={1} rows={3} />
            </Card>
        );
    }

    return (
        <Card>
            <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                    Notifications
                    {unreadCount > 0 && (
                        <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            {unreadCount} new
                        </span>
                    )}
                </h3>
                {unreadCount > 0 && (
                    <Button size="sm" variant="ghost" onClick={onMarkAllAsRead}>
                        Mark all as read
                    </Button>
                )}
            </div>

            {notifications.length === 0 ? (
                <EmptyState
                    description="You're all caught up! No new notifications."
                    icon={BellIcon}
                    title="No notifications"
                />
            ) : (
                <div className="space-y-1 max-h-96 overflow-y-auto">
                    {notifications.map((notification) => (
                        <NotificationItem
                            key={notification.id}
                            notification={notification}
                            onClick={onNotificationClick}
                            onDismiss={onDismiss}
                            onRead={onMarkAsRead}
                        />
                    ))}
                </div>
            )}
        </Card>
    );
};
