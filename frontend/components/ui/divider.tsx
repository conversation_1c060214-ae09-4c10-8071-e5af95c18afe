import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

import { cn } from "@/lib/utils";

const dividerVariants = cva("relative flex items-center", {
    variants: {
        orientation: {
            horizontal: "w-full",
            vertical: "h-full flex-col",
        },
        spacing: {
            none: "",
            sm: "my-4",
            md: "my-6",
            lg: "my-8",
            xl: "my-12",
        },
        thickness: {
            thin: "[&_.divider-line]:border-t",
            medium: "[&_.divider-line]:border-t-2",
            thick: "[&_.divider-line]:border-t-4",
        },
    },
    defaultVariants: {
        orientation: "horizontal",
        spacing: "md",
        thickness: "thin",
    },
});

const dividerLineVariants = cva("divider-line flex-1 border-border", {
    variants: {
        orientation: {
            horizontal: "border-t",
            vertical: "border-l h-full",
        },
    },
    defaultVariants: {
        orientation: "horizontal",
    },
});

const dividerTextVariants = cva(
    "flex shrink-0 items-center justify-center bg-background text-muted-foreground text-sm font-medium",
    {
        variants: {
            orientation: {
                horizontal: "px-3",
                vertical: "py-3 writing-mode-vertical",
            },
        },
        defaultVariants: {
            orientation: "horizontal",
        },
    },
);

export interface DividerProps
    extends React.HTMLAttributes<HTMLDivElement>,
        VariantProps<typeof dividerVariants> {
    children?: React.ReactNode;
    label?: string;
    icon?: React.ReactNode;
    textClassName?: string;
    lineClassName?: string;
}

const Divider = React.forwardRef<HTMLDivElement, DividerProps>(
    (
        {
            className,
            orientation = "horizontal",
            spacing = "md",
            thickness = "thin",
            children,
            label,
            icon,
            textClassName,
            lineClassName,
            ...props
        },
        ref,
    ) => {
        const content = children || label;
        const isVertical = orientation === "vertical";

        if (!content && !icon) {
            // Simple line divider
            return (
                <div
                    ref={ref}
                    {...(orientation && { "aria-orientation": orientation })}
                    className={cn(
                        dividerVariants({ orientation, spacing }),
                        className,
                    )}
                    role="separator"
                    {...props}
                >
                    <div
                        className={cn(
                            dividerLineVariants({ orientation }),
                            thickness === "thin" && "border-t",
                            thickness === "medium" && "border-t-2",
                            thickness === "thick" && "border-t-4",
                            isVertical &&
                                thickness === "thin" &&
                                "border-l border-t-0",
                            isVertical &&
                                thickness === "medium" &&
                                "border-l-2 border-t-0",
                            isVertical &&
                                thickness === "thick" &&
                                "border-l-4 border-t-0",
                            lineClassName,
                        )}
                    />
                </div>
            );
        }

        return (
            <div
                ref={ref}
                {...(orientation && { "aria-orientation": orientation })}
                className={cn(
                    dividerVariants({ orientation, spacing }),
                    className,
                )}
                role="separator"
                {...props}
            >
                <div
                    className={cn(
                        dividerLineVariants({ orientation }),
                        thickness === "thin" && "border-t",
                        thickness === "medium" && "border-t-2",
                        thickness === "thick" && "border-t-4",
                        isVertical &&
                            thickness === "thin" &&
                            "border-l border-t-0",
                        isVertical &&
                            thickness === "medium" &&
                            "border-l-2 border-t-0",
                        isVertical &&
                            thickness === "thick" &&
                            "border-l-4 border-t-0",
                        lineClassName,
                    )}
                />

                <div
                    className={cn(
                        dividerTextVariants({ orientation }),
                        textClassName,
                    )}
                >
                    {icon && <span className="mr-2">{icon}</span>}
                    {content}
                </div>

                <div
                    className={cn(
                        dividerLineVariants({ orientation }),
                        thickness === "thin" && "border-t",
                        thickness === "medium" && "border-t-2",
                        thickness === "thick" && "border-t-4",
                        isVertical &&
                            thickness === "thin" &&
                            "border-l border-t-0",
                        isVertical &&
                            thickness === "medium" &&
                            "border-l-2 border-t-0",
                        isVertical &&
                            thickness === "thick" &&
                            "border-l-4 border-t-0",
                        lineClassName,
                    )}
                />
            </div>
        );
    },
);

Divider.displayName = "Divider";

export { Divider, dividerVariants };
