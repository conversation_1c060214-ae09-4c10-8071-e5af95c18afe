"use client";

import * as CheckboxPrimitive from "@radix-ui/react-checkbox";
import { CheckIcon } from "lucide-react";
import * as React from "react";

import { cn } from "@/lib/utils";

export interface CheckboxProps
    extends React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root> {
    label?: string;
    containerClassName?: string;
    labelClassName?: string;
}

const Checkbox = React.forwardRef<
    React.ElementRef<typeof CheckboxPrimitive.Root>,
    CheckboxProps
>(
    (
        { className, label, containerClassName, labelClassName, id, ...props },
        ref,
    ) => {
        // Generate a unique ID if none provided and we have a label
        const checkboxId =
            id ||
            (label
                ? `checkbox-${Math.random().toString(36).substr(2, 9)}`
                : undefined);

        const checkboxElement = (
            <CheckboxPrimitive.Root
                ref={ref}
                className={cn(
                    "peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",
                    className,
                )}
                id={checkboxId}
                {...props}
            >
                <CheckboxPrimitive.Indicator
                    className={cn(
                        "flex items-center justify-center text-current",
                    )}
                >
                    <CheckIcon className="h-4 w-4" />
                </CheckboxPrimitive.Indicator>
            </CheckboxPrimitive.Root>
        );

        if (label) {
            return (
                <div className={cn("flex items-center", containerClassName)}>
                    <label
                        className="relative flex items-center cursor-pointer select-none"
                        htmlFor={checkboxId}
                    >
                        {checkboxElement}
                        <span
                            className={cn(
                                "ml-2 block text-sm text-gray-700 dark:text-gray-300",
                                labelClassName,
                            )}
                        >
                            {label}
                        </span>
                    </label>
                </div>
            );
        }

        return checkboxElement;
    },
);

Checkbox.displayName = CheckboxPrimitive.Root.displayName;

export { Checkbox };
