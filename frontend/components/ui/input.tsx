import { EyeClosedIcon, EyeIcon } from "lucide-react";
import * as React from "react";
import { useState } from "react";

import { Button } from "./button";

import { cn } from "@/lib/utils";

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
    iconLeft?: React.ReactNode;
    iconRight?: React.ReactNode;
    label?: string;
    labelRight?: React.ReactNode;
    error?: string;
    containerClassName?: string;
    labelClassName?: string;
    labelContainerClassName?: string;
    errorClassName?: string;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
    (
        {
            className,
            type,
            iconLeft,
            iconRight,
            label,
            labelRight,
            error,
            containerClassName,
            labelClassName,
            labelContainerClassName,
            errorClassName,
            id,
            ...props
        },
        ref,
    ) => {
        const [passwordVisible, setPasswordVisible] = useState(false);

        const isPassword = type === "password";
        const inputType = isPassword && passwordVisible ? "text" : type;
        const hasError = Boolean(error);

        const togglePasswordVisibility = () => {
            setPasswordVisible((prev) => !prev);
        };

        // Generate a unique ID if none provided and we have a label
        const inputId = id || (label ? `input-${Math.random().toString(36).substr(2, 9)}` : undefined);

        return (
            <div className={cn("w-full", containerClassName)}>
                {/* Label */}
                {label && (
                    <div className={cn("flex items-center justify-between mb-1", labelContainerClassName)}>
                        <label className={cn("block text-sm font-medium", labelClassName)} htmlFor={inputId}>
                            {label}
                        </label>
                        {labelRight && <div className="text-sm">{labelRight}</div>}
                    </div>
                )}

                {/* Input Container */}
                <div className="relative w-full">
                    {/* Left Icon */}
                    {iconLeft && (
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-muted-foreground">
                            {iconLeft}
                        </div>
                    )}

                    <input
                        ref={ref}
                        aria-describedby={hasError && inputId ? `${inputId}-error` : undefined}
                        aria-invalid={hasError}
                        className={cn(
                            "border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
                            "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
                            "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive pl-3",
                            iconLeft && "pl-10",
                            (isPassword || iconRight) && "pr-10",
                            hasError && "border-red-300 focus-visible:border-red-500 focus-visible:ring-red-500/20",
                            className,
                        )}
                        id={inputId}
                        type={inputType}
                        {...props}
                    />

                    {/* Right Icon or Password Toggle */}
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                        {isPassword ? (
                            <Button
                                size="icon"
                                tabIndex={-1}
                                type="button"
                                variant="transparent"
                                onClick={togglePasswordVisibility}
                            >
                                {passwordVisible ? <EyeClosedIcon size={18} /> : <EyeIcon size={18} />}
                            </Button>
                        ) : (
                            iconRight && <div className="text-muted-foreground">{iconRight}</div>
                        )}
                    </div>
                </div>

                {/* Error Message */}
                {error && (
                    <p
                        className={cn("mt-1 text-sm text-red-600", errorClassName)}
                        id={inputId ? `${inputId}-error` : undefined}
                    >
                        {error}
                    </p>
                )}
            </div>
        );
    },
);

Input.displayName = "Input";
export { Input };
