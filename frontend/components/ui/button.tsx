import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { Loader2 } from "lucide-react";
import * as React from "react";

import { cn } from "@/lib/utils";

const buttonVariants = cva(
    "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
    {
        variants: {
            variant: {
                default:
                    "bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",
                destructive:
                    "bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",
                outline:
                    "border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground",
                secondary:
                    "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",
                ghost: "hover:bg-accent hover:text-accent-foreground",
                link: "text-primary underline-offset-4 hover:underline",
                transparent: "bg-transparent text-foreground hover:bg-muted/10",
            },
            size: {
                default: "h-9 px-4 py-2",
                sm: "h-8 rounded-md px-3",
                lg: "h-10 rounded-md px-6",
                icon: "size-9",
            },
        },
        defaultVariants: {
            variant: "default",
            size: "default",
        },
    },
);

export interface ButtonProps
    extends React.ComponentProps<"button">,
        VariantProps<typeof buttonVariants> {
    asChild?: boolean;
    loading?: boolean;
    loadingText?: string;
    iconLeft?: React.ReactNode;
    iconRight?: React.ReactNode;
    iconOnly?: boolean;
    loadingIcon?: React.ReactNode;
}

function Button({
    className,
    variant,
    size,
    asChild = false,
    loading = false,
    loadingText,
    iconLeft,
    iconRight,
    iconOnly = false,
    loadingIcon,
    disabled,
    children,
    ...props
}: ButtonProps) {
    const Comp = asChild ? Slot : "button";

    // Default loading icon
    const defaultLoadingIcon = <Loader2 className="animate-spin" />;
    const renderLoadingIcon = loadingIcon || defaultLoadingIcon;

    // Determine if button should be disabled
    const isDisabled = disabled || loading;

    // Handle icon-only buttons
    const isIconOnlyButton = iconOnly || size === "icon";

    // Content rendering logic
    const renderContent = () => {
        if (loading) {
            if (isIconOnlyButton) {
                return renderLoadingIcon;
            }

            return (
                <>
                    {renderLoadingIcon}
                    {loadingText || children}
                </>
            );
        }

        if (isIconOnlyButton) {
            return iconLeft || iconRight || children;
        }

        return (
            <>
                {iconLeft}
                {children}
                {iconRight}
            </>
        );
    };

    return (
        <Comp
            className={cn(
                buttonVariants({ variant, size }),
                loading && "cursor-not-allowed",
                className,
            )}
            data-slot="button"
            disabled={isDisabled}
            {...props}
        >
            {renderContent()}
        </Comp>
    );
}

export { Button, buttonVariants };
