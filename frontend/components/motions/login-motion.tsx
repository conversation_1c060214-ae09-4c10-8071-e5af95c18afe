import { BoxesIcon, Gavel, ShieldCheckIcon, Tv2Icon } from "lucide-react";
import { useEffect, useState } from "react";

import { Button } from "../ui/button";

export const LoginMotion = () => {
    const [currentSlide, setCurrentSlide] = useState(0);

    const slides = [
        {
            title: "NGORA's Mandate",
            description:
                "The Non-Governmental Organizations Regulatory Authority (NGORA) is a statutory body established by an Act of Parliament. As part of the Ministry of Gender, Community Development and Social Welfare, NGORA is responsible for registering and regulating all NGOs operating in Malawi.",
            icon: Gavel,
            imagePath:
                "https://images.unsplash.com/photo-*************-2afd69097998?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80",
            quote: {
                text: "myNGO system has transformed how we monitor and regulate organizations in Malawi. The platform's efficiency has greatly improved our regulatory processes.",
                author: "Mr. <PERSON>",
                position: "Board Chairperson, NGORA",
            },
        },
        {
            title: "Ensuring Compliance and Accountability",
            description:
                "NGORA enforces annual reporting requirements, mandating NGOs to submit their financial and operational reports within six months of the end of their financial year. By promoting compliance, NGORA ensures transparency and integrity across the non-governmental sector.",
            icon: ShieldCheckIcon,
            imagePath:
                "https://images.unsplash.com/photo-**********-6726b3ff858f?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80",
        },
        {
            title: "myNGO: A Streamlined Digital Platform",
            description:
                "myNGO is NGORA's official digital platform designed to simplify key NGO processes. Users can register their organization, renew licenses, apply for TEP processing certificates, and lodge complaints through our efficient, paperless system.",
            icon: Tv2Icon,
            imagePath:
                "https://images.unsplash.com/photo-**********-e076c223a692?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80",
        },
        {
            title: "NGORA vs. CONGOMA: Distinct Roles",
            description:
                "While NGORA serves as the official regulator overseeing legal compliance of NGOs, CONGOMA acts as a coordinating body that represents and networks NGOs. Understanding the distinction helps stakeholders engage with the appropriate institution for their specific needs.",
            icon: BoxesIcon,
            imagePath:
                "https://images.unsplash.com/photo-1491975474562-1f4e30bc9468?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80",
        },
    ];

    useEffect(() => {
        const interval = setInterval(() => {
            setCurrentSlide((prev) => (prev + 1) % slides.length);
        }, 8000);

        return () => clearInterval(interval);
    }, [slides.length]);

    return (
        <div>
            {slides.map((slide, index) => (
                <div
                    key={index}
                    aria-hidden={currentSlide !== index}
                    className={`absolute inset-0 transition-all duration-1000 ease-in-out ${
                        currentSlide === index ? "opacity-100 translate-x-0" : "opacity-0 translate-x-8"
                    }`}
                >
                    {/* Background with gradient overlay */}
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-900 via-blue-800 to-blue-900 opacity-90 z-10" />

                    {/* Background image */}
                    <div
                        className="absolute inset-0 bg-cover bg-center opacity-20 mix-blend-overlay z-0"
                        style={{
                            backgroundImage: `url(${slide.imagePath})`,
                        }}
                    />
                    {/* Decorative patterns */}
                    <div className="absolute inset-0 bg-blue-900 opacity-30 z-5">
                        <div className="absolute top-0 left-0 right-0 h-40 bg-gradient-to-b from-blue-800/20 to-transparent" />
                        <div className="absolute bottom-0 left-0 right-0 h-40 bg-gradient-to-t from-blue-800/20 to-transparent" />
                    </div>
                    {/* Content */}
                    <div className="absolute inset-0 flex items-center justify-center z-20">
                        <div className="max-w-xl text-white p-8 md:p-12">
                            <slide.icon className="h-12 w-12 mb-6" />
                            <h1 className="text-3xl md:text-4xl font-bold mb-6 text-white">{slide.title}</h1>
                            <p className="text-lg md:text-xl text-blue-100 leading-relaxed mb-6">{slide.description}</p>

                            {/* Testimonial - only show on the slide that has a quote */}
                            {slide.quote && (
                                <div className="bg-white/10 backdrop-blur-sm p-6 rounded-xl mt-8 border border-white/20">
                                    <svg
                                        className="w-10 h-10 text-blue-300/60 mb-4"
                                        fill="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path d="M14.017 21v-7.391C14.017 10.233 16.233 8 19.608 8c.297 0 .535.24.535.534v1.494c0 .294-.238.533-.534.533a2.565 2.565 0 00-2.565 2.565V14h4.064c.297 0 .534.238.534.534v1.932c0 .295-.237.533-.534.533h-4.064V21c0 .295-.238.533-.534.533h-1.957a.533.533 0 01-.534-.533zm-10 0v-7.391C4.017 10.233 6.233 8 9.608 8c.297 0 .535.24.535.534v1.494c0 .294-.238.533-.534.533A2.565 2.565 0 007.044 13.126V14h4.064c.297 0 .534.238.534.534v1.932c0 .295-.237.533-.534.533H7.044V21c0 .295-.238.533-.534.533H4.553a.533.533 0 01-.534-.533z" />
                                    </svg>
                                    <p className="italic text-white/90 mb-4 text-lg">{slide.quote.text}</p>
                                    <div className="flex items-center">
                                        <div className="w-10 h-10 rounded-full bg-blue-600 flex items-center justify-center text-white font-bold">
                                            {slide.quote.author
                                                .split(" ")
                                                .map((name) => name[0])
                                                .join("")}
                                        </div>
                                        <div className="ml-3">
                                            <p className="font-medium text-white">{slide.quote.author}</p>
                                            <p className="text-sm text-blue-200">{slide.quote.position}</p>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            ))}

            {/* Enhanced slideshow navigation */}
            <div className="absolute bottom-10 left-0 right-0 flex justify-center z-30">
                <div className="flex items-center bg-blue-950/30 backdrop-blur-sm rounded-full px-3 py-2 space-x-2 border border-blue-700/30">
                    {slides.map((_, index) => (
                        <Button
                            key={index}
                            aria-label={`Go to slide ${index + 1}`}
                            className={`rounded-full transition-all duration-300 h-2 w-2 p-0 ${
                                currentSlide === index ? "bg-white h-2.5 w-2.5" : "bg-white/30 hover:bg-white/50"
                            }`}
                            variant="ghost"
                            onClick={() => setCurrentSlide(index)}
                        />
                    ))}
                </div>
            </div>
        </div>
    );
};
