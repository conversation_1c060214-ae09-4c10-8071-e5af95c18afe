import React from "react";

interface LogoProps {
    size?: "sm" | "md" | "lg" | "xl";
    className?: string;
}

interface LogoSVGProps {
    width?: number;
    height?: number;
    className?: string;
}

export const Logo: React.FC<LogoProps> = ({ size = "md", className = "" }) => {
    const sizeClasses = {
        sm: "text-2xl",
        md: "text-4xl",
        lg: "text-6xl",
        xl: "text-8xl",
    };

    const circleSize = {
        sm: "w-6 h-6",
        md: "w-8 h-8",
        lg: "w-16 h-16",
        xl: "w-24 h-24",
    };

    return (
        <div className={`inline-flex items-center ${className}`}>
            <span className={`font-bold text-orange-500 ${sizeClasses[size]}`}>my</span>
            <span className={`font-bold text-orange-500 ${sizeClasses[size]}`}>NG</span>
            <div className={`${circleSize[size]} bg-purple-600 rounded-full`} />
        </div>
    );
};

export const LogoSVG: React.FC<LogoSVGProps> = ({ width = 160, height = 60, className = "" }) => {
    return (
        <svg
            className={className}
            height={height}
            viewBox="0 0 160 60"
            width={width}
            xmlns="http://www.w3.org/2000/svg"
        >
            <text fill="#f97316" fontFamily="Poppins" fontSize="32" fontWeight="bold" x="10" y="42">
                my
            </text>

            <text fill="#f97316" fontFamily="Poppins" fontSize="32" fontWeight="bold" x="55" y="42">
                NG
            </text>
            <circle cx="125" cy="30" fill="#9333ea" r="18" />
        </svg>
    );
};
