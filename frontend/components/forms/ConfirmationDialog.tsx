// =============================================================================
// CONFIRMATION DIALOG
// =============================================================================

import { Button } from "../ui.old/Button";
import { Modal } from "../ui.old/Modal";

interface ConfirmationDialogProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
    title: string;
    message: string;
    confirmText?: string;
    cancelText?: string;
    type?: "danger" | "warning" | "info";
    loading?: boolean;
}

export const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({
    isOpen,
    onClose,
    onConfirm,
    title,
    message,
    confirmText = "Confirm",
    cancelText = "Cancel",
    type = "danger",
    loading = false,
}) => {
    const buttonVariant =
        type === "danger"
            ? "danger"
            : type === "warning"
              ? "secondary"
              : "primary";

    return (
        <Modal isOpen={isOpen} size="md" title={title} onClose={onClose}>
            <div className="space-y-4">
                <p className="text-sm text-gray-600">{message}</p>
                <div className="flex justify-end space-x-3">
                    <Button
                        disabled={loading}
                        variant="outline"
                        onClick={onClose}
                    >
                        {cancelText}
                    </Button>
                    <Button
                        disabled={loading}
                        loading={loading}
                        variant={buttonVariant}
                        onClick={onConfirm}
                    >
                        {confirmText}
                    </Button>
                </div>
            </div>
        </Modal>
    );
};
