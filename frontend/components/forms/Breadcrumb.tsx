// =============================================================================
// BREADCRUMB NAVIGATION
// =============================================================================

interface BreadcrumbItem {
    label: string;
    href?: string;
    current?: boolean;
  }
  
  interface BreadcrumbProps {
    items: BreadcrumbItem[];
    className?: string;
  }
  
  export const Breadcrumb: React.FC<BreadcrumbProps> = ({ items, className = '' }) => {
    return (
      <nav className={`flex ${className}`} aria-label="Breadcrumb">
        <ol className="flex items-center space-x-4">
          {items.map((item, index) => (
            <li key={index}>
              <div className="flex items-center">
                {index > 0 && (
                  <svg
                    className="flex-shrink-0 h-5 w-5 text-gray-300 mr-4"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    aria-hidden="true"
                  >
                    <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                  </svg>
                )}
                {item.href && !item.current ? (
                  <a
                    href={item.href}
                    className="text-sm font-medium text-gray-500 hover:text-gray-700"
                  >
                    {item.label}
                  </a>
                ) : (
                  <span className={`text-sm font-medium ${
                    item.current ? 'text-gray-900' : 'text-gray-500'
                  }`}>
                    {item.label}
                  </span>
                )}
              </div>
            </li>
          ))}
        </ol>
      </nav>
    );
  };