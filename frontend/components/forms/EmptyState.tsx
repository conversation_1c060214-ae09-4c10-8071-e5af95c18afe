// =============================================================================
// EMPTY STATE COMPONENT
// =============================================================================

import { DocumentIcon, PlusIcon } from "@heroicons/react/24/outline";
import { Button } from "../ui.old/Button";

interface EmptyStateProps {
    icon?: React.ComponentType<{ className?: string }>;
    title: string;
    description: string;
    actionLabel?: string;
    onAction?: () => void;
    className?: string;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
    icon: Icon = DocumentIcon,
    title,
    description,
    actionLabel,
    onAction,
    className = "",
}) => {
    return (
        <div className={`text-center py-12 ${className}`}>
            <Icon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">{title}</h3>
            <p className="mt-1 text-sm text-gray-500">{description}</p>
            {actionLabel && onAction && (
                <div className="mt-6">
                    <Button onClick={onAction} variant="primary">
                        <PlusIcon className="h-4 w-4 mr-2" />
                        {actionLabel}
                    </Button>
                </div>
            )}
        </div>
    );
};
