// =============================================================================
// ORGANIZATION FORM COMPONENT
// =============================================================================

import { useState } from "react";

import { Alert } from "../ui.old/Alert";
import { Button } from "../ui.old/Button";
import { Card } from "../ui.old/Card";
import { Input } from "../ui.old/Input";
import { Select } from "../ui.old/Select";
import { Label } from "../ui/label";

interface OrganizationFormData {
    name: string;
    organization_type_id: string;
    registration_number: string;
    tax_number?: string;
    email: string;
    phone: string;
    website?: string;
    physical_address: string;
    postal_address: string;
    country_id: string;
    region_id?: string;
    district_id?: string;
    vision?: string;
    mission?: string;
    objectives?: string;
    date_established?: string;
}

interface OrganizationType {
    id: string;
    display_value: string;
    code: string;
}

interface Country {
    id: string;
    name: string;
    short_code: string;
}

interface OrganizationFormProps {
    initialData?: Partial<OrganizationFormData>;
    organizationTypes?: OrganizationType[];
    countries?: Country[];
    isEdit?: boolean;
    loading?: boolean;
    error?: string;
    onSubmit?: (data: OrganizationFormData) => Promise<void>;
    onCancel?: () => void;
}

export const OrganizationForm: React.FC<OrganizationFormProps> = ({
    initialData = {},
    organizationTypes = [],
    countries = [],
    isEdit = false,
    loading = false,
    error,
    onSubmit,
    onCancel,
}) => {
    const [formData, setFormData] = useState<OrganizationFormData>({
        name: "",
        organization_type_id: "",
        registration_number: "",
        tax_number: "",
        email: "",
        phone: "",
        website: "",
        physical_address: "",
        postal_address: "",
        country_id: "",
        region_id: "",
        district_id: "",
        vision: "",
        mission: "",
        objectives: "",
        date_established: "",
        ...initialData,
    });

    const [errors, setErrors] = useState<Record<string, string>>({});

    const typeOptions = [
        { value: "", label: "Select Organization Type" },
        ...organizationTypes.map((type) => ({
            value: type.id,
            label: type.display_value,
        })),
    ];

    const countryOptions = [
        { value: "", label: "Select Country" },
        ...countries.map((country) => ({
            value: country.id,
            label: country.name,
        })),
    ];

    const validateForm = (): boolean => {
        const newErrors: Record<string, string> = {};

        // Required fields
        if (!formData.name.trim())
            newErrors.name = "Organization name is required";
        if (!formData.organization_type_id)
            newErrors.organization_type_id = "Organization type is required";
        if (!formData.registration_number.trim())
            newErrors.registration_number = "Registration number is required";
        if (!formData.email.trim()) newErrors.email = "Email is required";
        if (!formData.phone.trim())
            newErrors.phone = "Phone number is required";
        if (!formData.physical_address.trim())
            newErrors.physical_address = "Physical address is required";
        if (!formData.country_id) newErrors.country_id = "Country is required";

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        if (formData.email && !emailRegex.test(formData.email)) {
            newErrors.email = "Please enter a valid email address";
        }

        // Website validation (optional)
        if (formData.website && !formData.website.startsWith("http")) {
            newErrors.website = "Website should start with http:// or https://";
        }

        setErrors(newErrors);

        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) return;

        try {
            await onSubmit?.(formData);
        } catch (err) {
            throw err;
            // Error handling done by parent
        }
    };

    const updateField = (field: keyof OrganizationFormData, value: any) => {
        setFormData((prev) => ({ ...prev, [field]: value }));
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors((prev) => ({ ...prev, [field]: "" }));
        }
    };

    return (
        <Card
            title={isEdit ? "Edit Organization" : "Register New Organization"}
        >
            {error && (
                <Alert
                    dismissible
                    className="mb-6"
                    message={error}
                    type="error"
                />
            )}

            <form className="space-y-6" onSubmit={handleSubmit}>
                {/* Basic Information */}
                <div>
                    <h4 className="text-lg font-medium text-gray-900 mb-4">
                        Basic Information
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="md:col-span-2">
                            <Input
                                required
                                disabled={loading}
                                error={errors.name}
                                label="Organization Name"
                                value={formData.name}
                                onChange={(value) => updateField("name", value)}
                            />
                        </div>
                        <Select
                            required
                            disabled={loading}
                            error={errors.organization_type_id}
                            label="Organization Type"
                            options={typeOptions}
                            value={formData.organization_type_id}
                            onChange={(value) =>
                                updateField("organization_type_id", value)
                            }
                        />
                        <Input
                            required
                            disabled={loading}
                            error={errors.registration_number}
                            label="Registration Number"
                            value={formData.registration_number}
                            onChange={(value) =>
                                updateField("registration_number", value)
                            }
                        />
                        <Input
                            disabled={loading}
                            error={errors.tax_number}
                            label="Tax Number"
                            value={formData.tax_number}
                            onChange={(value) =>
                                updateField("tax_number", value)
                            }
                        />
                        <Input
                            disabled={loading}
                            error={errors.date_established}
                            label="Date Established"
                            placeholder="YYYY-MM-DD"
                            type="text"
                            value={formData.date_established}
                            onChange={(value) =>
                                updateField("date_established", value)
                            }
                        />
                    </div>
                </div>

                {/* Contact Information */}
                <div>
                    <h4 className="text-lg font-medium text-gray-900 mb-4">
                        Contact Information
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Input
                            required
                            disabled={loading}
                            error={errors.email}
                            label="Email"
                            type="email"
                            value={formData.email}
                            onChange={(value) => updateField("email", value)}
                        />
                        <Input
                            required
                            disabled={loading}
                            error={errors.phone}
                            label="Phone Number"
                            type="tel"
                            value={formData.phone}
                            onChange={(value) => updateField("phone", value)}
                        />
                        <div className="md:col-span-2">
                            <Input
                                disabled={loading}
                                error={errors.website}
                                label="Website"
                                placeholder="https://example.com"
                                value={formData.website}
                                onChange={(value) =>
                                    updateField("website", value)
                                }
                            />
                        </div>
                    </div>
                </div>

                {/* Address Information */}
                <div>
                    <h4 className="text-lg font-medium text-gray-900 mb-4">
                        Address Information
                    </h4>
                    <div className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <Select
                                required
                                disabled={loading}
                                error={errors.country_id}
                                label="Country"
                                options={countryOptions}
                                value={formData.country_id}
                                onChange={(value) =>
                                    updateField("country_id", value)
                                }
                            />
                            <Input
                                disabled={loading}
                                error={errors.region_id}
                                label="Region"
                                value={formData.region_id}
                                onChange={(value) =>
                                    updateField("region_id", value)
                                }
                            />
                            <Input
                                disabled={loading}
                                error={errors.district_id}
                                label="District"
                                value={formData.district_id}
                                onChange={(value) =>
                                    updateField("district_id", value)
                                }
                            />
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Physical Address{" "}
                                    <span className="text-red-500">*</span>
                                </label>
                                <textarea
                                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                    disabled={loading}
                                    rows={3}
                                    value={formData.physical_address}
                                    onChange={(e) =>
                                        updateField(
                                            "physical_address",
                                            e.target.value,
                                        )
                                    }
                                />
                                {errors.physical_address && (
                                    <p className="mt-1 text-sm text-red-600">
                                        {errors.physical_address}
                                    </p>
                                )}
                            </div>
                            <div>
                                <Label
                                    className="block text-sm font-medium text-gray-700 mb-1"
                                    htmlFor="postal-address"
                                >
                                    Postal Address
                                </Label>
                                <textarea
                                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                    disabled={loading}
                                    name="postal-address"
                                    rows={3}
                                    value={formData.postal_address}
                                    onChange={(e) =>
                                        updateField(
                                            "postal_address",
                                            e.target.value,
                                        )
                                    }
                                />
                            </div>
                        </div>
                    </div>
                </div>

                {/* Organization Details */}
                <div>
                    <h4 className="text-lg font-medium text-gray-900 mb-4">
                        Organization Details
                    </h4>
                    <div className="space-y-4">
                        <div>
                            <Label
                                className="block text-sm font-medium text-gray-700 mb-1"
                                htmlFor="vision"
                            >
                                Vision
                            </Label>
                            <textarea
                                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                disabled={loading}
                                name="vision"
                                placeholder="Organization's vision statement..."
                                rows={2}
                                value={formData.vision}
                                onChange={(e) =>
                                    updateField("vision", e.target.value)
                                }
                            />
                        </div>
                        <div>
                            <Label className="block text-sm font-medium text-gray-700 mb-1">
                                Mission
                            </Label>
                            <textarea
                                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                disabled={loading}
                                placeholder="Organization's mission statement..."
                                rows={2}
                                value={formData.mission}
                                onChange={(e) =>
                                    updateField("mission", e.target.value)
                                }
                            />
                        </div>
                        <div>
                            <Label
                                className="block text-sm font-medium text-gray-700 mb-1"
                                htmlFor="objectives"
                            >
                                Objectives
                            </Label>
                            <textarea
                                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                disabled={loading}
                                name="objectives"
                                placeholder="List the main objectives of the organization..."
                                rows={3}
                                value={formData.objectives}
                                onChange={(e) =>
                                    updateField("objectives", e.target.value)
                                }
                            />
                        </div>
                    </div>
                </div>

                {/* Form Actions */}
                <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                    <Button
                        disabled={loading}
                        type="button"
                        variant="outline"
                        onClick={onCancel}
                    >
                        Cancel
                    </Button>
                    <Button
                        disabled={loading}
                        loading={loading}
                        type="submit"
                        variant="primary"
                    >
                        {isEdit
                            ? "Update Organization"
                            : "Register Organization"}
                    </Button>
                </div>
            </form>
        </Card>
    );
};
