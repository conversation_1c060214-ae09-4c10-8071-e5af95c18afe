// =============================================================================
// USERS MANAGEMENT PAGE
// =============================================================================

import { PlusIcon } from "lucide-react";
import { useState } from "react";

import { ActionButtons } from "../ui.old/ActionButtons";
import { Button } from "../ui.old/Button";
import { Card } from "../ui.old/Card";
import { DataTable } from "../ui.old/DataTable";
import { Input } from "../ui.old/Input";
import { Select } from "../ui.old/Select";

interface User {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
    status: string;
    created_at: string;
    role?: string;
}

interface UsersPageProps {
    users?: User[];
    loading?: boolean;
    onCreateUser?: () => void;
    onEditUser?: (user: User) => void;
    onDeleteUser?: (user: User) => void;
    onViewUser?: (user: User) => void;
}

export const UsersPage: React.FC<UsersPageProps> = ({
    users = [],
    loading = false,
    onCreateUser,
    onEditUser,
    onDeleteUser,
    onViewUser,
}) => {
    const [searchTerm, setSearchTerm] = useState("");
    const [statusFilter, setStatusFilter] = useState("");
    const [deleteModal, setDeleteModal] = useState<{
        open: boolean;
        user?: User;
    }>({ open: false });

    const statusOptions = [
        { value: "", label: "All Statuses" },
        { value: "active", label: "Active" },
        { value: "inactive", label: "Inactive" },
        { value: "suspended", label: "Suspended" },
    ];

    const filteredUsers = users.filter((user) => {
        const matchesSearch =
            user.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            user.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            user.email.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesStatus = !statusFilter || user.status === statusFilter;

        return matchesSearch && matchesStatus;
    });

    const columns = [
        {
            header: "Name",
            accessorKey: "first_name" as keyof User,
            cell: (value: any, row: User) => (
                <div>
                    <div className="font-medium text-gray-900">
                        {row.first_name} {row.last_name}
                    </div>
                    <div className="text-gray-500">{row.email}</div>
                </div>
            ),
        },
        {
            header: "Role",
            accessorKey: "role" as keyof User,
            cell: (value: string) => (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {value || "User"}
                </span>
            ),
        },
        {
            header: "Status",
            accessorKey: "status" as keyof User,
            cell: (value: string) => (
                <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        value === "active"
                            ? "bg-green-100 text-green-800"
                            : value === "inactive"
                              ? "bg-gray-100 text-gray-800"
                              : "bg-red-100 text-red-800"
                    }`}
                >
                    {value}
                </span>
            ),
        },
        {
            header: "Created",
            accessorKey: "created_at" as keyof User,
            cell: (value: string) => new Date(value).toLocaleDateString(),
        },
        {
            header: "Actions",
            accessorKey: "user_id" as keyof User,
            cell: (value: any, row: User) => (
                <ActionButtons
                    onDelete={() => setDeleteModal({ open: true, user: row })}
                    onEdit={() => onEditUser?.(row)}
                    onView={() => onViewUser?.(row)}
                />
            ),
        },
    ];

    const handleConfirmDelete = () => {
        if (deleteModal.user) {
            onDeleteUser?.(deleteModal.user);
            setDeleteModal({ open: false });
        }
    };

    return (
        <div className="space-y-6">
            {/* Page Header */}
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">Users</h1>
                    <p className="mt-1 text-sm text-gray-600">
                        Manage users and their permissions in your organization
                    </p>
                </div>
                <Button variant="primary" onClick={onCreateUser}>
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Add User
                </Button>
            </div>

            {/* Filters */}
            <Card>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Input
                        placeholder="Search users..."
                        value={searchTerm}
                        onChange={setSearchTerm}
                    />
                    <Select
                        options={statusOptions}
                        placeholder="Filter by status"
                        value={statusFilter}
                        onChange={setStatusFilter}
                    />
                    <div className="flex justify-end">
                        <Button
                            variant="outline"
                            onClick={() => {
                                setSearchTerm("");
                                setStatusFilter("");
                            }}
                        >
                            Clear Filters
                        </Button>
                    </div>
                </div>
            </Card>

            {/* Users Table */}
            <Card padding="none">
                <DataTable
                    columns={columns}
                    data={filteredUsers}
                    emptyMessage="No users found"
                    loading={loading}
                />
            </Card>

            {/* Delete Confirmation Modal */}
            {/* <Dialog
                isOpen={deleteModal.open}
                size="md"
                title="Delete User"
                onClose={() => setDeleteModal({ open: false })}
            >
                <div className="space-y-4">
                    <p className="text-sm text-gray-600">
                        Are you sure you want to delete{" "}
                        <strong>
                            {deleteModal.user?.first_name}{" "}
                            {deleteModal.user?.last_name}
                        </strong>
                        ? ? This action cannot be undone.
                    </p>
                    <div className="flex justify-end space-x-3">
                        <Button
                            variant="outline"
                            onClick={() => setDeleteModal({ open: false })}
                        >
                            Cancel
                        </Button>
                        <Button variant="danger" onClick={handleConfirmDelete}>
                            Delete
                        </Button>
                    </div>
                </div>
            </Dialog> */}
        </div>
    );
};
