// =============================================================================
// ORGANIZATIONS PAGE
// =============================================================================

import { PlusIcon } from "lucide-react";
import { useState } from "react";

import { ActionButtons } from "../ui.old/ActionButtons";
import { Button } from "../ui.old/Button";
import { Card } from "../ui.old/Card";
import { DataTable } from "../ui.old/DataTable";
import { Input } from "../ui.old/Input";
import { Select } from "../ui.old/Select";

interface Organization {
    id: string;
    name: string;
    type: string;
    status: string;
    registration_number: string;
    email: string;
    phone: string;
    created_at: string;
    member_count?: number;
}

interface OrganizationsPageProps {
    organizations?: Organization[];
    loading?: boolean;
    onCreateOrganization?: () => void;
    onEditOrganization?: (org: Organization) => void;
    onViewOrganization?: (org: Organization) => void;
}

export const OrganizationsPage: React.FC<OrganizationsPageProps> = ({
    organizations = [],
    loading = false,
    onCreateOrganization,
    onEditOrganization,
    onViewOrganization,
}) => {
    const [searchTerm, setSearchTerm] = useState("");
    const [typeFilter, setTypeFilter] = useState("");
    const [statusFilter, setStatusFilter] = useState("");

    const typeOptions = [
        { value: "", label: "All Types" },
        { value: "NGO", label: "NGO" },
        { value: "CBO", label: "Community Based Organization" },
        { value: "FAITH", label: "Faith Based Organization" },
    ];

    const statusOptions = [
        { value: "", label: "All Statuses" },
        { value: "active", label: "Active" },
        { value: "pending", label: "Pending" },
        { value: "suspended", label: "Suspended" },
    ];

    const filteredOrganizations = organizations.filter((org) => {
        const matchesSearch =
            org.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            org.registration_number
                .toLowerCase()
                .includes(searchTerm.toLowerCase());

        const matchesType = !typeFilter || org.type === typeFilter;
        const matchesStatus = !statusFilter || org.status === statusFilter;

        return matchesSearch && matchesType && matchesStatus;
    });

    const columns = [
        {
            header: "Organization",
            accessorKey: "name" as keyof Organization,
            cell: (value: any, row: Organization) => (
                <div>
                    <div className="font-medium text-gray-900">{row.name}</div>
                    <div className="text-gray-500">
                        {row.registration_number}
                    </div>
                </div>
            ),
        },
        {
            header: "Type",
            accessorKey: "type" as keyof Organization,
            cell: (value: string) => (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                    {value}
                </span>
            ),
        },
        {
            header: "Status",
            accessorKey: "status" as keyof Organization,
            cell: (value: string) => (
                <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        value === "active"
                            ? "bg-green-100 text-green-800"
                            : value === "pending"
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-red-100 text-red-800"
                    }`}
                >
                    {value}
                </span>
            ),
        },
        {
            header: "Members",
            accessorKey: "member_count" as keyof Organization,
            cell: (value: number) => value || 0,
        },
        {
            header: "Contact",
            accessorKey: "email" as keyof Organization,
            cell: (value: any, row: Organization) => (
                <div className="text-sm">
                    <div>{row.email}</div>
                    <div className="text-gray-500">{row.phone}</div>
                </div>
            ),
        },
        {
            header: "Actions",
            accessorKey: "id" as keyof Organization,
            cell: (value: any, row: Organization) => (
                <ActionButtons
                    onEdit={() => onEditOrganization?.(row)}
                    onView={() => onViewOrganization?.(row)}
                />
            ),
        },
    ];

    return (
        <div className="space-y-6">
            {/* Page Header */}
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">
                        Organizations
                    </h1>
                    <p className="mt-1 text-sm text-gray-600">
                        Manage registered organizations and their applications
                    </p>
                </div>
                <Button variant="primary" onClick={onCreateOrganization}>
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Register Organization
                </Button>
            </div>

            {/* Filters */}
            <Card>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Input
                        placeholder="Search organizations..."
                        value={searchTerm}
                        onChange={setSearchTerm}
                    />
                    <Select
                        options={typeOptions}
                        placeholder="Filter by type"
                        value={typeFilter}
                        onChange={setTypeFilter}
                    />
                    <Select
                        options={statusOptions}
                        placeholder="Filter by status"
                        value={statusFilter}
                        onChange={setStatusFilter}
                    />
                    <div className="flex justify-end">
                        <Button
                            variant="outline"
                            onClick={() => {
                                setSearchTerm("");
                                setTypeFilter("");
                                setStatusFilter("");
                            }}
                        >
                            Clear Filters
                        </Button>
                    </div>
                </div>
            </Card>

            {/* Organizations Table */}
            <Card padding="none">
                <DataTable
                    columns={columns}
                    data={filteredOrganizations}
                    emptyMessage="No organizations found"
                    loading={loading}
                    onRowClick={onViewOrganization}
                />
            </Card>
        </div>
    );
};
