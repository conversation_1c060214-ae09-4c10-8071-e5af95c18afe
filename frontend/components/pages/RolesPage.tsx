// =============================================================================
// ROLES MANAGEMENT PAGE
// =============================================================================

import { PlusIcon } from "lucide-react";
import { useState } from "react";

import { ActionButtons } from "../ui.old/ActionButtons";
import { Button } from "../ui.old/Button";
import { Card } from "../ui.old/Card";
import { DataTable } from "../ui.old/DataTable";
import { Input } from "../ui.old/Input";

type Permission = {
    id: string;
    code: string;
    display_value: string;
    description?: string;
    type?: string;
};

type Role = {
    id: string;
    code: string;
    name: string;
    description?: string;
    permissions: Permission[];
    created_at?: string;
    updated_at?: string;
};

interface RolesPageProps {
    roles?: Role[];
    loading?: boolean;
    onCreateRole?: () => void;
    onEditRole?: (role: Role) => void;
    onDeleteRole?: (role: Role) => void;
    onViewRole?: (role: Role) => void;
}

export const RolesPage: React.FC<RolesPageProps> = ({
    roles = [],
    loading = false,
    onCreateRole,
    onEditRole,
    onDeleteRole,
    onViewRole,
}) => {
    const [searchTerm, setSearchTerm] = useState("");

    const filteredRoles = roles.filter(
        (role) =>
            role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            role.code.toLowerCase().includes(searchTerm.toLowerCase()),
    );

    const columns = [
        {
            header: "Role",
            accessorKey: "name" as keyof Role,
            cell: (value: any, row: Role) => (
                <div>
                    <div className="font-medium text-gray-900">{row.name}</div>
                    <div className="text-gray-500 text-sm font-mono">
                        {row.code}
                    </div>
                </div>
            ),
        },
        {
            header: "Description",
            accessorKey: "description" as keyof Role,
            cell: (value: string) => value || "—",
        },
        {
            header: "Permissions",
            accessorKey: "permissions" as keyof Role,
            cell: (permissions: Permission[]) => (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {permissions?.length || 0} permissions
                </span>
            ),
        },
        {
            header: "Created",
            accessorKey: "created_at" as keyof Role,
            cell: (value: string) => new Date(value).toLocaleDateString(),
        },
        {
            header: "Actions",
            accessorKey: "id" as keyof Role,
            cell: (value: any, row: Role) => (
                <ActionButtons
                    onDelete={() => onDeleteRole?.(row)}
                    onEdit={() => onEditRole?.(row)}
                    onView={() => onViewRole?.(row)}
                />
            ),
        },
    ];

    return (
        <div className="space-y-6">
            {/* Page Header */}
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">
                        Roles & Permissions
                    </h1>
                    <p className="mt-1 text-sm text-gray-600">
                        Manage user roles and their associated permissions
                    </p>
                </div>
                <Button variant="primary" onClick={onCreateRole}>
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Create Role
                </Button>
            </div>

            {/* Search */}
            <Card>
                <Input
                    placeholder="Search roles..."
                    value={searchTerm}
                    onChange={setSearchTerm}
                />
            </Card>

            {/* Roles Table */}
            <Card padding="none">
                <DataTable
                    columns={columns}
                    data={filteredRoles}
                    emptyMessage="No roles found"
                    loading={loading}
                />
            </Card>
        </div>
    );
};
