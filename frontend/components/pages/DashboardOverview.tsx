// =============================================================================
// DASHBOARD OVERVIEW PAGE
// =============================================================================

import {
    Building2Icon,
    ChartBarIcon,
    FileClockIcon,
    UsersIcon,
} from "lucide-react";

import { Button } from "../ui.old/Button";
import { Card } from "../ui.old/Card";

interface DashboardStats {
    totalUsers: number;
    totalOrganizations: number;
    pendingApplications: number;
    activeWorkflows: number;
}

interface DashboardProps {
    stats?: DashboardStats;
    loading?: boolean;
}

export const DashboardOverview: React.FC<DashboardProps> = ({
    stats = {
        totalUsers: 0,
        totalOrganizations: 0,
        pendingApplications: 0,
        activeWorkflows: 0,
    },
    loading = false,
}) => {
    const statCards = [
        {
            title: "Total Users",
            value: stats.totalUsers,
            icon: UsersIcon,
            color: "blue",
            href: "/users",
        },
        {
            title: "Organizations",
            value: stats.totalOrganizations,
            icon: Building2Icon,
            color: "green",
            href: "/organizations",
        },
        {
            title: "Pending Applications",
            value: stats.pendingApplications,
            icon: FileClockIcon,
            color: "yellow",
            href: "/applications",
        },
        {
            title: "Active Workflows",
            value: stats.activeWorkflows,
            icon: ChartBarIcon,
            color: "purple",
            href: "/workflows",
        },
    ];

    const colorClasses = {
        blue: "bg-blue-500",
        green: "bg-green-500",
        yellow: "bg-yellow-500",
        purple: "bg-purple-500",
    };

    if (loading) {
        return (
            <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {[1, 2, 3, 4].map((i) => (
                        <div
                            key={i}
                            className="bg-white rounded-lg border border-gray-200 p-6 animate-pulse"
                        >
                            <div className="h-4 bg-gray-200 rounded w-1/2 mb-2" />
                            <div className="h-8 bg-gray-200 rounded w-1/3" />
                        </div>
                    ))}
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Welcome Header */}
            <div>
                <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
                <p className="mt-1 text-sm text-gray-600">
                    Welcome back! Here&apos;s what&apos;s happening with your
                    NGO platform.
                </p>
            </div>

            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {statCards.map((stat, index) => (
                    <Card
                        key={index}
                        className="hover:shadow-md transition-shadow cursor-pointer"
                    >
                        <a className="block" href={stat.href}>
                            <div className="flex items-center">
                                <div
                                    className={`flex-shrink-0 p-3 rounded-md ${colorClasses[stat.color as keyof typeof colorClasses]}`}
                                >
                                    <stat.icon className="h-6 w-6 text-white" />
                                </div>
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">
                                        {stat.title}
                                    </p>
                                    <p className="text-2xl font-bold text-gray-900">
                                        {stat.value.toLocaleString()}
                                    </p>
                                </div>
                            </div>
                        </a>
                    </Card>
                ))}
            </div>

            {/* Recent Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card
                    subtitle="Latest organization registration requests"
                    title="Recent Applications"
                >
                    <div className="space-y-3">
                        {[1, 2, 3, 4, 5].map((i) => (
                            <div
                                key={i}
                                className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0"
                            >
                                <div>
                                    <p className="text-sm font-medium text-gray-900">
                                        Sample Organization {i}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                        Submitted 2 hours ago
                                    </p>
                                </div>
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    Pending
                                </span>
                            </div>
                        ))}
                    </div>
                    <div className="mt-4">
                        <Button fullWidth size="sm" variant="outline">
                            View All Applications
                        </Button>
                    </div>
                </Card>

                <Card
                    subtitle="Recent user and system actions"
                    title="System Activity"
                >
                    <div className="space-y-3">
                        {[
                            {
                                action: "New user registered",
                                user: "John Doe",
                                time: "10 minutes ago",
                            },
                            {
                                action: "Application approved",
                                user: "Admin",
                                time: "1 hour ago",
                            },
                            {
                                action: "Document uploaded",
                                user: "Jane Smith",
                                time: "2 hours ago",
                            },
                            {
                                action: "Workflow completed",
                                user: "System",
                                time: "3 hours ago",
                            },
                            {
                                action: "User role updated",
                                user: "Admin",
                                time: "4 hours ago",
                            },
                        ].map((activity, index) => (
                            <div
                                key={index}
                                className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0"
                            >
                                <div>
                                    <p className="text-sm font-medium text-gray-900">
                                        {activity.action}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                        by {activity.user}
                                    </p>
                                </div>
                                <p className="text-xs text-gray-400">
                                    {activity.time}
                                </p>
                            </div>
                        ))}
                    </div>
                    <div className="mt-4">
                        <Button fullWidth size="sm" variant="outline">
                            View Activity Log
                        </Button>
                    </div>
                </Card>
            </div>
        </div>
    );
};
