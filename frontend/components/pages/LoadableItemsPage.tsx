// =============================================================================
// LOADABLE ITEMS MANAGEMENT
// =============================================================================

import { PlusIcon } from "lucide-react";
import { useState } from "react";

import { SearchFilterBar } from "../forms/SearchFilterBar";
import { ActionButtons } from "../ui.old/ActionButtons";
import { Button } from "../ui.old/Button";
import { Card } from "../ui.old/Card";
import { DataTable } from "../ui.old/DataTable";

type LoadableItem = {
    id: string;
    code: string;
    display_value: string;
    type: string;
    description?: string;
    created_at: string;
    // Add any other fields your items use
};

interface LoadableItemsPageProps {
    items?: LoadableItem[];
    loading?: boolean;
    onCreateItem?: () => void;
    onEditItem?: (item: LoadableItem) => void;
    onDeleteItem?: (item: LoadableItem) => void;
}

export const LoadableItemsPage: React.FC<LoadableItemsPageProps> = ({
    items = [],
    loading = false,
    onCreateItem,
    onEditItem,
    onDeleteItem,
}) => {
    const [typeFilter, setTypeFilter] = useState("");
    const [searchTerm, setSearchTerm] = useState("");

    const typeOptions = [
        { value: "", label: "All Types" },
        { value: "PERMISSION", label: "Permissions" },
        { value: "ROLE", label: "Roles" },
        { value: "DEPARTMENT", label: "Departments" },
        { value: "ORGANIZATION_TYPE", label: "Organization Types" },
        { value: "SECTOR", label: "Sectors" },
    ];

    const filteredItems = items.filter((item) => {
        const matchesSearch =
            item.display_value
                .toLowerCase()
                .includes(searchTerm.toLowerCase()) ||
            item.code.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesType = !typeFilter || item.type === typeFilter;

        return matchesSearch && matchesType;
    });

    const columns = [
        {
            header: "Name",
            accessorKey: "display_value" as keyof LoadableItem,
            cell: (value: any, row: LoadableItem) => (
                <div>
                    <div className="font-medium text-gray-900">
                        {row.display_value}
                    </div>
                    <div className="text-gray-500 text-sm font-mono">
                        {row.code}
                    </div>
                </div>
            ),
        },
        {
            header: "Type",
            accessorKey: "type" as keyof LoadableItem,
            cell: (value: string) => (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {value.replace("_", " ")}
                </span>
            ),
        },
        {
            header: "Description",
            accessorKey: "description" as keyof LoadableItem,
            cell: (value: string) => value || "—",
        },
        {
            header: "Created",
            accessorKey: "created_at" as keyof LoadableItem,
            cell: (value: string) => new Date(value).toLocaleDateString(),
        },
        {
            header: "Actions",
            accessorKey: "id" as keyof LoadableItem,
            cell: (value: any, row: LoadableItem) => (
                <ActionButtons
                    onDelete={() => onDeleteItem?.(row)}
                    onEdit={() => onEditItem?.(row)}
                />
            ),
        },
    ];

    return (
        <div className="space-y-6">
            {/* Page Header */}
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">
                        Loadable Items
                    </h1>
                    <p className="mt-1 text-sm text-gray-600">
                        Manage system configuration items like permissions,
                        roles, and types
                    </p>
                </div>
                <Button variant="primary" onClick={onCreateItem}>
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Add Item
                </Button>
            </div>

            {/* Filters */}
            <SearchFilterBar
                filters={[
                    {
                        key: "type",
                        label: "Type",
                        type: "select",
                        options: typeOptions,
                        placeholder: "Filter by type",
                    },
                ]}
                searchPlaceholder="Search items..."
                showExport={true}
                onClear={() => {
                    setSearchTerm("");
                    setTypeFilter("");
                }}
                onFilter={(filters) => setTypeFilter(filters.type || "")}
                onSearch={setSearchTerm}
            />

            {/* Items Table */}
            <Card padding="none">
                <DataTable
                    columns={columns}
                    data={filteredItems}
                    emptyMessage="No loadable items found"
                    loading={loading}
                />
            </Card>
        </div>
    );
};
