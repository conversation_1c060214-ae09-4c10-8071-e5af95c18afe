import { Popover, Transition } from "@headlessui/react";
import {
    AcademicCapIcon,
    ArrowRightOnRectangleIcon,
    BanknotesIcon,
    BuildingOfficeIcon,
    ChartBarIcon,
    ChevronDownIcon,
    ChevronRightIcon,
    ClipboardDocumentListIcon,
    Cog8ToothIcon,
    CogIcon,
    CubeIcon,
    DocumentDuplicateIcon,
    DocumentTextIcon,
    FolderIcon,
    HomeIcon,
    MapPinIcon,
    ShieldCheckIcon,
    UsersIcon,
    XMarkIcon,
} from "@heroicons/react/24/outline";
import Image from "next/image";
import React, { useState } from "react";

import { useAuth } from "@/composables/useStore";
interface SidebarMenuItem {
    id: string;
    label: string;
    href: string;
    icon: React.ComponentType<{ className?: string }>;
    badge?: number;
    current?: boolean;
    permission?: string;
    subItems?: SidebarMenuItem[];
}

interface SidebarUser {
    name: string;
    email: string;
    avatar?: string;
    role: string;
    organization?: string;
}

interface SidebarProps {
    isOpen: boolean;
    onToggle: () => void;
    currentPath: string;
    user?: SidebarUser;
    onLogout?: () => void;
    className?: string;
    minimized?: boolean;
}

export const Sidebar: React.FC<SidebarProps> = ({
    isOpen,
    onToggle,
    currentPath,
    user,
    onLogout,
    className = "",
    minimized = false,
}) => {
    const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
    // const [userMenuOpen, setUserMenuOpen] = useState(false);
    const { session } = useAuth();

    // Main navigation items
    const navigationItems: SidebarMenuItem[] = [
        {
            id: "dashboard",
            label: "Dashboard",
            href: "/dashboard",
            icon: HomeIcon,
            current: currentPath === "/dashboard",
        },
        {
            id: "organizations",
            label: "Organizations",
            href: "/organizations",
            icon: BuildingOfficeIcon,
            badge: 3,
            current: currentPath.startsWith("/organizations"),
            subItems: [
                {
                    id: "organizations-list",
                    label: "All Organizations",
                    href: "/organizations",
                    icon: FolderIcon,
                    current: currentPath === "/organizations",
                },
                {
                    id: "organizations-pending",
                    label: "Pending Registration",
                    href: "/organizations/pending",
                    icon: ClipboardDocumentListIcon,
                    badge: 2,
                    current: currentPath === "/organizations/pending",
                },
                {
                    id: "organizations-licenses",
                    label: "Licenses & Permits",
                    href: "/organizations/licenses",
                    icon: DocumentDuplicateIcon,
                    current: currentPath.startsWith("/organizations/licenses"),
                },
            ],
        },
        {
            id: "applications",
            label: "Applications",
            href: "/applications",
            icon: DocumentTextIcon,
            badge: 12,
            current: currentPath.startsWith("/applications"),
            subItems: [
                {
                    id: "applications-pending",
                    label: "Pending Review",
                    href: "/applications/pending",
                    icon: ClipboardDocumentListIcon,
                    badge: 8,
                    current: currentPath === "/applications/pending",
                },
                {
                    id: "applications-approved",
                    label: "Approved",
                    href: "/applications/approved",
                    icon: DocumentTextIcon,
                    current: currentPath === "/applications/approved",
                },
                {
                    id: "applications-rejected",
                    label: "Rejected",
                    href: "/applications/rejected",
                    icon: DocumentTextIcon,
                    current: currentPath === "/applications/rejected",
                },
            ],
        },
        {
            id: "workflows",
            label: "Workflows",
            href: "/workflows",
            icon: ChartBarIcon,
            current: currentPath.startsWith("/workflows"),
            subItems: [
                {
                    id: "workflows-active",
                    label: "Active Workflows",
                    href: "/workflows/active",
                    icon: ChartBarIcon,
                    badge: 5,
                    current: currentPath === "/workflows/active",
                },
                {
                    id: "workflows-templates",
                    label: "Templates",
                    href: "/workflows/templates",
                    icon: DocumentDuplicateIcon,
                    current: currentPath === "/workflows/templates",
                },
            ],
        },
        {
            id: "users",
            label: "Users & Access",
            href: "/users",
            icon: UsersIcon,
            current:
                currentPath.startsWith("/users") ||
                currentPath.startsWith("/roles"),
            subItems: [
                {
                    id: "users-list",
                    label: "All Users",
                    href: "/users",
                    icon: UsersIcon,
                    current: currentPath === "/users",
                },
                {
                    id: "roles",
                    label: "Roles & Permissions",
                    href: "/roles",
                    icon: ShieldCheckIcon,
                    current: currentPath.startsWith("/roles"),
                },
                {
                    id: "departments",
                    label: "Departments",
                    href: "/departments",
                    icon: AcademicCapIcon,
                    current: currentPath.startsWith("/departments"),
                },
            ],
        },
        {
            id: "payments",
            label: "Payments & Fees",
            href: "/payments",
            icon: BanknotesIcon,
            current: currentPath.startsWith("/payments"),
            subItems: [
                {
                    id: "payments-overview",
                    label: "Payment Overview",
                    href: "/payments",
                    icon: BanknotesIcon,
                    current: currentPath === "/payments",
                },
                {
                    id: "invoices",
                    label: "Invoices",
                    href: "/payments/invoices",
                    icon: DocumentTextIcon,
                    current: currentPath === "/payments/invoices",
                },
                {
                    id: "fee-structure",
                    label: "Fee Structure",
                    href: "/finance/fees",
                    icon: CubeIcon,
                    current: currentPath === "/finance/fees",
                },
            ],
        },
        {
            id: "reports",
            label: "Reports & Analytics",
            href: "/reports",
            icon: ChartBarIcon,
            current: currentPath.startsWith("/reports"),
            subItems: [
                {
                    id: "reports-overview",
                    label: "Overview",
                    href: "/reports",
                    icon: ChartBarIcon,
                    current: currentPath === "/reports",
                },
                {
                    id: "reports-organizations",
                    label: "Organization Reports",
                    href: "/reports/organizations",
                    icon: BuildingOfficeIcon,
                    current: currentPath === "/reports/organizations",
                },
                {
                    id: "reports-applications",
                    label: "Application Reports",
                    href: "/reports/applications",
                    icon: DocumentTextIcon,
                    current: currentPath === "/reports/applications",
                },
            ],
        },
        {
            id: "settings",
            label: "Settings",
            href: "/settings",
            icon: Cog8ToothIcon,
            current: currentPath.startsWith("/settings"),
            subItems: [
                {
                    id: "settings-general",
                    label: "General Settings",
                    href: "/settings",
                    icon: CogIcon,
                    current: currentPath === "/settings",
                },
                {
                    id: "settings-loadable",
                    label: "System Configuration",
                    href: "/settings/loadable-items",
                    icon: CubeIcon,
                    current: currentPath === "/settings/loadable-items",
                },
                {
                    id: "settings-locations",
                    label: "Locations",
                    href: "/settings/locations",
                    icon: MapPinIcon,
                    current: currentPath === "/settings/locations",
                },
            ],
        },
    ];

    const toggleExpandedItem = (itemId: string) => {
        const newExpanded = new Set(expandedItems);

        if (newExpanded.has(itemId)) {
            newExpanded.delete(itemId);
        } else {
            newExpanded.add(itemId);
        }
        setExpandedItems(newExpanded);
    };

    const renderMenuItem = (item: SidebarMenuItem, level: number = 0) => {
        if (minimized) {
            if (item.subItems && item.subItems.length > 0) {
                return (
                    <Popover
                        key={item.id}
                        className="relative flex items-center justify-center my-2"
                    >
                        <Popover.Button
                            className={`flex items-center justify-center w-10 h-10 rounded-lg transition-colors duration-150
                ${item.current ? "bg-blue-700 text-white" : "text-gray-300 hover:bg-gray-800 hover:text-white"}`}
                            title={item.label}
                        >
                            <item.icon className="h-6 w-6" />
                        </Popover.Button>
                        <Popover.Panel className="fixed left-20 top-1/2 -translate-y-1/2 z-50 bg-white rounded-lg shadow-lg border border-gray-200 min-w-[160px] py-2">
                            {item.subItems.map((sub) => (
                                <a
                                    key={sub.id}
                                    className={`flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 whitespace-nowrap ${sub.current ? "font-bold text-blue-700" : ""}`}
                                    href={sub.href}
                                >
                                    {sub.icon && (
                                        <sub.icon className="h-4 w-4 mr-2" />
                                    )}
                                    {sub.label}
                                </a>
                            ))}
                        </Popover.Panel>
                    </Popover>
                );
            }

            return (
                <div
                    key={item.id}
                    className="flex items-center justify-center my-2"
                >
                    <button
                        className={`flex items-center justify-center w-10 h-10 rounded-lg transition-colors duration-150
              ${item.current ? "bg-blue-700 text-white" : "text-gray-300 hover:bg-gray-800 hover:text-white"}`}
                        title={item.label}
                        onClick={() => (window.location.href = item.href)}
                    >
                        <item.icon className="h-6 w-6" />
                    </button>
                </div>
            );
        }
        const isExpanded = expandedItems.has(item.id);
        const hasSubItems = item.subItems && item.subItems.length > 0;
        const Icon = item.icon;

        return (
            <div key={item.id} className="relative">
                <div
                    className={`group flex items-center justify-between px-3 py-2.5 text-sm font-medium rounded-lg cursor-pointer relative
            ${level === 0 ? "mb-1" : "mb-0.5 ml-3"}
            ${
            item.current
                    ? "bg-blue-700 text-white opacity-100 before:absolute before:left-0 before:top-2 before:bottom-2 before:w-1 before:bg-blue-400 before:rounded-full"
                    : "text-white hover:bg-gray-800 hover:text-blue-200 opacity-100"
            }
          `}
                >
                    <div className="flex items-center flex-1">
                        <Icon
                            className={`flex-shrink-0 h-5 w-5 mr-3 ${item.current ? "text-white" : "text-gray-400 group-hover:text-blue-300"}`}
                        />
                        <span className="truncate">{item.label}</span>
                        {item.badge && item.badge > 0 && (
                            <span
                                className={`ml-auto inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${item.current ? "bg-blue-600 text-white" : "bg-gray-700 text-gray-200"}`}
                            >
                                {item.badge > 99 ? "99+" : item.badge}
                            </span>
                        )}
                    </div>
                    {hasSubItems && (
                        <button
                            className={`ml-2 p-1 rounded hover:bg-blue-900 transition-colors ${item.current ? "text-white" : "text-gray-400 hover:text-blue-300"}`}
                            tabIndex={-1}
                            onClick={(e) => {
                                e.stopPropagation();
                                toggleExpandedItem(item.id);
                            }}
                        >
                            {isExpanded ? (
                                <ChevronDownIcon className="h-4 w-4" />
                            ) : (
                                <ChevronRightIcon className="h-4 w-4" />
                            )}
                        </button>
                    )}
                </div>
                {/* Sub-items */}
                {hasSubItems && (
                    <Transition
                        enter="transition-all duration-200 ease-out"
                        enterFrom="opacity-0 max-h-0"
                        enterTo="opacity-100 max-h-96"
                        leave="transition-all duration-200 ease-in"
                        leaveFrom="opacity-100 max-h-96"
                        leaveTo="opacity-0 max-h-0"
                        show={isExpanded}
                    >
                        <div className="overflow-hidden">
                            <div className="py-1">
                                {item.subItems?.map((subItem) =>
                                    renderMenuItem(subItem, level + 1),
                                )}
                            </div>
                        </div>
                    </Transition>
                )}
            </div>
        );
    };

    const sidebarContent = (
        <div className="flex flex-col h-full w-full bg-gray-900 border-r border-gray-800 shadow-xl relative group/sidebar">
            {/* Blue glow shadow on hover */}
            <div className="absolute inset-0 pointer-events-none z-0 group-hover/sidebar:shadow-[0_0_32px_0_rgba(59,130,246,0.25)] transition-all duration-300" />
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-800 z-10 relative">
                <div
                    className={`flex items-center ${minimized ? "justify-center" : "gap-3"} w-full`}
                >
                    <div className="flex-shrink-0 w-10 h-10 bg-blue-700 rounded-xl flex items-center justify-center">
                        <span className="text-white font-bold text-xl">N</span>
                    </div>
                    {!minimized && (
                        <div>
                            <h1 className="text-xl font-extrabold text-white tracking-tight">
                                Ngora
                            </h1>
                            <p className="text-xs text-gray-400 font-medium">
                                NGO Management
                            </p>
                        </div>
                    )}
                </div>
                <button
                    className="lg:hidden p-2 rounded-lg text-gray-400 hover:text-blue-400 hover:bg-gray-800 transition-colors"
                    onClick={onToggle}
                >
                    <XMarkIcon className="h-5 w-5" />
                </button>
            </div>

            {/* Navigation */}
            <nav
                className={`flex-1 ${minimized ? "px-0 py-4" : "px-4 py-8"} overflow-y-auto z-10 relative`}
            >
                {minimized ? (
                    <div className="flex flex-col items-center space-y-2 w-full">
                        {navigationItems.map((item) => renderMenuItem(item))}
                    </div>
                ) : (
                    <div className="space-y-8">
                        {/* Section: Main */}
                        <div>
                            <div className="px-2 py-2 text-xs font-extrabold text-white uppercase tracking-widest mb-3 hover:text-blue-400 transition-colors cursor-default">
                                Main
                            </div>
                            <div className="space-y-1">
                                {navigationItems
                                    .slice(0, 1)
                                    .map((item) => renderMenuItem(item))}
                            </div>
                        </div>
                        <div className="border-t border-gray-800" />
                        {/* Section: Management */}
                        <div>
                            <div className="px-2 py-2 text-xs font-extrabold text-white uppercase tracking-widest mb-3 hover:text-blue-400 transition-colors cursor-default">
                                Management
                            </div>
                            <div className="space-y-1">
                                {navigationItems
                                    .slice(1, 7)
                                    .map((item) => renderMenuItem(item))}
                            </div>
                        </div>
                        <div className="border-t border-gray-800" />
                        {/* Section: Other */}
                        <div>
                            <div className="px-2 py-2 text-xs font-extrabold text-white uppercase tracking-widest mb-3 hover:text-blue-400 transition-colors cursor-default">
                                Other
                            </div>
                            <div className="space-y-1">
                                {navigationItems
                                    .slice(7)
                                    .map((item) => renderMenuItem(item))}
                            </div>
                        </div>
                    </div>
                )}
            </nav>

            {/* Profile Card pinned to bottom */}
            <div
                className="mt-auto flex-shrink-0"
                style={{ zIndex: 1, position: "relative" }}
            >
                {(() => {
                    const displayUser = {
                        name: `${session?.first_name} ${session?.last_name}`,
                        email: session?.email,
                        avatar: undefined,
                    };

                    if (minimized) {
                        return (
                            <div className="border-t border-gray-800 p-4 bg-gray-800 flex flex-col items-center gap-2 w-full">
                                {displayUser.avatar ? (
                                    <Image
                                        alt={displayUser.name}
                                        className="h-10 w-10 rounded-full object-cover border-2 border-gray-900"
                                        src={displayUser.avatar}
                                    />
                                ) : (
                                    <div className="h-10 w-10 rounded-full bg-blue-700 flex items-center justify-center">
                                        <span className="text-white text-lg font-medium">
                                            {displayUser.name
                                                .split(" ")
                                                .map((n) => n[0])
                                                .join("")
                                                .toUpperCase()}
                                        </span>
                                    </div>
                                )}
                                <button
                                    className="p-2 rounded-full text-red-400 hover:text-red-300 hover:bg-gray-700 transition-colors"
                                    title="Sign Out"
                                    onClick={onLogout}
                                >
                                    <ArrowRightOnRectangleIcon className="h-5 w-5" />
                                </button>
                            </div>
                        );
                    }

                    return (
                        <div className="border-t border-gray-800 p-5 bg-gray-800 flex items-center gap-3 w-full">
                            <div className="flex-shrink-0">
                                {displayUser.avatar ? (
                                    <img
                                        alt={displayUser.name}
                                        className="h-10 w-10 rounded-full object-cover border-2 border-gray-900"
                                        src={displayUser.avatar}
                                    />
                                ) : (
                                    <div className="h-10 w-10 rounded-full bg-blue-700 flex items-center justify-center">
                                        <span className="text-white text-lg font-medium">
                                            {displayUser.name
                                                .split(" ")
                                                .map((n) => n[0])
                                                .join("")
                                                .toUpperCase()}
                                        </span>
                                    </div>
                                )}
                            </div>
                            <div className="flex-1 min-w-0">
                                <p className="text-sm font-semibold text-white truncate">
                                    {displayUser.name}
                                </p>
                                <p className="text-xs text-gray-400 truncate">
                                    {displayUser.email}
                                </p>
                            </div>
                            <button
                                className="p-2 rounded-full text-red-400 hover:text-red-300 hover:bg-gray-700 transition-colors"
                                title="Sign Out"
                                onClick={onLogout}
                            >
                                <ArrowRightOnRectangleIcon className="h-5 w-5" />
                            </button>
                        </div>
                    );
                })()}
            </div>
        </div>
    );

    return (
        <>
            {/* Mobile overlay */}
            {isOpen && (
                <button
                    className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
                    onClick={onToggle}
                />
            )}

            {/* Desktop sidebar */}
            <div
                className={`hidden lg:flex lg:flex-col lg:fixed lg:inset-y-0 ${minimized ? "lg:w-20" : "lg:w-64"} transition-transform duration-300 ease-in-out ${className}`}
            >
                {sidebarContent}
            </div>

            {/* Mobile sidebar */}
            <Transition
                enter="transition-transform duration-300 ease-out"
                enterFrom="-translate-x-full"
                enterTo="translate-x-0"
                leave="transition-transform duration-300 ease-in"
                leaveFrom="translate-x-0"
                leaveTo="-translate-x-full"
                show={isOpen}
            >
                <div className="fixed inset-y-0 left-0 w-64 z-50 lg:hidden">
                    {sidebarContent}
                </div>
            </Transition>
        </>
    );
};
