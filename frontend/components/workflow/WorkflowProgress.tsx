// =============================================================================
// WORKFLOW PROGRESS TRACKER
// =============================================================================
import React, { Fragment, ReactNode } from 'react';
import { 
  Dialog, 
  Transition, 
  Menu, 
  Disclosure, 
  Listbox,
  Switch,
  RadioGroup,
  Tab
} from '@headlessui/react';
import { 
  ChevronDownIcon, 
  CheckIcon, 
  XMarkIcon,
  EyeIcon,
  EyeSlashIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  InformationCircleIcon,
  XCircleIcon,
  Bars3Icon,
  BellIcon,
  UserCircleIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  ArrowRightOnRectangleIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';
import { WorkflowStageCard } from './WorkflowStageCard';

type WorkflowStage = {
  id: string;
  name: string;
  status: 'PENDING' | 'IN_REVIEW' | 'COMPLETED' | 'APPROVED' | 'REJECTED';
  order: number;
  started_at?: string;
  completed_at?: string;
  // Add any other fields your workflow stages use
};

interface WorkflowProgressProps {
    stages: WorkflowStage[];
    currentStageId?: string;
    onStageClick?: (stage: WorkflowStage) => void;
    layout?: 'horizontal' | 'vertical';
    className?: string;
  }
  
  export const WorkflowProgress: React.FC<WorkflowProgressProps> = ({
    stages,
    currentStageId,
    onStageClick,
    layout = 'horizontal',
    className = '',
  }) => {
    const sortedStages = [...stages].sort((a, b) => a.order - b.order);
  
    if (layout === 'vertical') {
      return (
        <div className={`space-y-4 ${className}`}>
          {sortedStages.map((stage, index) => (
            <div key={stage.id} className="relative">
              <WorkflowStageCard
                stage={stage}
                isActive={stage.id === currentStageId}
                onActionClick={onStageClick}
              />
              {index < sortedStages.length - 1 && (
                <div className="absolute left-3 top-full w-0.5 h-4 bg-gray-300 transform translate-y-1" />
              )}
            </div>
          ))}
        </div>
      );
    }
  
    return (
      <div className={`flex items-center space-x-4 overflow-x-auto pb-4 ${className}`}>
        {sortedStages.map((stage, index) => (
          <div key={stage.id} className="flex items-center">
            <div className="flex-shrink-0 min-w-[200px]">
              <WorkflowStageCard
                stage={stage}
                isActive={stage.id === currentStageId}
                onActionClick={onStageClick}
              />
            </div>
            {index < sortedStages.length - 1 && (
              <ArrowRightIcon className="h-5 w-5 text-gray-400 mx-2 flex-shrink-0" />
            )}
          </div>
        ))}
      </div>
    );
  };