// =============================================================================
// APPLICATION DETAILS COMPONENT
// =============================================================================
import { CogIcon, DocumentTextIcon } from "@heroicons/react/24/outline";
import React, { useState } from "react";

import { Button } from "../ui.old/Button";
import { Card } from "../ui.old/Card";
import { Tabs } from "../ui.old/Tabs";

import { WorkflowProgress } from "./WorkflowProgress";

type WorkflowStage = {
    id: string;
    name: string;
    status: "PENDING" | "IN_REVIEW" | "COMPLETED" | "APPROVED" | "REJECTED";
    order: number;
    started_at?: string;
    completed_at?: string;
    // Add any other fields your workflow stages use
};

interface Application {
    id: string;
    code: string;
    type:
        | "ORGANIZATION_REGISTRATION"
        | "LICENCE_RENEWAL"
        | "PERMIT_APPLICATION";
    status: "DRAFT" | "REVIEW" | "REJECTED" | "SUSPENDED" | "REGISTERED";
    created_at: string;
    updated_at: string;
    organization_name?: string;
    applicant_name?: string;
    workflow_stages?: WorkflowStage[];
}

interface ApplicationDetailsProps {
    application: Application;
    onStatusChange?: (newStatus: string) => void;
    onStageAction?: (stage: WorkflowStage) => void;
    loading?: boolean;
}

export const ApplicationDetails: React.FC<ApplicationDetailsProps> = ({
    application,
    onStatusChange,
    onStageAction,
    loading = false,
}) => {
    const [activeTab, setActiveTab] = useState("overview");

    const applicationTypeLabels = {
        ORGANIZATION_REGISTRATION: "Organization Registration",
        LICENCE_RENEWAL: "License Renewal",
        PERMIT_APPLICATION: "Permit Application",
    };

    const statusConfig = {
        DRAFT: { color: "gray", label: "Draft" },
        REVIEW: { color: "blue", label: "Under Review" },
        REJECTED: { color: "red", label: "Rejected" },
        SUSPENDED: { color: "yellow", label: "Suspended" },
        REGISTERED: { color: "green", label: "Registered" },
    };

    const tabs = [
        {
            id: "overview",
            label: "Overview",
            content: (
                <div className="space-y-6">
                    {/* Application Summary */}
                    <Card title="Application Summary">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700">
                                    Application Code
                                </label>
                                <p className="mt-1 text-sm text-gray-900 font-mono">
                                    {application.code}
                                </p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">
                                    Type
                                </label>
                                <p className="mt-1 text-sm text-gray-900">
                                    {applicationTypeLabels[application.type]}
                                </p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">
                                    Organization
                                </label>
                                <p className="mt-1 text-sm text-gray-900">
                                    {application.organization_name || "N/A"}
                                </p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">
                                    Current Status
                                </label>
                                <div className="mt-1">
                                    <span
                                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                            statusConfig[application.status]
                                                .color === "green"
                                                ? "bg-green-100 text-green-800"
                                                : statusConfig[
                                                        application.status
                                                    ].color === "blue"
                                                  ? "bg-blue-100 text-blue-800"
                                                  : statusConfig[
                                                          application.status
                                                      ].color === "red"
                                                    ? "bg-red-100 text-red-800"
                                                    : statusConfig[
                                                            application.status
                                                        ].color === "yellow"
                                                      ? "bg-yellow-100 text-yellow-800"
                                                      : "bg-gray-100 text-gray-800"
                                        }`}
                                    >
                                        {statusConfig[application.status].label}
                                    </span>
                                </div>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">
                                    Submitted
                                </label>
                                <p className="mt-1 text-sm text-gray-900">
                                    {new Date(
                                        application.created_at,
                                    ).toLocaleDateString()}
                                </p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">
                                    Last Updated
                                </label>
                                <p className="mt-1 text-sm text-gray-900">
                                    {new Date(
                                        application.updated_at,
                                    ).toLocaleDateString()}
                                </p>
                            </div>
                        </div>
                    </Card>

                    {/* Quick Actions */}
                    <Card title="Quick Actions">
                        <div className="flex space-x-3">
                            <Button size="sm" variant="primary">
                                <DocumentTextIcon className="h-4 w-4 mr-2" />
                                View Documents
                            </Button>
                            <Button size="sm" variant="outline">
                                Download PDF
                            </Button>
                            <Button size="sm" variant="outline">
                                Send Email
                            </Button>
                        </div>
                    </Card>
                </div>
            ),
        },
        {
            id: "workflow",
            label: "Workflow",
            content: (
                <div className="space-y-6">
                    {application.workflow_stages &&
                    application.workflow_stages.length > 0 ? (
                        <WorkflowProgress
                            layout="vertical"
                            stages={application.workflow_stages}
                            onStageClick={onStageAction}
                        />
                    ) : (
                        <Card>
                            <div className="text-center py-6">
                                <CogIcon className="mx-auto h-12 w-12 text-gray-400" />
                                <h3 className="mt-2 text-sm font-medium text-gray-900">
                                    No Workflow Configured
                                </h3>
                                <p className="mt-1 text-sm text-gray-500">
                                    This application type doesn&apos;t have a
                                    workflow configured yet.
                                </p>
                            </div>
                        </Card>
                    )}
                </div>
            ),
        },
        {
            id: "documents",
            label: "Documents",
            content: (
                <Card title="Application Documents">
                    <div className="text-center py-6">
                        <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900">
                            No Documents
                        </h3>
                        <p className="mt-1 text-sm text-gray-500">
                            No documents have been uploaded for this application
                            yet.
                        </p>
                        <div className="mt-6">
                            <Button size="sm" variant="primary">
                                Upload Document
                            </Button>
                        </div>
                    </div>
                </Card>
            ),
        },
        {
            id: "history",
            label: "History",
            content: (
                <Card title="Application History">
                    <div className="space-y-4">
                        {[
                            {
                                action: "Application submitted",
                                user: "John Doe",
                                timestamp: "2025-01-15 10:30:00",
                            },
                            {
                                action: "Initial review started",
                                user: "Admin",
                                timestamp: "2025-01-15 14:20:00",
                            },
                            {
                                action: "Documents requested",
                                user: "Reviewer",
                                timestamp: "2025-01-16 09:15:00",
                            },
                        ].map((entry, index) => (
                            <div
                                key={index}
                                className="flex items-start space-x-3 py-2 border-b border-gray-100 last:border-b-0"
                            >
                                <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2" />
                                <div className="flex-1">
                                    <p className="text-sm font-medium text-gray-900">
                                        {entry.action}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                        by {entry.user} on {entry.timestamp}
                                    </p>
                                </div>
                            </div>
                        ))}
                    </div>
                </Card>
            ),
        },
    ];

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">
                        Application {application.code}
                    </h1>
                    <p className="mt-1 text-sm text-gray-600">
                        {applicationTypeLabels[application.type]}
                    </p>
                </div>
                <div className="flex space-x-3">
                    <Button variant="outline">Edit Application</Button>
                    <Button variant="primary">Process Application</Button>
                </div>
            </div>

            {/* Content Tabs */}
            <Tabs defaultTab="overview" tabs={tabs} onChange={setActiveTab} />
        </div>
    );
};
