// =============================================================================
// WORKFLOW STAGE CARD
// =============================================================================
import React from "react";

import { Button } from "../ui.old/Button";

import { CheckCircleIcon, Clock10Icon, UsersRoundIcon } from "lucide-react";
import { WorkflowStatusBadge } from "./WorkflowStatusBadge";
interface WorkflowStage {
    id: string;
    name: string;
    description?: string;
    status: "PENDING" | "IN_REVIEW" | "COMPLETED" | "APPROVED" | "REJECTED";
    assignee?: string;
    due_date?: string;
    completed_at?: string;
    order: number;
}

interface WorkflowStageCardProps {
    stage: WorkflowStage;
    isActive?: boolean;
    onActionClick?: (stage: WorkflowStage) => void;
    showActions?: boolean;
}

export const WorkflowStageCard: React.FC<WorkflowStageCardProps> = ({
    stage,
    isActive = false,
    onActionClick,
    showActions = true,
}) => {
    const isCompleted =
        stage.status === "COMPLETED" || stage.status === "APPROVED";
    const isRejected = stage.status === "REJECTED";

    return (
        <div
            className={`relative p-4 rounded-lg border-2 transition-all ${
                isActive
                    ? "border-blue-500 bg-blue-50"
                    : isCompleted
                      ? "border-green-300 bg-green-50"
                      : isRejected
                        ? "border-red-300 bg-red-50"
                        : "border-gray-200 bg-white"
            }`}
        >
            {/* Stage Number */}
            <div
                className={`absolute -top-3 -left-3 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                    isCompleted
                        ? "bg-green-500 text-white"
                        : isRejected
                          ? "bg-red-500 text-white"
                          : isActive
                            ? "bg-blue-500 text-white"
                            : "bg-gray-300 text-gray-600"
                }`}
            >
                {stage.order}
            </div>

            <div className="space-y-3">
                {/* Stage Header */}
                <div className="flex items-start justify-between">
                    <div>
                        <h4 className="font-medium text-gray-900">
                            {stage.name}
                        </h4>
                        {stage.description && (
                            <p className="text-sm text-gray-600 mt-1">
                                {stage.description}
                            </p>
                        )}
                    </div>
                    <WorkflowStatusBadge status={stage.status} />
                </div>

                {/* Stage Details */}
                <div className="space-y-2 text-sm text-gray-600">
                    {stage.assignee && (
                        <div className="flex items-center">
                            <UsersRoundIcon className="h-4 w-4 mr-2" />
                            <span>Assigned to: {stage.assignee}</span>
                        </div>
                    )}

                    {stage.due_date && !isCompleted && (
                        <div className="flex items-center">
                            <Clock10Icon className="h-4 w-4 mr-2" />
                            <span>
                                Due:{" "}
                                {new Date(stage.due_date).toLocaleDateString()}
                            </span>
                        </div>
                    )}

                    {stage.completed_at && (
                        <div className="flex items-center">
                            <CheckCircleIcon className="h-4 w-4 mr-2 text-green-500" />
                            <span>
                                Completed:{" "}
                                {new Date(
                                    stage.completed_at,
                                ).toLocaleDateString()}
                            </span>
                        </div>
                    )}
                </div>

                {/* Actions */}
                {showActions && isActive && !isCompleted && (
                    <div className="pt-2 border-t border-gray-200">
                        <Button
                            size="sm"
                            variant="primary"
                            onClick={() => onActionClick?.(stage)}
                        >
                            Take Action
                        </Button>
                    </div>
                )}
            </div>
        </div>
    );
};
