// =============================================================================
// NAVIGATION BAR COMPONENT
// =============================================================================
import React, { Fragment, ReactNode } from 'react';
import { 
  Dialog, 
  Transition, 
  Menu, 
  Disclosure, 
  Listbox,
  Switch,
  RadioGroup,
  Tab
} from '@headlessui/react';
import { 
  ChevronDownIcon, 
  CheckIcon, 
  XMarkIcon,
  EyeIcon,
  EyeSlashIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  InformationCircleIcon,
  XCircleIcon,
  Bars3Icon,
  BellIcon,
  UserCircleIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline';
interface NavItem {
    label: string;
    href: string;
    icon?: React.ComponentType<{ className?: string }>;
    current?: boolean;
    badge?: number;
  }
  
  interface NavBarProps {
    brandName: string;
    navigation: NavItem[];
    userMenuItems: Array<{ label: string; onClick: () => void; icon?: React.ComponentType<{ className?: string }> }>;
    onMenuClick?: () => void;
    notificationCount?: number;
    userName?: string;
    userAvatar?: string;
  }
  
  export const NavBar: React.FC<NavBarProps> = ({
    brandName,
    navigation,
    userMenuItems,
    onMenuClick,
    notificationCount = 0,
    userName = "Hen Mik",
    userAvatar,
  }) => {
    // Helper for user initials
    const getInitials = (name: string) => name.split(' ').map(n => n[0]).join('').toUpperCase();
    return (
      <nav className="bg-white border-b border-gray-200 sticky top-0 z-30 shadow-sm w-full">
        <div className="w-full px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex w-full items-center">
              {/* Menu button always visible on desktop */}
              <button
                type="button"
                className="inline-flex items-center justify-center p-2 mr-2 rounded-md text-gray-400 hover:text-blue-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
                onClick={onMenuClick}
                aria-label="Toggle menu"
              >
                <Bars3Icon className="h-6 w-6" aria-hidden="true" />
              </button>
              {/* Brand */}
              <div className="flex items-center px-4 gap-2">
                {/* Placeholder logo/icon */}
                <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center text-white font-extrabold text-lg">N</div>
                <div className="text-xl font-extrabold text-blue-700 tracking-tight">{brandName}</div>
              </div>
              {/* Desktop navigation */}
              <div className="hidden md:ml-6 md:flex md:items-center md:space-x-2">
                {navigation.map((item, index) => (
                  <a
                    key={index}
                    href={item.href}
                    className={`inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150
                      ${item.current
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-700 hover:bg-blue-50 hover:text-blue-700'}
                    `}
                  >
                    {item.icon && <item.icon className="h-4 w-4 mr-2" />}
                    {item.label}
                    {item.badge && item.badge > 0 && (
                      <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-700">
                        {item.badge}
                      </span>
                    )}
                  </a>
                ))}
              </div>
            </div>
            <div className="flex items-center space-x-4">
              {/* Search */}
              <div className="hidden md:block">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                  </div>
                  <input
                    className="block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-lg leading-5 bg-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm shadow-sm"
                    placeholder="Search..."
                    type="search"
                  />
                </div>
              </div>
              {/* Notifications */}
              <button
                type="button"
                className="relative p-1 rounded-full text-gray-400 hover:text-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <BellIcon className="h-6 w-6" aria-hidden="true" />
                {notificationCount > 0 && (
                  <span className="absolute -top-1 -right-1 h-4 w-4 bg-blue-600 rounded-full flex items-center justify-center">
                    <span className="text-xs font-bold text-white">
                      {notificationCount > 9 ? '9+' : notificationCount}
                    </span>
                  </span>
                )}
              </button>
              {/* User menu */}
              <Menu as="div" className="relative">
                <div>
                  <Menu.Button className="flex text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    {userAvatar ? (
                      <img className="h-8 w-8 rounded-full" src={userAvatar} alt="" />
                    ) : (
                      <div className="h-8 w-8 rounded-full bg-blue-700 flex items-center justify-center text-white font-bold">
                        {getInitials(userName)}
                      </div>
                    )}
                  </Menu.Button>
                </div>
                <Transition
                  as={Fragment}
                  enter="transition ease-out duration-100"
                  enterFrom="transform opacity-0 scale-95"
                  enterTo="transform opacity-100 scale-100"
                  leave="transition ease-in duration-75"
                  leaveFrom="transform opacity-100 scale-100"
                  leaveTo="transform opacity-0 scale-95"
                >
                  <Menu.Items className="absolute right-0 z-10 mt-2 w-52 origin-top-right rounded-xl bg-white py-2 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                    <div className="px-4 py-2 border-b border-gray-100">
                      <p className="text-sm font-semibold text-gray-900">{userName}</p>
                    </div>
                    {userMenuItems.map((item, index) => (
                      <Menu.Item key={index}>
                        {({ active }) => (
                          <button
                            onClick={item.onClick}
                            className={`${
                              active ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                            } flex w-full items-center px-4 py-2 text-sm rounded-md transition-colors`}
                          >
                            {item.icon && <item.icon className="h-4 w-4 mr-3" />}
                            {item.label}
                          </button>
                        )}
                      </Menu.Item>
                    ))}
                   <div className="my-2 border-t border-gray-100" />
                   <Menu.Item>
                     {({ active }) => (
                       <button
                         onClick={() => {
                           const signOut = userMenuItems.find(i => i.label.toLowerCase().includes('sign out') || i.label.toLowerCase().includes('logout'));
                           if (signOut) signOut.onClick();
                         }}
                         className={`${
                           active ? 'bg-blue-50 text-blue-700' : 'text-red-600'
                         } flex w-full items-center px-4 py-2 text-sm rounded-md font-semibold transition-colors`}
                       >
                         <ArrowRightOnRectangleIcon className="h-4 w-4 mr-3" />
                         Sign Out
                       </button>
                     )}
                   </Menu.Item>
                  </Menu.Items>
                </Transition>
              </Menu>
            </div>
          </div>
        </div>
      </nav>
    );
  };