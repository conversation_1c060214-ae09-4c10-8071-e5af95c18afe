// =============================================================================
// INPUT COMPONENT
// =============================================================================
import { EyeIcon, EyeSlashIcon } from "@heroicons/react/24/outline";
import React from "react";
interface InputProps {
    label?: string;
    placeholder?: string;
    type?: "text" | "email" | "password" | "number" | "tel";
    value?: string;
    onChange?: (value: string) => void;
    error?: string;
    disabled?: boolean;
    required?: boolean;
    className?: string;
    showPasswordToggle?: boolean;
}

export const Input: React.FC<InputProps> = ({
    label,
    placeholder,
    type = "text",
    value,
    onChange,
    error,
    disabled = false,
    required = false,
    className = "",
    showPasswordToggle = false,
}) => {
    const [showPassword, setShowPassword] = React.useState(false);
    const inputType = type === "password" && showPassword ? "text" : type;

    return (
        <div className={className}>
            {label && (
                <label className="block text-sm font-medium text-gray-700 mb-1">
                    {label}{" "}
                    {required && <span className="text-red-500">*</span>}
                </label>
            )}
            <div className="relative">
                <input
                    className={`block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                        error ? "border-red-300" : "border-gray-300"
                    } ${disabled ? "bg-gray-50 cursor-not-allowed" : ""}`}
                    disabled={disabled}
                    placeholder={placeholder}
                    required={required}
                    type={inputType}
                    value={value}
                    onChange={(e) => onChange?.(e.target.value)}
                />
                {showPasswordToggle && type === "password" && (
                    <button
                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                    >
                        {showPassword ? (
                            <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                        ) : (
                            <EyeIcon className="h-5 w-5 text-gray-400" />
                        )}
                    </button>
                )}
            </div>
            {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
        </div>
    );
};
