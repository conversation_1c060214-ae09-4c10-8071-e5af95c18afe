"use client";
import React from 'react';

interface ProgressBarProps {
  value: number;
  max?: number;
  size?: 'sm' | 'md' | 'lg';
  color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple';
  showLabel?: boolean;
  label?: string;
  className?: string;
  animated?: boolean;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  value,
  max = 100,
  size = 'md',
  color = 'blue',
  showLabel = true,
  label,
  className = '',
  animated = true,
}) => {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100);

  const sizeClasses = {
    sm: 'h-1.5',
    md: 'h-2.5',
    lg: 'h-3',
  };

  const colorClasses = {
    blue: 'from-blue-500 to-blue-600',
    green: 'from-green-500 to-emerald-600',
    yellow: 'from-yellow-500 to-amber-600',
    red: 'from-red-500 to-rose-600',
    purple: 'from-purple-500 to-violet-600',
  };

  const bgColorClasses = {
    blue: 'bg-blue-50',
    green: 'bg-green-50',
    yellow: 'bg-yellow-50',
    red: 'bg-red-50',
    purple: 'bg-purple-50',
  };

  return (
    <div className={className}>
      {showLabel && (
        <div className="flex justify-between text-sm font-medium text-gray-700 mb-2">
          <span>{label || 'Progress'}</span>
          <span className="text-gray-900">{Math.round(percentage)}%</span>
        </div>
      )}
      <div className={`w-full ${bgColorClasses[color]} rounded-full ${sizeClasses[size]} overflow-hidden`}>
        <div
          className={`bg-gradient-to-r ${colorClasses[color]} ${sizeClasses[size]} rounded-full shadow-sm ${
            animated ? 'transition-all duration-700 ease-out' : ''
          }`}
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  );
};