// =============================================================================
// DATA TABLE COMPONENT
// =============================================================================
import { ReactNode } from "react";

interface TableColumn<T> {
    header: string;
    accessorKey: keyof T;
    cell?: (value: any, row: T) => ReactNode;
    sortable?: boolean;
    width?: string;
}

interface TableProps<T> {
    data: T[];
    columns: TableColumn<T>[];
    loading?: boolean;
    emptyMessage?: string;
    onRowClick?: (row: T) => void;
    className?: string;
}

export function DataTable<T extends Record<string, any>>({
    data,
    columns,
    loading = false,
    emptyMessage = "No data available",
    onRowClick,
    className = "",
}: TableProps<T>) {
    if (loading) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
            </div>
        );
    }

    return (
        <div
            className={`overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg ${className}`}
        >
            <table className="min-w-full divide-y divide-gray-300">
                <thead className="bg-gray-50">
                    <tr>
                        {columns.map((column, index) => (
                            <th
                                key={index}
                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                scope="col"
                                style={{ width: column.width }}
                            >
                                {column.header}
                            </th>
                        ))}
                    </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                    {data.length === 0 ? (
                        <tr>
                            <td
                                className="px-6 py-12 text-center text-gray-500"
                                colSpan={columns.length}
                            >
                                {emptyMessage}
                            </td>
                        </tr>
                    ) : (
                        data.map((row, rowIndex) => (
                            <tr
                                key={rowIndex}
                                className={`${onRowClick ? "hover:bg-gray-50 cursor-pointer" : ""}`}
                                onClick={() => onRowClick?.(row)}
                            >
                                {columns.map((column, colIndex) => (
                                    <td
                                        key={colIndex}
                                        className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                                    >
                                        {column.cell
                                            ? column.cell(
                                                  row[column.accessorKey],
                                                  row,
                                              )
                                            : row[column.accessorKey]}
                                    </td>
                                ))}
                            </tr>
                        ))
                    )}
                </tbody>
            </table>
        </div>
    );
}
