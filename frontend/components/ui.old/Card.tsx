"use client";
import React, { ReactNode } from 'react';

interface CardProps {
  children: ReactNode;
  title?: string;
  subtitle?: string;
  className?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  variant?: 'default' | 'elevated' | 'gradient' | 'glass';
  hover?: boolean;
}

export const Card: React.FC<CardProps> = ({
  children,
  title,
  subtitle,
  className = '',
  padding = 'md',
  variant = 'default',
  hover = false
}) => {
  const paddings = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
  };

  const variants = {
    default: 'bg-white border border-gray-200/60 shadow-sm',
    elevated: 'bg-white border border-gray-200/60 shadow-lg shadow-gray-900/5',
    gradient: 'bg-gradient-to-br from-white via-gray-50 to-white border border-gray-200/60 shadow-lg shadow-gray-900/5',
    glass: 'bg-white/80 backdrop-blur-lg border border-white/20 shadow-xl shadow-gray-900/10',
  };

  const hoverEffect = hover ? 'transition-all duration-300 hover:shadow-xl hover:shadow-gray-900/10 hover:-translate-y-1' : '';

  return (
    <div className={`rounded-xl ${variants[variant]} ${hoverEffect} ${className}`}>
      {(title || subtitle) && (
        <div className="border-b border-gray-100 px-6 py-5">
          {title && (
            <div className="flex items-center space-x-2">
              <h3 className="text-lg font-semibold text-gray-900 tracking-tight">{title}</h3>
              <div className="h-1.5 w-1.5 rounded-full bg-gradient-to-r from-blue-500 to-purple-500"></div>
            </div>
          )}
          {subtitle && (
            <p className="mt-1 text-sm text-gray-500 font-medium">{subtitle}</p>
          )}
        </div>
      )}
      <div className={paddings[padding]}>
        {children}
      </div>
    </div>
  );
};