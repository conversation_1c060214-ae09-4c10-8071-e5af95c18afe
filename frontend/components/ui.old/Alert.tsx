// =============================================================================
// ALERT COMPONENT
// =============================================================================
import {
    CheckCircleIcon,
    ExclamationTriangleIcon,
    InformationCircleIcon,
    XCircleIcon,
    XMarkIcon,
} from "@heroicons/react/24/outline";
import React from "react";
interface AlertProps {
    type: "success" | "error" | "warning" | "info";
    title?: string;
    message: string;
    dismissible?: boolean;
    onDismiss?: () => void;
    className?: string;
}

export const Alert: React.FC<AlertProps> = ({
    type,
    title,
    message,
    dismissible = false,
    onDismiss,
    className = "",
}) => {
    const icons = {
        success: CheckCircleIcon,
        error: XCircleIcon,
        warning: ExclamationTriangleIcon,
        info: InformationCircleIcon,
    };

    const styles = {
        success: "bg-green-50 border-green-200 text-green-800",
        error: "bg-red-50 border-red-200 text-red-800",
        warning: "bg-yellow-50 border-yellow-200 text-yellow-800",
        info: "bg-blue-50 border-blue-200 text-blue-800",
    };

    const iconStyles = {
        success: "text-green-400",
        error: "text-red-400",
        warning: "text-yellow-400",
        info: "text-blue-400",
    };

    const Icon = icons[type];

    return (
        <div className={`border rounded-md p-4 ${styles[type]} ${className}`}>
            <div className="flex">
                <div className="flex-shrink-0">
                    <Icon
                        aria-hidden="true"
                        className={`h-5 w-5 ${iconStyles[type]}`}
                    />
                </div>
                <div className="ml-3 flex-1">
                    {title && (
                        <h3 className="text-sm font-medium mb-1">{title}</h3>
                    )}
                    <p className="text-sm">{message}</p>
                </div>
                {dismissible && (
                    <div className="ml-auto pl-3">
                        <div className="-mx-1.5 -my-1.5">
                            <button
                                className={`inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 ${iconStyles[type]} hover:bg-opacity-20`}
                                type="button"
                                onClick={onDismiss}
                            >
                                <XMarkIcon
                                    aria-hidden="true"
                                    className="h-5 w-5"
                                />
                            </button>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};
