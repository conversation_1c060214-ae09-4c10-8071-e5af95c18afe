// =============================================================================
// FILE UPLOAD COMPONENT
// =============================================================================

import {
    CloudArrowUpIcon,
    EyeIcon,
    TrashIcon,
} from "@heroicons/react/24/outline";
import { useState } from "react";

import { Alert } from "./Alert";
import { Button } from "./Button";
interface UploadedFile {
    id: string;
    name: string;
    size: number;
    type: string;
    url?: string;
    uploadedAt: Date;
}

interface FileUploadProps {
    onFilesUploaded?: (files: File[]) => void;
    onFileRemove?: (fileId: string) => void;
    acceptedTypes?: string[];
    maxFileSize?: number; // in bytes
    maxFiles?: number;
    multiple?: boolean;
    uploadedFiles?: UploadedFile[];
    loading?: boolean;
    error?: string;
    className?: string;
}

export const FileUpload: React.FC<FileUploadProps> = ({
    onFilesUploaded,
    onFileRemove,
    acceptedTypes = ["image/*", "application/pdf", ".doc", ".docx"],
    maxFileSize = 10 * 1024 * 1024, // 10MB
    maxFiles = 5,
    multiple = true,
    uploadedFiles = [],
    loading = false,
    error,
    className = "",
}) => {
    const [dragActive, setDragActive] = useState(false);
    const [validationError, setValidationError] = useState<string>("");

    const validateFiles = (files: FileList): File[] => {
        const validFiles: File[] = [];
        let errorMessage = "";

        for (let i = 0; i < files.length; i++) {
            const file = files[i];

            // Check file size
            if (file.size > maxFileSize) {
                errorMessage = `File "${file.name}" is too large. Maximum size is ${(maxFileSize / 1024 / 1024).toFixed(1)}MB.`;
                continue;
            }
            // Check file type (simplified check)
            const isValidType = acceptedTypes.some((type) => {
                if (type.includes("*")) {
                    return file.type.startsWith(type.replace(/\*/g, ""));
                }

                return file.type === type || file.name.endsWith(type);
            });

            if (!isValidType) {
                errorMessage = `File type "${file.type}" is not supported.`;
                continue;
            }
            validFiles.push(file);
        }

        // Check total file count
        if (uploadedFiles.length + validFiles.length > maxFiles) {
            errorMessage = `Cannot upload more than ${maxFiles} files.`;

            return [];
        }

        setValidationError(errorMessage);

        return validFiles;
    };

    const handleFileSelect = (files: FileList | null) => {
        if (!files || files.length === 0) return;

        const validFiles = validateFiles(files);

        if (validFiles.length > 0) {
            onFilesUploaded?.(validFiles);
        }
    };

    const handleDrag = (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        if (e.type === "dragenter" || e.type === "dragover") {
            setDragActive(true);
        } else if (e.type === "dragleave") {
            setDragActive(false);
        }
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setDragActive(false);

        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
            handleFileSelect(e.dataTransfer.files);
        }
    };

    const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return "0 Bytes";
        const k = 1024;
        const sizes = ["Bytes", "KB", "MB", "GB"];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    };

    const getFileIcon = (fileType: string) => {
        if (fileType.includes("image")) return "🖼️";
        if (fileType.includes("pdf")) return "📄";
        if (fileType.includes("word") || fileType.includes("document"))
            return "📝";
        if (fileType.includes("excel") || fileType.includes("spreadsheet"))
            return "📊";

        return "📎";
    };

    return (
        <div className={`space-y-4 ${className}`}>
            {/* Upload Area */}
            <div
                className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                    dragActive
                        ? "border-blue-500 bg-blue-50"
                        : loading
                          ? "border-gray-200 bg-gray-50"
                          : "border-gray-300 hover:border-gray-400"
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
            >
                <input
                    accept={acceptedTypes.join(",")}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    disabled={loading}
                    multiple={multiple}
                    type="file"
                    onChange={(e) => handleFileSelect(e.target.files)}
                />

                <div className="space-y-2">
                    <CloudArrowUpIcon
                        className={`mx-auto h-12 w-12 ${
                            loading ? "text-gray-300" : "text-gray-400"
                        }`}
                    />

                    {loading ? (
                        <div>
                            <p className="text-sm text-gray-500">
                                Uploading files...
                            </p>
                            <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                                <div
                                    className="bg-blue-600 h-2 rounded-full animate-pulse"
                                    style={{ width: "45%" }}
                                />
                            </div>
                        </div>
                    ) : (
                        <div>
                            <p className="text-sm text-gray-600">
                                <span className="font-medium text-blue-600 hover:text-blue-500 cursor-pointer">
                                    Click to upload
                                </span>{" "}
                                or drag and drop
                            </p>
                            <p className="text-xs text-gray-500">
                                {acceptedTypes.includes("image/*") &&
                                    "Images, "}
                                PDF, DOC, DOCX files up to{" "}
                                {(maxFileSize / 1024 / 1024).toFixed(0)}MB
                            </p>
                            {multiple && (
                                <p className="text-xs text-gray-400">
                                    Maximum {maxFiles} files
                                </p>
                            )}
                        </div>
                    )}
                </div>
            </div>
            {/* Error Messages */}
            {(error || validationError) && (
                <Alert
                    dismissible
                    message={error || validationError}
                    type="error"
                    onDismiss={() => setValidationError("")}
                />
            )}

            {/* Uploaded Files List */}
            {uploadedFiles.length > 0 && (
                <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-900">
                        Uploaded Files ({uploadedFiles.length})
                    </h4>
                    {uploadedFiles.map((file) => (
                        <div
                            key={file.id}
                            className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
                        >
                            <div className="flex items-center space-x-3">
                                <span className="text-lg">
                                    {getFileIcon(file.type)}
                                </span>
                                <div>
                                    <p className="text-sm font-medium text-gray-900">
                                        {file.name}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                        {formatFileSize(file.size)} •{" "}
                                        {new Date(
                                            file.uploadedAt,
                                        ).toLocaleDateString()}
                                    </p>
                                </div>
                            </div>
                            <div className="flex items-center space-x-2">
                                {file.url && (
                                    <Button
                                        size="sm"
                                        variant="ghost"
                                        onClick={() =>
                                            window.open(file.url, "_blank")
                                        }
                                    >
                                        <EyeIcon className="h-4 w-4" />
                                    </Button>
                                )}
                                <Button
                                    className="text-red-600 hover:text-red-700"
                                    variant="ghost"
                                    onClick={() => onFileRemove?.(file.id)}
                                >
                                    <TrashIcon className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};
