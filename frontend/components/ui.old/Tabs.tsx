// =============================================================================
// TABS COMPONENT
// =============================================================================
import React, { Fragment, ReactNode } from 'react';
import { 
  Dialog, 
  Transition, 
  Menu, 
  Disclosure, 
  Listbox,
  Switch,
  RadioGroup,
  Tab
} from '@headlessui/react';
import { 
  ChevronDownIcon, 
  CheckIcon, 
  XMarkIcon,
  EyeIcon,
  EyeSlashIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  InformationCircleIcon,
  XCircleIcon,
  Bars3Icon,
  BellIcon,
  UserCircleIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline';
interface TabItem {
    id: string;
    label: string;
    content: ReactNode;
    disabled?: boolean;
  }
  
  interface TabsProps {
    tabs: TabItem[];
    defaultTab?: string;
    onChange?: (tabId: string) => void;
    className?: string;
  }
  
  export const Tabs: React.FC<TabsProps> = ({
    tabs,
    defaultTab,
    onChange,
    className = '',
  }) => {
    const [selectedIndex, setSelectedIndex] = React.useState(
      defaultTab ? tabs.findIndex(tab => tab.id === defaultTab) : 0
    );
  
    const handleTabChange = (index: number) => {
      setSelectedIndex(index);
      onChange?.(tabs[index].id);
    };
  
    return (
      <div className={className}>
        <Tab.Group selectedIndex={selectedIndex} onChange={handleTabChange}>
          <Tab.List className="flex space-x-1 rounded-xl bg-gray-100 p-1">
            {tabs.map((tab, index) => (
              <Tab
                key={tab.id}
                disabled={tab.disabled}
                className={({ selected }) =>
                  `w-full rounded-lg py-2.5 text-sm font-medium leading-5 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75 ${
                    selected
                      ? 'bg-white shadow'
                      : 'text-gray-500 hover:bg-white/[0.12] hover:text-gray-700'
                  } ${tab.disabled ? 'opacity-50 cursor-not-allowed' : ''}`
                }
              >
                {tab.label}
              </Tab>
            ))}
          </Tab.List>
          <Tab.Panels className="mt-4">
            {tabs.map((tab, index) => (
              <Tab.Panel key={tab.id} className="focus:outline-none">
                {tab.content}
              </Tab.Panel>
            ))}
          </Tab.Panels>
        </Tab.Group>
      </div>
    );
  };