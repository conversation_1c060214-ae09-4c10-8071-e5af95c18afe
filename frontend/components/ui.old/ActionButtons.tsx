// =============================================================================
// ACTION BUTTONS COMPONENT
// =============================================================================
import React, { Fragment, ReactNode } from 'react';
import { 
  Dialog, 
  Transition, 
  Menu, 
  Disclosure, 
  Listbox,
  Switch,
  RadioGroup,
  Tab
} from '@headlessui/react';
import { 
  ChevronDownIcon, 
  CheckIcon, 
  XMarkIcon,
  EyeIcon,
  EyeSlashIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  InformationCircleIcon,
  XCircleIcon,
  Bars3Icon,
  BellIcon,
  UserCircleIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline';
interface ActionButtonsProps {
    onEdit?: () => void;
    onDelete?: () => void;
    onView?: () => void;
    className?: string;
  }
  
  export const ActionButtons: React.FC<ActionButtonsProps> = ({
    onEdit,
    onDelete,
    onView,
    className = '',
  }) => {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        {onView && (
          <button
            onClick={onView}
            className="p-1 text-gray-400 hover:text-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded"
            title="View"
          >
            <EyeIcon className="h-4 w-4" />
          </button>
        )}
        {onEdit && (
          <button
            onClick={onEdit}
            className="p-1 text-gray-400 hover:text-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 rounded"
            title="Edit"
          >
            <PencilIcon className="h-4 w-4" />
          </button>
        )}
        {onDelete && (
          <button
            onClick={onDelete}
            className="p-1 text-gray-400 hover:text-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 rounded"
            title="Delete"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        )}
      </div>
    );
  };