"use client";
import {
    AlertCircleIcon,
    CheckCircleIcon,
    ClockIcon,
    EyeIcon,
} from "lucide-react";
import React from "react";

import { Button } from "@/components/ui.old/Button";
import { Card } from "@/components/ui.old/Card";
import { ProgressBar } from "@/components/ui.old/ProgressBar";
import { ApplicationPipeline } from "@/types/dashboard";

interface ApplicationPipelineProps {
    pipeline: ApplicationPipeline[];
    loading?: boolean;
}

export const ApplicationPipelineChart: React.FC<ApplicationPipelineProps> = ({
    pipeline,
    loading = false,
}) => {
    const getStageIcon = (stage: string) => {
        switch (stage.toLowerCase()) {
            case "submitted":
                return ClockIcon;
            case "under review":
                return AlertCircleIcon;
            case "pending approval":
                return ClockIcon;
            case "approved":
                return CheckCircleIcon;
            default:
                return ClockIcon;
        }
    };

    if (loading) {
        return (
            <Card
                className="animate-pulse"
                title="Application Pipeline"
                variant="elevated"
            >
                <div className="space-y-6">
                    {[1, 2, 3, 4].map((i) => (
                        <div key={i} className="space-y-3">
                            <div className="flex items-center justify-between">
                                <div className="h-5 bg-gray-200 rounded w-1/3" />
                                <div className="h-4 bg-gray-200 rounded w-1/4" />
                            </div>
                            <div className="h-2.5 bg-gray-200 rounded-full" />
                        </div>
                    ))}
                </div>
            </Card>
        );
    }

    return (
        <Card
            className="h-full"
            hover={true}
            subtitle="Track applications through workflow stages"
            title="Application Pipeline"
            variant="elevated"
        >
            <div className="space-y-6">
                {pipeline.map((stage, index) => {
                    const Icon = getStageIcon(stage.stage);

                    return (
                        <div key={index} className="group">
                            <div className="flex items-center justify-between mb-3">
                                <div className="flex items-center space-x-3">
                                    <div
                                        className={`p-2 rounded-lg ${
                                            stage.color === "green"
                                                ? "bg-green-100 text-green-600"
                                                : stage.color === "blue"
                                                  ? "bg-blue-100 text-blue-600"
                                                  : stage.color === "yellow"
                                                    ? "bg-yellow-100 text-yellow-600"
                                                    : "bg-red-100 text-red-600"
                                        }`}
                                    >
                                        <Icon className="h-4 w-4" />
                                    </div>
                                    <span className="text-sm font-semibold text-gray-900">
                                        {stage.stage}
                                    </span>
                                </div>
                                <div className="flex items-center space-x-3">
                                    <span className="text-lg font-bold text-gray-900">
                                        {stage.count}
                                    </span>
                                    <span className="text-xs font-medium text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                                        {stage.percentage}%
                                    </span>
                                </div>
                            </div>
                            <ProgressBar
                                animated={true}
                                color={stage.color}
                                showLabel={false}
                                size="md"
                                value={stage.percentage}
                            />
                        </div>
                    );
                })}

                <div className="pt-6 border-t border-gray-100">
                    <Button fullWidth size="sm" variant="outline">
                        <EyeIcon className="h-4 w-4 mr-2" />
                        View All Applications
                    </Button>
                </div>
            </div>
        </Card>
    );
};
