"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { InfoIcon } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { FeeDto, FeeCreateRequest, FeeUpdateRequest, LoadableItemDto, CurrencyDto } from "@/types";
import * as SettingsService from "@/services/SettingsService";

// Form validation schema
const feeFormSchema = z
    .object({
        name: z.string().min(1, "Fee name is required").max(255, "Fee name is too long"),
        amount: z.number().min(0.01, "Amount must be greater than 0"),
        fee_category_id: z.string().min(1, "Fee category is required"),
        currency_id: z.string().min(1, "Currency is required"),
        based_on_income: z.boolean(),
        min_income: z.number().min(0, "Minimum income must be 0 or greater").optional(),
        max_income: z.number().min(0, "Maximum income must be greater than 0").optional(),
        description: z.string().optional(),
        effective_from: z.date(),
        status: z.enum(["ACTIVE", "SCHEDULED", "EXPIRED", "DRAFT"]),
    })
    .refine(
        (data) => {
            if (data.based_on_income) {
                if (!data.min_income && data.min_income !== 0) return false;
                if (!data.max_income) return false;
                if (data.min_income >= data.max_income) return false;
            }

            return true;
        },
        {
            message: "When income-based, minimum income must be less than maximum income",
            path: ["max_income"],
        },
    );

type FeeFormData = z.infer<typeof feeFormSchema>;

interface FeeFormProps {
    fee?: FeeDto | null;
    onSubmit: (data: FeeCreateRequest | FeeUpdateRequest) => Promise<void>;
    onCancel: () => void;
    loading?: boolean;
}

export function FeeForm({ fee, onSubmit, onCancel, loading = false }: FeeFormProps) {
    const [categories, setCategories] = useState<LoadableItemDto[]>([]);
    const [currencies, setCurrencies] = useState<CurrencyDto[]>([]);
    const [loadingData, setLoadingData] = useState(true);
    const [submitError, setSubmitError] = useState<string | null>(null);

    const isEditing = !!fee;

    const form = useForm<FeeFormData>({
        resolver: zodResolver(feeFormSchema),
        defaultValues: {
            name: fee?.name || "",
            amount: fee?.amount || 0,
            fee_category_id: fee?.fee_category_id || "",
            currency_id: fee?.currency_id || "",
            based_on_income: fee?.based_on_income || false,
            min_income: fee?.min_income || 0,
            max_income: fee?.max_income || 0,
            description: fee?.description || "",
            effective_from: fee?.effective_from ? new Date(fee.effective_from) : new Date(),
            status: fee?.status || "DRAFT",
        },
    });

    const {
        watch,
        setValue,
        formState: { errors },
    } = form;
    const basedOnIncome = watch("based_on_income");

    // Load reference data
    useEffect(() => {
        const loadReferenceData = async () => {
            try {
                const [categoriesRes, currenciesRes] = await Promise.all([
                    SettingsService.fetchLoadableItems({ type: "FEE_CATEGORY", size: 100 }),
                    SettingsService.fetchCurrencies(),
                ]);

                if (categoriesRes.success) {
                    setCategories(categoriesRes.data || []);
                }

                if (currenciesRes.success) {
                    setCurrencies(currenciesRes.data || []);
                }
            } catch (err) {
                console.error("Failed to load reference data:", err);
            } finally {
                setLoadingData(false);
            }
        };

        loadReferenceData();
    }, []);

    const handleSubmit = async (data: FeeFormData) => {
        setSubmitError(null);

        try {
            const submitData = {
                ...data,
                min_income: basedOnIncome ? data.min_income || 0 : 0,
                max_income: basedOnIncome ? data.max_income || 0 : 0,
            };

            await onSubmit(submitData);
        } catch (err: any) {
            setSubmitError(err.message || "An unexpected error occurred");
        }
    };

    if (loadingData) {
        return (
            <div className="space-y-6">
                <div className="animate-pulse space-y-4">
                    <div className="h-4 bg-gray-200 rounded w-1/4" />
                    <div className="h-10 bg-gray-200 rounded" />
                    <div className="h-4 bg-gray-200 rounded w-1/3" />
                    <div className="h-10 bg-gray-200 rounded" />
                </div>
            </div>
        );
    }

    return (
        <form className="space-y-6" onSubmit={form.handleSubmit(handleSubmit)}>
            {submitError && (
                <Alert variant="destructive">
                    <InfoIcon className="h-4 w-4" />
                    <AlertDescription>{submitError}</AlertDescription>
                </Alert>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Basic Information */}
                <Card>
                    <CardHeader>
                        <CardTitle>Basic Information</CardTitle>
                        <CardDescription>Enter the basic details for this fee</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="name">Fee Name *</Label>
                            <Input
                                id="name"
                                {...form.register("name")}
                                error={errors.name?.message}
                                placeholder="e.g., Registration Fee"
                            />
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="fee_category_id">Category *</Label>
                            <Select
                                value={watch("fee_category_id")}
                                onValueChange={(value) => setValue("fee_category_id", value)}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select fee category" />
                                </SelectTrigger>
                                <SelectContent>
                                    {categories.map((category) => (
                                        <SelectItem key={category.id} value={category.id}>
                                            {category.display_value}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            {errors.fee_category_id && (
                                <p className="text-sm text-destructive">{errors.fee_category_id.message}</p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="description">Description</Label>
                            <Textarea
                                id="description"
                                {...form.register("description")}
                                placeholder="Optional description of this fee"
                                rows={3}
                            />
                        </div>
                    </CardContent>
                </Card>

                {/* Pricing Information */}
                <Card>
                    <CardHeader>
                        <CardTitle>Pricing Information</CardTitle>
                        <CardDescription>Set the amount and currency for this fee</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="currency_id">Currency *</Label>
                            <Select
                                value={watch("currency_id")}
                                onValueChange={(value) => setValue("currency_id", value)}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select currency" />
                                </SelectTrigger>
                                <SelectContent>
                                    {currencies.map((currency) => (
                                        <SelectItem key={currency.id} value={currency.id}>
                                            {currency.code} - {currency.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            {errors.currency_id && (
                                <p className="text-sm text-destructive">{errors.currency_id.message}</p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="amount">Amount *</Label>
                            <Input
                                id="amount"
                                min="0"
                                step="0.01"
                                type="number"
                                {...form.register("amount", { valueAsNumber: true })}
                                error={errors.amount?.message}
                                placeholder="0.00"
                            />
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="status">Status *</Label>
                            <Select value={watch("status")} onValueChange={(value) => setValue("status", value as any)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Select status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="DRAFT">Draft</SelectItem>
                                    <SelectItem value="ACTIVE">Active</SelectItem>
                                    <SelectItem value="SCHEDULED">Scheduled</SelectItem>
                                </SelectContent>
                            </Select>
                            {errors.status && <p className="text-sm text-destructive">{errors.status.message}</p>}
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Income-Based Pricing */}
            <Card>
                <CardHeader>
                    <CardTitle>Income-Based Pricing</CardTitle>
                    <CardDescription>Configure if this fee should vary based on organization income</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="flex items-center space-x-2">
                        <Switch
                            checked={basedOnIncome}
                            id="based_on_income"
                            onCheckedChange={(checked) => setValue("based_on_income", checked)}
                        />
                        <Label htmlFor="based_on_income">This fee varies based on organization income</Label>
                    </div>

                    {basedOnIncome && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
                            <div className="space-y-2">
                                <Label htmlFor="min_income">Minimum Income</Label>
                                <Input
                                    id="min_income"
                                    min="0"
                                    step="0.01"
                                    type="number"
                                    {...form.register("min_income", { valueAsNumber: true })}
                                    error={errors.min_income?.message}
                                    placeholder="0.00"
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="max_income">Maximum Income</Label>
                                <Input
                                    id="max_income"
                                    min="0"
                                    step="0.01"
                                    type="number"
                                    {...form.register("max_income", { valueAsNumber: true })}
                                    error={errors.max_income?.message}
                                    placeholder="0.00"
                                />
                            </div>
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Effective Date */}
            <Card>
                <CardHeader>
                    <CardTitle>Effective Date</CardTitle>
                    <CardDescription>When should this fee become effective?</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="space-y-2">
                        <Label htmlFor="effective_from">Effective From *</Label>
                        <Input
                            id="effective_from"
                            type="datetime-local"
                            {...form.register("effective_from", {
                                valueAsDate: true,
                                setValueAs: (value) => (value ? new Date(value) : new Date()),
                            })}
                            defaultValue={watch("effective_from")?.toISOString().slice(0, 16)}
                            error={errors.effective_from?.message}
                        />
                    </div>
                </CardContent>
            </Card>

            {/* Form Actions */}
            <div className="flex justify-end space-x-4 pt-6 border-t">
                <Button disabled={loading} type="button" variant="outline" onClick={onCancel}>
                    Cancel
                </Button>
                <Button disabled={loading} type="submit">
                    {loading ? "Saving..." : isEditing ? "Update Fee" : "Create Fee"}
                </Button>
            </div>
        </form>
    );
}
