import { Extension } from "@tiptap/core";
import { Plugin, Plugin<PERSON>ey } from "prosemirror-state";

interface ContentLimitOptions {
    limit: number;
    unit: "word" | "char";
    onExceedLimit?: (currentCount: number) => void;
}

const ContentLimit = Extension.create<ContentLimitOptions>({
    name: "contentLimit",

    addOptions() {
        return {
            limit: 0,
            unit: "word",
            onExceedLimit: undefined,
        };
    },

    addProseMirrorPlugins() {
        const { limit, unit, onExceedLimit } = this.options;

        if (limit === 0) {
            return [];
        }

        return [
            new Plugin({
                key: new PluginKey("contentLimit"),
                filterTransaction: (transaction, _) => {
                    const newDoc = transaction.doc;

                    const textContent = newDoc.textBetween(
                        0,
                        newDoc.content.size,
                        " ",
                        " ",
                    );

                    let currentCount: number;

                    if (unit === "char") {
                        currentCount = textContent.replace(/\s/g, "").length;
                    } else {
                        const words = textContent.split(/\s+/).filter(Boolean);

                        currentCount = words.length;
                    }

                    if (currentCount > limit) {
                        if (onExceedLimit) {
                            onExceedLimit(currentCount);
                        }

                        return false;
                    }

                    return true;
                },
            }),
        ];
    },
});

export default ContentLimit;
