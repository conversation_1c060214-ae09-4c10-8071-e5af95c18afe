"use client";

import { Check, ChevronsUpDown, Globe, Loader2 } from "lucide-react";
import { useMemo, useRef, useState } from "react";

import { Button } from "@/components/ui/button";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { fetchCountries } from "@/services/SettingsService";
import { CountryDto } from "@/types";
import { AsyncAutocompleteProps } from "@/types/input.dto";

interface CountryInputProps extends Omit<AsyncAutocompleteProps, "onSelectionChange"> {
    onSelectionChange?: (value: string | null) => void;
    onCountrySelect?: (country: CountryDto | null) => void;
}

export default function CountryInput({
    className,
    errorMessage,
    isDisabled,
    isInvalid,
    isRequired,
    label,
    placeholder = "Select a country...",
    value,
    onSelectionChange,
    onCountrySelect,
    ...props
}: CountryInputProps) {
    const [open, setOpen] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [countries, setCountries] = useState<CountryDto[]>([]);
    const [searchValue, setSearchValue] = useState("");
    const debounceTimeout = useRef<NodeJS.Timeout>();

    const selectedCountry = useMemo(() => {
        if (!value || countries.length === 0) return null;

        return countries.find((c) => c.id.toString() === value || c.name === value) || null;
    }, [value, countries]);

    const handleSearch = async (searchTerm: string) => {
        setSearchValue(searchTerm);

        if (debounceTimeout.current) {
            clearTimeout(debounceTimeout.current);
        }

        if (!searchTerm.trim()) {
            setCountries([]);

            return;
        }

        setIsLoading(true);

        debounceTimeout.current = setTimeout(async () => {
            try {
                const results = await fetchCountries({ name: searchTerm });

                if (results.data) {
                    setCountries(results.data);
                }
            } catch (error) {
                console.error("Error fetching countries:", error);
                setCountries([]);
            } finally {
                setIsLoading(false);
            }
        }, 300);
    };

    const handleSelect = (country: CountryDto) => {
        setOpen(false);
        onSelectionChange?.(country.id.toString());
        onCountrySelect?.(country);
    };

    const handleClear = () => {
        setSearchValue("");
        setCountries([]);
        onSelectionChange?.(null);
        onCountrySelect?.(null);
    };

    // Determine display state using useMemo
    const displayState = useMemo(() => {
        if (!searchValue) {
            return {
                showEmptyPrompt: true,
                showNoResults: false,
                showCountries: false,
            };
        }
        if (isLoading) {
            return {
                showEmptyPrompt: false,
                showNoResults: false,
                showCountries: false,
            };
        }

        return {
            showEmptyPrompt: false,
            showNoResults: countries.length === 0,
            showCountries: countries.length > 0,
        };
    }, [searchValue, isLoading, countries.length]);

    return (
        <div className={cn("space-y-2", className)}>
            {label && (
                <Label
                    className={cn(
                        "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
                        isRequired && "after:content-['*'] after:ml-0.5 after:text-destructive",
                    )}
                >
                    {label}
                </Label>
            )}
            <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                    <Button
                        aria-expanded={open}
                        className={cn(
                            "w-full justify-between font-normal",
                            !selectedCountry && "text-muted-foreground",
                            isInvalid && "border-destructive focus:ring-destructive",
                            isDisabled && "cursor-not-allowed opacity-50",
                        )}
                        disabled={isDisabled}
                        role="combobox"
                        variant="outline"
                    >
                        <div className="flex items-center gap-2 flex-1 min-w-0">
                            {selectedCountry ? (
                                <>
                                    <span className="text-xl flex-shrink-0">{selectedCountry.flag}</span>
                                    <span className="truncate">{selectedCountry.name}</span>
                                </>
                            ) : (
                                <>
                                    <Globe className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                                    <span className="truncate">{placeholder}</span>
                                </>
                            )}
                        </div>
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                </PopoverTrigger>

                <PopoverContent align="start" className="w-full p-0">
                    <Command shouldFilter={false}>
                        <CommandInput
                            className="h-9"
                            placeholder="Search countries..."
                            value={searchValue}
                            onValueChange={handleSearch}
                        />
                        <CommandList>
                            {isLoading && (
                                <div className="flex items-center justify-center py-6">
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                    <span className="ml-2 text-sm text-muted-foreground">Searching...</span>
                                </div>
                            )}
                            {displayState.showNoResults && (
                                <CommandEmpty>
                                    <div className="flex flex-col items-center py-6 text-center">
                                        <Globe className="h-8 w-8 text-muted-foreground mb-2" />
                                        <p className="text-sm font-medium">No countries found</p>
                                        <p className="text-xs text-muted-foreground">Try a different search term</p>
                                    </div>
                                </CommandEmpty>
                            )}
                            {displayState.showEmptyPrompt && (
                                <div className="flex flex-col items-center py-6 text-center">
                                    <Globe className="h-8 w-8 text-muted-foreground mb-2" />
                                    <p className="text-sm text-muted-foreground">Start typing to search countries</p>
                                </div>
                            )}

                            {displayState.showCountries && (
                                <CommandGroup>
                                    {countries.map((country) => (
                                        <CommandItem
                                            key={country.id}
                                            className="flex items-center gap-3 py-2 cursor-pointer"
                                            value={country.name}
                                            onSelect={() => handleSelect(country)}
                                        >
                                            <span className="text-xl flex-shrink-0">{country.flag}</span>
                                            <span className="flex-1 truncate">{country.name}</span>
                                            <Check
                                                className={cn(
                                                    "ml-auto h-4 w-4",
                                                    selectedCountry?.id === country.id ? "opacity-100" : "opacity-0",
                                                )}
                                            />
                                        </CommandItem>
                                    ))}
                                </CommandGroup>
                            )}
                        </CommandList>
                    </Command>
                </PopoverContent>
            </Popover>

            {/* Clear button when country is selected */}
            {selectedCountry && !isDisabled && (
                <Button
                    className="h-auto p-1 text-xs text-muted-foreground hover:text-foreground"
                    size="sm"
                    type="button"
                    variant="ghost"
                    onClick={handleClear}
                >
                    Clear selection
                </Button>
            )}

            {/* Error message */}
            {isInvalid && errorMessage && <p className="text-sm text-destructive">{errorMessage}</p>}
        </div>
    );
}
