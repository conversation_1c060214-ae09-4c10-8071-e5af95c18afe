"use client";

import { <PERSON><PERSON> } from "@heroui/button";
import { Tooltip } from "@heroui/tooltip";
import Link from "@tiptap/extension-link";
import TextAlign from "@tiptap/extension-text-align";
import Underline from "@tiptap/extension-underline";
import { EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import {
    AlignCenter,
    AlignLeft,
    AlignRight,
    Bold,
    Italic,
    Link as LinkIcon,
    List,
    ListOrdered,
    Quote,
    Redo,
    Strikethrough,
    Underline as UnderlineIcon,
    Undo,
} from "lucide-react";
import { useCallback } from "react";

import ContentLimit from "./extensions/context-limit";

interface TiptapEditorProps {
    content?: string;
    onChange?: (html: string) => void;
    placeholder?: string;
    editable?: boolean;
    className?: string;
    maxLimit?: number;
    limitUnit?: "word" | "char";
    onLimitExceeded?: (currentCount: number) => void;
}

export const TiptapEditor: React.FC<TiptapEditorProps> = ({
    content = "",
    onChange,
    placeholder = "Start writing...",
    editable = true,
    className = "",
    maxLimit = 250,
    limitUnit = "char",
    onLimitExceeded,
}) => {
    const editor = useEditor({
        immediatelyRender: false,
        extensions: [
            StarterKit.configure({
                bulletList: {
                    keepMarks: true,
                    keepAttributes: false,
                },
                orderedList: {
                    keepMarks: true,
                    keepAttributes: false,
                },
            }),
            Link.configure({
                openOnClick: false,
                HTMLAttributes: {
                    class: "text-blue-600 underline cursor-pointer",
                },
            }),
            TextAlign.configure({
                types: ["heading", "paragraph"],
            }),
            Underline,
            ContentLimit.configure({
                limit: maxLimit,
                unit: limitUnit,
                onExceedLimit: onLimitExceeded,
            }),
        ],
        content,
        editable,
        onUpdate: ({ editor }) => {
            if (onChange) {
                onChange(editor.getHTML());
            }
        },
        editorProps: {
            attributes: {
                class: "focus:outline-none min-h-[200px] p-4",
            },
        },
    });

    const setLink = useCallback(() => {
        if (!editor) return;

        const previousUrl = editor.getAttributes("link").href;
        const url = window.prompt("URL", previousUrl);

        if (url === null) {
            return;
        }

        if (url === "") {
            editor.chain().focus().extendMarkRange("link").unsetLink().run();

            return;
        }

        editor
            .chain()
            .focus()
            .extendMarkRange("link")
            .setLink({ href: url })
            .run();
    }, [editor]);

    if (!editor) {
        return null;
    }

    return (
        <div
            className={`border shadow m-auto max-w-2xl border-gray-300 rounded-lg ${className}`}
        >
            <div className="border-b mx-auto border-gray-200 p-2 flex flex-wrap gap-1">
                <Tooltip content="Bold">
                    <Button
                        isIconOnly
                        className={`p-2 rounded hover:bg-gray-100 ${editor.isActive("bold") ? "bg-gray-200" : ""}`}
                        disabled={
                            !editor.can().chain().focus().toggleBold().run()
                        }
                        radius="full"
                        variant="light"
                        onPress={() =>
                            editor.chain().focus().toggleBold().run()
                        }
                    >
                        <Bold size={16} />
                    </Button>
                </Tooltip>

                <Tooltip content="Italic">
                    <Button
                        isIconOnly
                        className={`p-2 rounded hover:bg-gray-100 ${editor.isActive("italic") ? "bg-gray-200" : ""}`}
                        disabled={
                            !editor.can().chain().focus().toggleItalic().run()
                        }
                        radius="full"
                        variant="light"
                        onPress={() =>
                            editor.chain().focus().toggleItalic().run()
                        }
                    >
                        <Italic size={16} />
                    </Button>
                </Tooltip>

                <Tooltip content="Underline">
                    <Button
                        isIconOnly
                        className={`p-2 rounded hover:bg-gray-100 ${editor.isActive("underline") ? "bg-gray-200" : ""}`}
                        disabled={
                            !editor
                                .can()
                                .chain()
                                .focus()
                                .toggleUnderline()
                                .run()
                        }
                        radius="full"
                        variant="light"
                        onPress={() =>
                            editor.chain().focus().toggleUnderline().run()
                        }
                    >
                        <UnderlineIcon size={16} />
                    </Button>
                </Tooltip>

                <Tooltip content="Strikethrough">
                    <Button
                        isIconOnly
                        className={`p-2 rounded hover:bg-gray-100 ${editor.isActive("strike") ? "bg-gray-200" : ""}`}
                        disabled={
                            !editor.can().chain().focus().toggleStrike().run()
                        }
                        radius="full"
                        variant="light"
                        onPress={() =>
                            editor.chain().focus().toggleStrike().run()
                        }
                    >
                        <Strikethrough size={16} />
                    </Button>
                </Tooltip>

                <div className="w-px h-8 bg-gray-300 mx-1" />

                <Tooltip content="Bullet List">
                    <Button
                        isIconOnly
                        className={`p-2 rounded hover:bg-gray-100 ${editor.isActive("bulletList") ? "bg-gray-200" : ""}`}
                        radius="full"
                        variant="light"
                        onPress={() =>
                            editor.chain().focus().toggleBulletList().run()
                        }
                    >
                        <List size={16} />
                    </Button>
                </Tooltip>

                <Tooltip content="Numbered List">
                    <Button
                        isIconOnly
                        className={`p-2 rounded hover:bg-gray-100 ${editor.isActive("orderedList") ? "bg-gray-200" : ""}`}
                        radius="full"
                        variant="light"
                        onPress={() =>
                            editor.chain().focus().toggleOrderedList().run()
                        }
                    >
                        <ListOrdered size={16} />
                    </Button>
                </Tooltip>

                <Tooltip content="Quote">
                    <Button
                        isIconOnly
                        className={`p-2 rounded hover:bg-gray-100 ${editor.isActive("blockquote") ? "bg-gray-200" : ""}`}
                        radius="full"
                        variant="light"
                        onPress={() =>
                            editor.chain().focus().toggleBlockquote().run()
                        }
                    >
                        <Quote size={16} />
                    </Button>
                </Tooltip>

                <div className="w-px h-8 bg-gray-300 mx-1" />

                <Tooltip content="Align Left">
                    <Button
                        isIconOnly
                        className={`p-2 rounded hover:bg-gray-100 ${editor.isActive({ textAlign: "left" }) ? "bg-gray-200" : ""}`}
                        radius="full"
                        variant="light"
                        onPress={() =>
                            editor.chain().focus().setTextAlign("left").run()
                        }
                    >
                        <AlignLeft size={16} />
                    </Button>
                </Tooltip>

                <Tooltip content="Align Center">
                    <Button
                        isIconOnly
                        className={`p-2 rounded hover:bg-gray-100 ${editor.isActive({ textAlign: "center" }) ? "bg-gray-200" : ""}`}
                        radius="full"
                        variant="light"
                        onPress={() =>
                            editor.chain().focus().setTextAlign("center").run()
                        }
                    >
                        <AlignCenter size={16} />
                    </Button>
                </Tooltip>

                <Tooltip content="Align Right">
                    <Button
                        isIconOnly
                        className={`p-2 rounded hover:bg-gray-100 ${editor.isActive({ textAlign: "right" }) ? "bg-gray-200" : ""}`}
                        radius="full"
                        variant="light"
                        onPress={() =>
                            editor.chain().focus().setTextAlign("right").run()
                        }
                    >
                        <AlignRight size={16} />
                    </Button>
                </Tooltip>

                <div className="w-px h-8 bg-gray-300 mx-1" />

                <Tooltip content="Add Link">
                    <Button
                        isIconOnly
                        className={`p-2 rounded hover:bg-gray-100 ${editor.isActive("link") ? "bg-gray-200" : ""}`}
                        radius="full"
                        variant="light"
                        onPress={setLink}
                    >
                        <LinkIcon size={16} />
                    </Button>
                </Tooltip>

                <div className="w-px h-8 bg-gray-300 mx-1" />

                <Tooltip content="Undo">
                    <Button
                        isIconOnly
                        className="p-2 rounded hover:bg-gray-100 disabled:opacity-50"
                        disabled={!editor.can().chain().focus().undo().run()}
                        radius="full"
                        variant="light"
                        onPress={() => editor.chain().focus().undo().run()}
                    >
                        <Undo size={16} />
                    </Button>
                </Tooltip>

                <Tooltip content="Redo">
                    <Button
                        isIconOnly
                        className="p-2 rounded hover:bg-gray-100 disabled:opacity-50"
                        disabled={!editor.can().chain().focus().redo().run()}
                        radius="full"
                        variant="light"
                        onPress={() => editor.chain().focus().redo().run()}
                    >
                        <Redo size={16} />
                    </Button>
                </Tooltip>
            </div>

            <EditorContent
                className="min-h-[200px] focus-within:outline-none [&_.ProseMirror]:outline-none [&_.ProseMirror_ul]:list-disc [&_.ProseMirror_ul]:ml-6 [&_.ProseMirror_ol]:list-decimal [&_.ProseMirror_ol]:ml-6 [&_.ProseMirror_li]:my-1 [&_.ProseMirror_blockquote]:border-l-4 [&_.ProseMirror_blockquote]:border-gray-300 [&_.ProseMirror_blockquote]:pl-4 [&_.ProseMirror_blockquote]:italic [&_.ProseMirror_h1]:text-2xl [&_.ProseMirror_h1]:font-bold [&_.ProseMirror_h1]:my-4 [&_.ProseMirror_h2]:text-xl [&_.ProseMirror_h2]:font-bold [&_.ProseMirror_h2]:my-3 [&_.ProseMirror_h3]:text-lg [&_.ProseMirror_h3]:font-bold [&_.ProseMirror_h3]:my-2 [&_.ProseMirror_p]:my-2"
                editor={editor}
            />
        </div>
    );
};
