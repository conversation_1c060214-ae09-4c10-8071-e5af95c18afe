import api from "@/config/api.config";
import http from "@/config/http";
import { HttpResponse } from "@/types";
import { OrganizationDto, OrganizationFilter } from "@/types/organization.dto";
import { httpResponse, jsonToQueryParams } from "@/utils/common";

export const fetchOrganizations = async (
    filter: Partial<OrganizationFilter>,
): Promise<HttpResponse<OrganizationDto[]>> => {
    try {
        const url = `${api.v1.organizations.root}?${jsonToQueryParams(filter)}`;
        const response = await http.get(url);

        return httpResponse<OrganizationDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};
