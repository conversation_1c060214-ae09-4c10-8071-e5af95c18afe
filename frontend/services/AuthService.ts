import api from "@/config/api.config";
import http from "@/config/http";
import { AuthDto, HttpResponse, LoginSchema, TwoFactorVerificationRequest, UserRequest } from "@/types";
import { httpResponse } from "@/utils/common";

export const login = async (data: LoginSchema): Promise<HttpResponse<AuthDto>> => {
    try {
        const response = await http.post(api.v1.auth.login, data);

        return httpResponse<AuthDto>(response);
    } catch (error: any) {
        return httpResponse<AuthDto>(error.response);
    }
};

export const register = async (data: UserRequest): Promise<HttpResponse<AuthDto>> => {
    try {
        const response = await http.post(api.v1.auth.register, data);

        return httpResponse<AuthDto>(response);
    } catch (error: any) {
        return httpResponse<AuthDto>(error.response);
    }
};

export const resendVerificationCode = async (email: string): Promise<HttpResponse<string>> => {
    try {
        const response = await http.post(api.v1.auth.resendAccountVerification, { email });

        return httpResponse<string>(response);
    } catch (error: any) {
        return httpResponse<string>(error.response);
    }
};

export const verifyTwoFA = async (data: TwoFactorVerificationRequest): Promise<HttpResponse<AuthDto>> => {
    try {
        const response = await http.post(api.v1.auth.two_factor, data);

        return httpResponse<AuthDto>(response);
    } catch (error: any) {
        return httpResponse<AuthDto>(error.response);
    }
};

export const logout = async (): Promise<HttpResponse<boolean>> => {
    try {
        const response = await http.delete(api.v1.auth.logout);

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const requestPasswordRequest = async (email: string): Promise<HttpResponse<boolean>> => {
    try {
        const response = await http.post(api.v1.auth.requestPasswordReset, { email });

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse<boolean>(e.response);
    }
};

export const resetPassword = async (token: string, password: string): Promise<HttpResponse<boolean>> => {
    try {
        const response = await http.post(api.v1.auth.resetPassword, { token, password });

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse<boolean>(e.response);
    }
};
