import api from "@/config/api.config";
import http from "@/config/http";
import {
    ActivityDto,
    ActivityFilter,
    ActivityRequest,
    HttpResponse,
} from "@/types";
import { httpResponse, jsonToQueryParams } from "@/utils/common";

export const fetchActivities = async (
    filter: Partial<ActivityFilter>,
): Promise<HttpResponse<ActivityDto[]>> => {
    try {
        const url = `${api.v1.activities.root}?${jsonToQueryParams(filter)}`;
        const response = await http.get(url);

        return httpResponse<ActivityDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const createActivity = async (
    data: ActivityRequest,
): Promise<HttpResponse<ActivityDto>> => {
    try {
        const response = await http.post(api.v1.activities.root, data);

        return httpResponse<ActivityDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const updateActivity = async (
    id: string,
    data: ActivityRequest,
): Promise<HttpResponse<ActivityDto>> => {
    try {
        const response = await http.put(api.v1.activities.action(id), data);

        return httpResponse<ActivityDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};
