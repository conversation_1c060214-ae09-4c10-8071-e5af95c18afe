import { UserFormData } from "@/components/users/user-schema";
import api from "@/config/api.config";
import http from "@/config/http";
import { HttpResponse, UserDto, UserFilter } from "@/types";
import { httpResponse, jsonToQueryParams } from "@/utils/common";

export const fetchAll = async (filter: Partial<UserFilter>): Promise<HttpResponse<UserDto[]>> => {
    try {
        if (filter.search) {
            filter.first_name = filter.search;
        }

        const url = `${api.v1.users.root}?${jsonToQueryParams(filter)}`;
        const response = await http.get(url);

        return httpResponse<UserDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const createUser = async (data: UserFormData): Promise<HttpResponse<UserDto>> => {
    try {
        const response = await http.post(api.v1.users.root, data);

        return httpResponse<UserDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const updateUser = async (data: UserFormData): Promise<HttpResponse<UserDto>> => {
    try {
        const response = await http.put(api.v1.users.root, data);

        return httpResponse<UserDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const deleteUser = async (userId: string): Promise<HttpResponse<boolean>> => {
    try {
        const response = await http.delete(api.v1.users.delete(userId));

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};
