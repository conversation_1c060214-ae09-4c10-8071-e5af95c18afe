import api from "@/config/api.config";
import http from "@/config/http";
import { <PERSON>e<PERSON><PERSON>, FeeFilter, FeeCreateRequest, FeeUpdateRequest, FeeScheduleRequest, HttpResponse } from "@/types";
import { httpResponse, jsonToQueryParams } from "@/utils/common";

export const fetchFees = async (filter: Partial<FeeFilter>): Promise<HttpResponse<FeeDto[]>> => {
    try {
        const url = `${api.v1.financial.fees.root}?${jsonToQueryParams(filter)}`;
        const response = await http.get(url);

        return httpResponse<FeeDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const getFeeById = async (id: string): Promise<HttpResponse<FeeDto>> => {
    try {
        const response = await http.get(api.v1.financial.fees.action(id));

        return httpResponse<FeeDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const createFee = async (data: FeeCreateRequest): Promise<HttpResponse<FeeDto>> => {
    try {
        const response = await http.post(api.v1.financial.fees.root, data);

        return httpResponse<FeeDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const updateFee = async (id: string, data: FeeUpdateRequest): Promise<HttpResponse<FeeDto>> => {
    try {
        const response = await http.put(api.v1.financial.fees.action(id), data);

        return httpResponse<FeeDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const deleteFee = async (id: string): Promise<HttpResponse<null>> => {
    try {
        const response = await http.delete(api.v1.financial.fees.action(id));

        return httpResponse<null>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const scheduleFeeUpdate = async (id: string, data: FeeScheduleRequest): Promise<HttpResponse<FeeDto>> => {
    try {
        const response = await http.post(api.v1.financial.fees.schedule(id), data);

        return httpResponse<FeeDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const activateFee = async (id: string): Promise<HttpResponse<FeeDto>> => {
    try {
        const response = await http.post(api.v1.financial.fees.activate(id));

        return httpResponse<FeeDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const getFeeHistory = async (categoryId: string, currencyId?: string): Promise<HttpResponse<FeeDto[]>> => {
    try {
        const params = currencyId ? `?currency_id=${currencyId}` : "";
        const url = `${api.v1.financial.fees.history(categoryId)}${params}`;
        const response = await http.get(url);

        return httpResponse<FeeDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

// Utility functions for fee data transformation
export const transformFeeForTable = (
    fee: FeeDto,
): FeeDto & {
    categoryName?: string;
    currencyDisplay?: string;
    statusBadgeColor?: string;
    incomeRangeDisplay?: string;
    effectivePeriodDisplay?: string;
} => {
    const statusColors = {
        ACTIVE: "success",
        SCHEDULED: "warning",
        EXPIRED: "secondary",
        DRAFT: "default",
    };

    const incomeRangeDisplay = fee.based_on_income
        ? `${fee.currency_code} ${fee.min_income.toLocaleString()} - ${fee.max_income.toLocaleString()}`
        : "Not income-based";

    const effectivePeriodDisplay = fee.effective_to
        ? `${new Date(fee.effective_from).toLocaleDateString()} - ${new Date(fee.effective_to).toLocaleDateString()}`
        : `From ${new Date(fee.effective_from).toLocaleDateString()}`;

    return {
        ...fee,
        categoryName: fee.category?.display_value || "Unknown Category",
        currencyDisplay: `${fee.currency_code} ${fee.amount.toLocaleString()}`,
        statusBadgeColor: statusColors[fee.status] || "default",
        incomeRangeDisplay,
        effectivePeriodDisplay,
    };
};

export const validateFeeForm = (data: Partial<FeeCreateRequest>): string[] => {
    const errors: string[] = [];

    if (!data.name?.trim()) {
        errors.push("Fee name is required");
    }

    if (!data.amount || data.amount <= 0) {
        errors.push("Amount must be greater than 0");
    }

    if (!data.fee_category_id) {
        errors.push("Fee category is required");
    }

    if (!data.currency_id) {
        errors.push("Currency is required");
    }

    if (data.based_on_income) {
        if (!data.min_income || data.min_income < 0) {
            errors.push("Minimum income must be 0 or greater");
        }

        if (!data.max_income || data.max_income <= 0) {
            errors.push("Maximum income must be greater than 0");
        }

        if (data.min_income && data.max_income && data.min_income >= data.max_income) {
            errors.push("Maximum income must be greater than minimum income");
        }
    }

    return errors;
};
