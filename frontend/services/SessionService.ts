import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";

import { siteConfig } from "@/config/site";
import { ISession, LoginResponse } from "@/types";

export const getAccessToken = (): string | null =>
    localStorage?.getItem(siteConfig.ACCESS_TOKEN_KEY);

export const getRefreshToken = (): string | null =>
    localStorage?.getItem(siteConfig.REFRESH_TOKEN_KEY);

export const setSession = (key: string, data: string): void => {
    localStorage?.setItem(key, data);
};

export const getSession = (
    key: string = siteConfig.USER_SESSION_KEY,
): ISession | null => {
    try {
        const data = localStorage?.getItem(key);

        return data ? (JSON.parse(data) as ISession) : null;
    } catch (e: unknown) {
        throw e;
    }
};

export const startSession = (
    data: LoginResponse,
    mode: "start" | "refresh" = "start",
    router?: AppRouterInstance,
) => {
    setSession(siteConfig.ACCESS_TOKEN_KEY, data.auth.access_token);
    setSession(siteConfig.REFRESH_TOKEN_KEY, data.auth.refresh_token);

    setSession(siteConfig.USER_SESSION_KEY, JSON.stringify(data));

    if (mode == "start" && !router) {
        window.location.href = "/dashboard";
    }

    if (router) {
        router.push("/dashboard");
    }
};
