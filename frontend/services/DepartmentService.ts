import api from "@/config/api.config";
import http from "@/config/http";
import {
    DepartmentDto,
    DepartmentFilter,
    DepartmentRequest,
    HttpResponse,
} from "@/types";
import { httpResponse, jsonToQueryParams } from "@/utils/common";

export const fetchActivities = async (
    filter: Partial<DepartmentFilter>,
): Promise<HttpResponse<DepartmentDto[]>> => {
    try {
        const url = `${api.v1.activities.root}?${jsonToQueryParams(filter)}`;
        const response = await http.get(url);

        return httpResponse<DepartmentDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const createActivity = async (
    data: DepartmentRequest,
): Promise<HttpResponse<DepartmentDto>> => {
    try {
        const response = await http.post(api.v1.activities.root, data);

        return httpResponse<DepartmentDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const updateActivity = async (
    id: string,
    data: DepartmentRequest,
): Promise<HttpResponse<DepartmentDto>> => {
    try {
        const response = await http.put(api.v1.activities.action(id), data);

        return httpResponse<DepartmentDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};
