# APP CONFIG
ALLOWED_HOSTS=http://localhost:4000,http://0.0.0.0:4000,http://127.0.0.1:4000
PORT=8000
APP_NAME=myNGO
SYSTEM_CODE=NGORA01
APP_VERSION=1.0.0
FRONTEND_URL=http://localhost:4000
BACKEND_URL=http://localhost:8000/v1

# DATABASE CONFIG
POSTGRES_USER=root
POSTGRES_PASSWORD=12345
POSTGRES_DB=ngora_dev_pdb
POSTGRES_HOST=db
POSTGRES_PORT=5432
# DATABASE_URL=postgresql://postgres:piper2023@localhost:5432/ngora_dev_pdb
DATABASE_URL=*******************************/ngora_dev_pdb
#DATABASE_URL=postgresql://root:12345@**********:5455/ngora_dev_pdb
# DATABASE_URL=postgresql://ngora_dev_owner:<EMAIL>/ngora_dev?sslmode=require
ENVIRONMENT=development
SSL_MODE=no

# ENCRYPTION
SECRET_KEY=LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FESytmYXluRERVeWIwMWZXNkp2RURPM3VBZgp3bzJPMTdrRHppZERIOUNSdVZ6WVppK2NNSTFqYzZKYmxKdTBOWHk0N1NudlpJS2tOaW1ONzRyQ1RhUEpXWDR4CmZibmROZ2V3MGZYRC9aSUpBM2RENHVLem1pdnVMd0ZtcHZydGFxNVZyTjltcmRvQ3NUU1RiN1JBbWlsSkZVUWQKSWt3NTc5VDRzckJsdjBTL2p3SURBUUFCCi0tLS0tRU5EIFBVQkxJQyBLRVktLS0tLQ
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRES_IN=30
REFRESH_TOKEN_EXPIRES_IN=60000

# EMAIL
MAILJET_PUBLIC_KEY=********************************
MAILJET_PRIVATE_KEY=********************************
SENDER_EMAIL=<EMAIL>
SENDER_NAME=NGORA myNGO

## STORAGE
MINIO_ROOT_USER=ngora
MINIO_ROOT_PASSWORD=ngora12345
MINIO_ENDPOINT=minio:9000
MINIO_ACCESS_KEY=ngora
MINIO_SECRET_KEY=ngora12345
MINIO_BUCKET=myngo
MINIO_SECURE=false

# REDIS CONFIG
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0

# SESSION
SESSION_ACCESS_COOKIE=ngora_access
SESSION_TYPE_KEY_COOKIE=ngora_session_type
SESSION_REFRESH_COOKIE=ngora_refresh
