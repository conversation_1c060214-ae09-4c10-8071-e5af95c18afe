import pytest
from fastapi import status
from httpx import AsyncClient


@pytest.mark.asyncio
async def test_app_starts(client: AsyncClient):
	"""
	Test that the FastAPI application starts correctly and returns a valid response.
	This tests the root endpoint which typically shows the API documentation.
	"""
	response = await client.get("/")

	# Expecting a redirect to the docs
	assert response.status_code in (status.HTTP_200_OK, status.HTTP_307_TEMPORARY_REDIRECT)

	if response.status_code == status.HTTP_307_TEMPORARY_REDIRECT:
		# Follow the redirect to docs
		assert response.headers.get("location") == "/docs"


@pytest.mark.asyncio
async def test_api_docs_accessible(client: AsyncClient):
	"""
	Test that the API documentation is accessible.
	"""
	response = await client.get("/docs")
	assert response.status_code == status.HTTP_200_OK
	assert "text/html" in response.headers.get("content-type", "")


@pytest.mark.asyncio
async def test_openapi_schema(client: AsyncClient):
	"""
	Test that the OpenAPI schema is accessible and contains expected components.
	"""
	response = await client.get("/openapi.json")
	assert response.status_code == status.HTTP_200_OK
	assert "application/json" in response.headers.get("content-type", "")

	# Check the schema contains expected components
	schema = response.json()
	assert "paths" in schema
	assert "components" in schema
	assert "info" in schema
	assert schema["info"]["title"] == "myNGO API"


@pytest.mark.asyncio
async def test_api_versioning(client: AsyncClient, monkeypatch):
	"""
	Test that API versioning is correctly implemented.
	Checks that v1 endpoints are accessible.
	"""
	# Create a mock user response for the get_current_user endpoint
	mock_user = {
		"user_id": "550e8400-e29b-41d4-a716-446655440000",
		"first_name": "Test",
		"last_name": "User",
		"username": "testuser",
		"email": "<EMAIL>",
		"status": "active",
		"created_at": "2025-05-25T14:30:00",
		"updated_at": "2025-05-25T14:30:00",
	}

	# Mock the get_current_user method to avoid authentication errors
	from src.modules.auth.auth_service import AuthService

	def mock_get_current_user(self, token):
		# If this is an invalid token, raise a 401 error
		if token == "invalid_token":
			from fastapi import HTTPException

			raise HTTPException(status_code=401, detail="Invalid token")
		return mock_user

	# Apply the mock
	monkeypatch.setattr(AuthService, "get_current_user", mock_get_current_user)

	# Check that the auth endpoint is accessible with v1 prefix
	# We don't need a valid request, just checking the route exists
	response = await client.get("/v1/auth/me", headers={"Authorization": "Bearer invalid_token"})

	# This should return 401 Unauthorized, not 404 Not Found, confirming the route exists
	assert response.status_code == status.HTTP_401_UNAUTHORIZED


@pytest.mark.asyncio
async def test_nonexistent_endpoint(client: AsyncClient):
	"""
	Test that a nonexistent endpoint returns a proper 404 error.
	"""
	response = await client.get("/nonexistent-endpoint")
	assert response.status_code == status.HTTP_404_NOT_FOUND


@pytest.mark.asyncio
async def test_health_check_endpoint_success(client: AsyncClient, monkeypatch):
	"""
	Test health check endpoint in normal/healthy state.
	Verifies that all components are properly reported and in healthy state.
	"""
	# Mock the SystemService to return controlled data
	from datetime import datetime

	from src.modules.system.system_schema import (
		HealthCheckResponse,
		HealthStatus,
		ResourceMetrics,
		ServiceHealth,
		SystemInfo,
	)
	from src.modules.system.system_service import SystemService

	# Create a mock for get_health_status
	def mock_get_health_status(self, db=None):
		return HealthCheckResponse(
			status=HealthStatus.HEALTHY,
			system=SystemInfo(version="1.0.0", environment="test", start_time=datetime.now(), uptime_seconds=60),
			services={
				"database": ServiceHealth(
					status=HealthStatus.HEALTHY,
					name="database",
					description="PostgreSQL Database",
					latency_ms=5,
					last_checked=datetime.now(),
				),
				"email": ServiceHealth(
					status=HealthStatus.HEALTHY, name="email", description="Email Service", last_checked=datetime.now()
				),
			},
			metrics=ResourceMetrics(cpu_usage_percent=10.5, memory_usage_percent=25.3, disk_usage_percent=40.2),
		)

	# Apply the mock
	monkeypatch.setattr(SystemService, "get_health_status", mock_get_health_status)

	# Test the endpoint
	response = await client.get("/health")

	# Verify the response
	assert response.status_code == status.HTTP_200_OK
	data = response.json()

	# Check BaseResponse structure
	assert "success" in data
	assert data["success"]
	assert "data" in data
	assert "errors" in data
	assert len(data["errors"]) == 0

	# Check health data structure
	health_data = data["data"]
	assert health_data["status"] == "healthy"
	assert "timestamp" in health_data
	assert "system" in health_data
	assert "services" in health_data
	assert "metrics" in health_data

	# Check system info
	system = health_data["system"]
	assert system["version"] == "1.0.0"
	assert system["environment"] == "test"
	assert "start_time" in system
	assert system["uptime_seconds"] == 60

	# Check services
	services = health_data["services"]
	assert "database" in services
	assert services["database"]["status"] == "healthy"
	assert services["database"]["name"] == "database"
	assert "email" in services
	assert services["email"]["status"] == "healthy"

	# Check metrics
	metrics = health_data["metrics"]
	assert metrics["cpu_usage_percent"] == 10.5
	assert metrics["memory_usage_percent"] == 25.3
	assert metrics["disk_usage_percent"] == 40.2


@pytest.mark.asyncio
async def test_health_check_degraded_state(client: AsyncClient, monkeypatch):
	"""
	Test health check endpoint when some services are in degraded state.
	Verifies the overall status is properly calculated.
	"""
	# Mock the SystemService to return controlled data with degraded status
	from datetime import datetime

	from src.modules.system.system_schema import (
		HealthCheckResponse,
		HealthStatus,
		ResourceMetrics,
		ServiceHealth,
		SystemInfo,
	)
	from src.modules.system.system_service import SystemService

	# Create a mock for get_health_status with degraded state
	def mock_get_health_status(self, db=None):
		return HealthCheckResponse(
			status=HealthStatus.DEGRADED,
			system=SystemInfo(version="1.0.0", environment="test", start_time=datetime.now(), uptime_seconds=60),
			services={
				"database": ServiceHealth(
					status=HealthStatus.HEALTHY,
					name="database",
					description="PostgreSQL Database",
					latency_ms=5,
					last_checked=datetime.now(),
				),
				"email": ServiceHealth(
					status=HealthStatus.DEGRADED,
					name="email",
					description="Email Service",
					error="High latency detected",
					latency_ms=500,
					last_checked=datetime.now(),
				),
			},
			metrics=ResourceMetrics(cpu_usage_percent=85.5, memory_usage_percent=90.3, disk_usage_percent=40.2),
		)

	# Apply the mock
	monkeypatch.setattr(SystemService, "get_health_status", mock_get_health_status)

	# Test the endpoint
	response = await client.get("/health")

	# Verify the response
	assert response.status_code == status.HTTP_200_OK
	data = response.json()

	# Check overall status
	assert data["success"]
	health_data = data["data"]
	assert health_data["status"] == "degraded"

	# Check degraded service
	services = health_data["services"]
	assert services["email"]["status"] == "degraded"
	assert services["email"]["error"] == "High latency detected"
	assert services["email"]["latency_ms"] == 500

	# Check high resource usage
	metrics = health_data["metrics"]
	assert metrics["cpu_usage_percent"] == 85.5
	assert metrics["memory_usage_percent"] == 90.3


@pytest.mark.asyncio
async def test_health_check_error_handling(client: AsyncClient, monkeypatch):
	"""
	Test health check endpoint's error handling.
	Verifies that errors during health check processing are properly caught and reported.
	"""
	# Mock the SystemService to raise an exception
	from src.modules.system.system_service import SystemService

	# Create a mock that raises an exception
	def mock_get_health_status_error(self, db=None):
		raise Exception("Simulated health check failure")

	# Apply the mock
	monkeypatch.setattr(SystemService, "get_health_status", mock_get_health_status_error)

	# Test the endpoint
	response = await client.get("/health")

	# Verify the response
	assert response.status_code == status.HTTP_200_OK  # We still return 200 even on errors
	data = response.json()

	# Check error response structure
	assert data["success"]
	assert data["data"] is None
	assert len(data["errors"]) > 0

	# Check error object structure
	error = data["errors"][0]
	assert isinstance(error, dict)
	assert "message" in error
	assert "code" in error

	# Check error content
	assert "Health check failed" in error["message"]
	assert "Simulated health check failure" in error["message"]
	assert error["code"] == "health_check_error"
