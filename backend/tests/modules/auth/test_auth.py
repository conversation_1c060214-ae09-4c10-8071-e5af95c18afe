from unittest.mock import patch

import pytest
from fastapi import status
from httpx import As<PERSON><PERSON><PERSON>

from src.modules.auth.auth_schema import UserResponse
from src.modules.auth.auth_service import AuthService


@pytest.mark.asyncio
async def test_register_user(client: AsyncClient, test_user_data, monkeypatch):
	"""
	Test user registration endpoint.
	"""
	# Create a mock user response object
	mock_user = UserResponse(
		user_id="550e8400-e29b-41d4-a716-446655440000",
		first_name=test_user_data["first_name"],
		last_name=test_user_data["last_name"],
		username=test_user_data["username"],
		email=test_user_data["email"],
		status="active",
		created_at="2025-05-25T14:30:00",
		updated_at="2025-05-25T14:30:00",
	)

	# Create a synchronous mock to avoid coroutine issues
	def mock_register_user(self, user_data):
		return mock_user

	# Apply the mock to the service method
	monkeypatch.setattr(AuthService, "register_user", mock_register_user)

	# Send a POST request to the register endpoint
	response = await client.post("/v1/auth/register", json=test_user_data)

	# Assert that the response has a 201 Created status code
	assert response.status_code == status.HTTP_201_CREATED

	# Assert that the returned user has the expected data
	user_data = response.json()
	assert user_data["username"] == test_user_data["username"]
	assert user_data["email"] == test_user_data["email"]


@pytest.mark.asyncio
async def test_login_user(client: AsyncClient, test_user_data):
	"""
	Test user login endpoint.
	"""
	# Mock the authenticate_user method in the auth service
	with patch("src.modules.auth.auth_service.AuthService.authenticate_user") as mock_auth:
		# Create a mock login response
		mock_login_response = {
			"success": True,
			"data": {
				"user_id": "550e8400-e29b-41d4-a716-446655440000",
				"first_name": test_user_data["first_name"],
				"last_name": test_user_data["last_name"],
				"username": test_user_data["username"],
				"email": test_user_data["email"],
				"status": "active",
				"created_at": "2025-05-25T14:30:00",
				"updated_at": "2025-05-25T14:30:00",
				"auth": {
					"access_token": "mock_access_token",
					"refresh_token": "mock_refresh_token",
					"token_type": "bearer",
				},
			},
			"errors": [],
		}

		# Configure the mock to return our login response
		mock_auth.return_value = mock_login_response

		# Send a POST request to the login endpoint with form data
		response = await client.post(
			"/v1/auth/login",
			data={"username": test_user_data["username"], "password": test_user_data["password"]},
			headers={"Content-Type": "application/x-www-form-urlencoded"},
		)

		# Assert that the response has a 200 OK status code
		assert response.status_code == status.HTTP_200_OK

		# Assert that the response contains the expected data
		login_data = response.json()
		assert login_data["success"] is True
		assert login_data["data"]["username"] == test_user_data["username"]
		assert "access_token" in login_data["data"]["auth"]

		# Verify that the service method was called with the correct credentials
		mock_auth.assert_called_once_with(test_user_data["username"], test_user_data["password"])


@pytest.mark.asyncio
async def test_request_password_reset(client: AsyncClient, monkeypatch):
	"""
	Test password reset request endpoint.
	"""

	# Create a synchronous mock initiate_password_reset method
	def mock_initiate_reset(self, email):
		# Note: using email instead of username based on controller implementation
		return None

	# Apply the mock to the service method
	monkeypatch.setattr(AuthService, "initiate_password_reset", mock_initiate_reset)

	# Send a password reset request with email (not username)
	response = await client.post("/v1/auth/request-password-reset", json={"email": "<EMAIL>"})

	# Assert the response status and message
	assert response.status_code == status.HTTP_200_OK
	assert "Reset instructions sent" in response.json()["detail"]


@pytest.mark.asyncio
async def test_get_current_user(client: AsyncClient, monkeypatch):
	"""
	Test get current user endpoint.
	"""
	# Create a mock user response
	mock_user = UserResponse(
		user_id="550e8400-e29b-41d4-a716-446655440000",
		first_name="Test",
		last_name="User",
		username="testuser",
		email="<EMAIL>",
		status="active",
		created_at="2025-05-25T14:30:00",
		updated_at="2025-05-25T14:30:00",
	)

	# Create a synchronous mock get_current_user method
	def mock_get_current_user(self, token):
		return mock_user

	# Apply the mock to the service method
	monkeypatch.setattr(AuthService, "get_current_user", mock_get_current_user)

	# Send a GET request to the /me endpoint with a mock token
	response = await client.get("/v1/auth/me", headers={"Authorization": "Bearer mock_token"})

	# Assert that the response has a 200 OK status code
	assert response.status_code == status.HTTP_200_OK

	# Assert that the returned user has the expected data
	user_data = response.json()
	assert user_data["username"] == "testuser"
	assert user_data["email"] == "<EMAIL>"

	assert user_data["email"] == "<EMAIL>"
