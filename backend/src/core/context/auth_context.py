from contextvars import ContextVar
from typing import Optional
from src.core.shared_schema import CurrentUser

current_user_context: ContextVar[Optional[CurrentUser]] = ContextVar("current_user", default=None)


def get_current_user() -> Optional[CurrentUser]:
	"""Get the current user from context"""
	return current_user_context.get()


def set_current_user(user: CurrentUser) -> None:
	"""Set the current user in context"""
	current_user_context.set(user)


def clear_current_user() -> None:
	"""Clear the current user from context"""
	current_user_context.set(None)
