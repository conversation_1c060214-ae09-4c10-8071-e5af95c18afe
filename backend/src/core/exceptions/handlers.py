import traceback

from fastapi import <PERSON><PERSON><PERSON>, <PERSON><PERSON>P<PERSON>x<PERSON>, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from pydantic import ValidationError
from sqlalchemy.exc import IntegrityError
from starlette.exceptions import HTTPException as StarletteHTTPException

from src.config.settings import IS_PRODUCTION
from src.core.exceptions.api import ApiException
from src.core.logger import logger


async def api_exception_handler(request: Request, exc: ApiException):
	"""
	Handle custom API exceptions
	"""
	logger.error(f"API exception on {request.method} {request.url}: {exc.message}")

	errors = [
		{
			"message": exc.message,
			"code": getattr(exc, "code", "api_error"),
			"status_code": getattr(exc, "status_code", 400),
		}
	]

	response_data = {
		"data": None,
		"success": False,
		"errors": errors,
	}

	return JSONResponse(status_code=getattr(exc, "status_code", 400), content=response_data)


async def attribute_error_handler(request: Request, exc: AttributeError):
	"""
	Handle AttributeError exceptions (like 'Depends' object has no attribute 'id')
	"""
	error_msg = str(exc)
	logger.error(f"Attribute error on {request.method} {request.url}: {error_msg}")

	logger.error(f"Full traceback: {traceback.format_exc()}")

	if IS_PRODUCTION:
		user_message = "Invalid request parameters"
	else:
		user_message = f"Attribute error: {error_msg}"

	errors = [
		{
			"message": user_message,
			"code": "attribute_error",
			"status_code": status.HTTP_500_INTERNAL_SERVER_ERROR,
		}
	]

	response_data = {
		"data": None,
		"success": False,
		"errors": errors,
	}

	return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=response_data)


async def value_error_handler(request: Request, exc: ValueError):
	"""
	Handle ValueError exceptions
	"""
	error_msg = str(exc)
	logger.error(f"Value error on {request.method} {request.url}: {error_msg}")

	if IS_PRODUCTION:
		user_message = "Invalid input data"
	else:
		user_message = error_msg

	errors = [
		{
			"message": user_message,
			"code": "value_error",
			"status_code": status.HTTP_400_BAD_REQUEST,
		}
	]

	response_data = {
		"data": None,
		"success": False,
		"errors": errors,
	}

	return JSONResponse(status_code=status.HTTP_400_BAD_REQUEST, content=response_data)


async def key_error_handler(request: Request, exc: KeyError):
	"""
	Handle KeyError exceptions (missing required fields)
	"""
	error_msg = str(exc)
	logger.error(f"Key error on {request.method} {request.url}: {error_msg}")

	if IS_PRODUCTION:
		user_message = "Missing required field"
	else:
		user_message = f"Missing required field: {error_msg}"

	errors = [
		{
			"message": user_message,
			"code": "key_error",
			"status_code": status.HTTP_400_BAD_REQUEST,
		}
	]

	response_data = {
		"data": None,
		"success": False,
		"errors": errors,
	}

	return JSONResponse(status_code=status.HTTP_400_BAD_REQUEST, content=response_data)


async def type_error_handler(request: Request, exc: TypeError):
	"""
	Handle TypeError exceptions
	"""
	error_msg = str(exc)
	logger.error(f"Type error on {request.method} {request.url}: {error_msg}")
	logger.error(f"Full traceback: {traceback.format_exc()}")

	if IS_PRODUCTION:
		user_message = "An internal server error occurred."
	else:
		user_message = f"Type error: {error_msg}"

	errors = [
		{
			"message": user_message,
			"code": "type_error",
			"status_code": status.HTTP_500_INTERNAL_SERVER_ERROR,
		}
	]

	response_data = {
		"data": None,
		"success": False,
		"errors": errors,
	}

	return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=response_data)


async def http_exception_handler(request: Request, exc: HTTPException):
	"""
	Handle HTTP exceptions and return a consistent response format.
	"""
	logger.error(f"HTTP exception on {request.method} {request.url}: Status={exc.status_code}, Detail={exc.detail}")

	response_status_code = exc.status_code

	if isinstance(exc.detail, list) and all(isinstance(err, dict) and "message" in err for err in exc.detail):
		errors = exc.detail
	else:
		response_error_message = exc.detail

		if exc.status_code == status.HTTP_400_BAD_REQUEST and (
			"error parsing the body" in str(exc.detail).lower() or "invalid form data" in str(exc.detail).lower()
		):
			response_status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
			response_error_message = "An internal server error occurred due to an issue with request body parsing."

		if status.HTTP_500_INTERNAL_SERVER_ERROR <= exc.status_code < 600 or IS_PRODUCTION:
			response_status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
			if IS_PRODUCTION:
				response_error_message = "An internal server error occurred."
			else:
				response_error_message = str(exc.detail)

		errors = [
			{
				"message": response_error_message,
				"code": f"http_{response_status_code}",
				"status_code": response_status_code,
			}
		]

	response_data = {
		"data": None,
		"success": False,
		"errors": errors,
	}

	return JSONResponse(status_code=response_status_code, content=response_data)


async def request_validation_exception_handler(request: Request, exc: RequestValidationError):
	"""
	Handles Pydantic validation errors specifically for incoming requests (body, query, path).
	"""
	logger.error(f"Request Validation Error: {exc.errors()}")

	status_code = status.HTTP_422_UNPROCESSABLE_ENTITY

	if request.method.upper() == "GET":
		status_code = status.HTTP_400_BAD_REQUEST

	response_data = {"data": None, "success": False, "errors": exc.errors()}

	return JSONResponse(
		status_code=status_code,
		content=response_data,
	)


async def validation_exception_handler(request: Request, exc: ValidationError):
	"""
	Handles Pydantic ValidationError that can occur during:
	"""

	logger.error(f"Pydantic Validation Error (Response): {exc.errors()}")

	response_data = {
		"data": None,
		"success": False,
		"errors": [
			{
				"message": "An internal server error occurred due to data validation issues.",
				"code": "INVALID_RESPONSE_BODY",
				"status_code": 500,
			}
		],
	}

	if not IS_PRODUCTION:
		response_data["errors"] = exc.errors()

	return JSONResponse(
		status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
		content=response_data,
	)


async def unhandled_exception_handler(request: Request, exc: Exception):
	logger.error(f"Unhandled Exception: {exc}")
	return JSONResponse(
		status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
		content={
			"data": None,
			"status": False,
			"errors": [{"message": "An unexpected internal server error occurred.", "code": "DataValidation"}],
		},
	)


async def database_integrity_exception_handler(request: Request, exc: IntegrityError):
	logger.error(f"Unhandled Exception: {exc}")
	message = "An unexpected internal server error occurred." if IS_PRODUCTION else str(exc)
	return JSONResponse(
		status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
		content={
			"data": None,
			"status": False,
			"errors": [{"message": message, "code": "DataIntegrity"}],
		},
	)


async def runtime_exception_handler(request: Request, exc: RuntimeError):
	logger.error(f"Unhandled Exception: {exc}")
	message = "An unexpected internal server error occurred." if IS_PRODUCTION else str(exc)
	return JSONResponse(
		status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
		content={
			"data": None,
			"status": False,
			"errors": [{"message": message, "code": "RuntimeError"}],
		},
	)


def register_exception_handlers(app: FastAPI):
	app.add_exception_handler(StarletteHTTPException, http_exception_handler)
	app.add_exception_handler(RequestValidationError, request_validation_exception_handler)
	app.add_exception_handler(ValidationError, validation_exception_handler)
	app.add_exception_handler(IntegrityError, database_integrity_exception_handler)
	app.add_exception_handler(RuntimeError, runtime_exception_handler)

	# HTTP exceptions
	app.add_exception_handler(HTTPException, http_exception_handler)

	# Custom API exceptions
	app.add_exception_handler(ApiException, api_exception_handler)

	# Python built-in exceptions
	app.add_exception_handler(AttributeError, attribute_error_handler)
	app.add_exception_handler(ValueError, value_error_handler)
	app.add_exception_handler(KeyError, key_error_handler)
	app.add_exception_handler(TypeError, type_error_handler)
	app.add_exception_handler(Exception, unhandled_exception_handler)
