import logging

from src.config.settings import ENVIRONMENT


def setup_internal_logger(name: str = None) -> logging.Logger:
	"""
	Set up and return a logger instance for the repository.
	Logger level is based on the environment:
	- Production: WARNING and above
	- Development: DEBUG and above
	"""
	logger = logging.getLogger(__file__ if name is None else name)

	if ENVIRONMENT == "production":
		logger.setLevel(logging.WARNING)
	else:
		logger.setLevel(logging.DEBUG)

	if not logger.handlers:
		console_handler = logging.StreamHandler()

		if ENVIRONMENT == "production":
			console_handler.setLevel(logging.WARNING)
		else:
			console_handler.setLevel(logging.DEBUG)

		formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
		console_handler.setFormatter(formatter)
		logger.addHandler(console_handler)

	return logger


internal_logger = setup_internal_logger()


def get_logger(name=__file__):
	return setup_internal_logger(name)
