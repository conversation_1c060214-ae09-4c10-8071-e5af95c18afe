from typing import Optional

from sqlalchemy.orm import Session

from src.config.db.database import get_db
from src.core.base.application_logger import ApplicationLogger
from src.core.context.auth_context import get_current_user
from src.core.shared_schema import CurrentUser


class BaseRepository(ApplicationLogger):
	def __init__(self):
		self.db: Session = get_db()
		super().__init__()

	@property
	def current_user(self) -> Optional[CurrentUser]:
		"""Get the current user from context"""
		return get_current_user()

	@property
	def current_user_id(self) -> Optional[str]:
		"""Get the current user ID from context"""
		user = self.current_user
		return user.user_id if user else None
