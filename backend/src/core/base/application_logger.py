import logging
import logging.config

LOGGING_CONFIG = {
	"version": 1,
	"disable_existing_loggers": False,  # Keeps loggers like uvicorn
	"formatters": {
		"default": {
			"format": "[%(asctime)s] %(levelname)s in %(name)s: %(message)s",
		},
	},
	"handlers": {
		"console": {
			"class": "logging.StreamHandler",
			"formatter": "default",
		},
		"file": {
			"class": "logging.FileHandler",
			"filename": "app.log",
			"formatter": "default",
		},
	},
	"root": {
		"level": "INFO",
		"handlers": ["console", "file"],
	},
}


class ApplicationLogger:
	def __init__(self):
		logging.config.dictConfig(LOGGING_CONFIG)
		self.logger = logging.getLogger(__name__)
