from datetime import datetime
from typing import Optional

from pydantic import BaseModel, UUID4, <PERSON>, validator

from src.config.db.models.currency import CurrencyStatus


class CurrencyDto(BaseModel):
    """Currency data transfer object"""
    id: UUID4
    name: str
    code: str
    exchange_rate: float
    is_default: bool
    status: CurrencyStatus
    created_at: datetime
    updated_at: datetime
    created_by: Optional[UUID4] = None
    updated_by: Optional[UUID4] = None

    class Config:
        from_attributes = True
        use_enum_values = True


class CurrencyCreateRequest(BaseModel):
    """Request model for creating a new currency"""
    name: str = Field(..., min_length=1, max_length=50, description="Currency name")
    code: str = Field(..., min_length=1, max_length=5, description="Currency code (e.g., USD, EUR)")
    exchange_rate: float = Field(..., gt=0, description="Exchange rate to base currency")
    is_default: bool = Field(default=False, description="Whether this is the default currency")
    status: CurrencyStatus = Field(default=CurrencyStatus.ACTIVE, description="Currency status")

    @validator('name')
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('Currency name cannot be empty')
        return v.strip()

    @validator('code')
    def validate_code(cls, v):
        if not v or not v.strip():
            raise ValueError('Currency code cannot be empty')
        return v.strip().upper()

    @validator('exchange_rate')
    def validate_exchange_rate(cls, v):
        if v <= 0:
            raise ValueError('Exchange rate must be greater than 0')
        return v


class CurrencyUpdateRequest(BaseModel):
    """Request model for updating an existing currency"""
    name: Optional[str] = Field(None, min_length=1, max_length=50, description="Currency name")
    code: Optional[str] = Field(None, min_length=1, max_length=5, description="Currency code")
    exchange_rate: Optional[float] = Field(None, gt=0, description="Exchange rate to base currency")
    is_default: Optional[bool] = Field(None, description="Whether this is the default currency")
    status: Optional[CurrencyStatus] = Field(None, description="Currency status")

    @validator('name')
    def validate_name(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('Currency name cannot be empty')
        return v.strip() if v else v

    @validator('code')
    def validate_code(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('Currency code cannot be empty')
        return v.strip().upper() if v else v

    @validator('exchange_rate')
    def validate_exchange_rate(cls, v):
        if v is not None and v <= 0:
            raise ValueError('Exchange rate must be greater than 0')
        return v


def to_currency_dto(currency, extras: str = "") -> CurrencyDto:
    """
    Convert Currency model to CurrencyDto
    
    Args:
        currency: Currency model instance
        extras: Comma-separated string of extra fields to include
        
    Returns:
        CurrencyDto: Converted DTO
    """
    if not currency:
        return None
    
    return CurrencyDto(
        id=currency.id,
        name=currency.name,
        code=currency.code,
        exchange_rate=currency.exchange_rate,
        is_default=currency.is_default,
        status=currency.status,
        created_at=currency.created_at,
        updated_at=currency.updated_at,
        created_by=currency.created_by,
        updated_by=currency.updated_by,
    )
