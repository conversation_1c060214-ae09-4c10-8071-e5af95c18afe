from typing import Optional

from pydantic import BaseModel, EmailStr, UUID4

from src.config.db.models import Gender, User, AccountStatus, Role


class RoleDto(BaseModel):
	id: UUID4
	code: str
	name: str
	description: str | None = None


class UserDto(BaseModel):
	id: UUID4
	first_name: str
	handle: str
	account_id: UUID4
	middle_name: Optional[str] = None
	last_name: str
	email: EmailStr
	gender: Gender
	account_status: AccountStatus
	verified: bool
	role: RoleDto | None = None
	is_external: bool


def to_role_dto(row: Role) -> RoleDto:
	return RoleDto(id=row.id, code=row.code, name=row.name, description=row.description)


def to_user_dto(row: User) -> UserDto:
	return UserDto(
		id=row.id,
		first_name=row.first_name,
		handle=row.account.handle,
		account_id=row.account.id,
		middle_name=row.middle_name,
		last_name=row.last_name,
		email=row.email,
		gender=row.gender,
		role=to_role_dto(row.roles[0]) if len(row.roles) > 0 else None,
		account_status=row.account.status,
		verified=row.verified,
		is_external=row.is_external,
	)
