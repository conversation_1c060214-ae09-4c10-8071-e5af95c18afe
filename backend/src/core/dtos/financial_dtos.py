from datetime import datetime, date
from typing import Optional, List

from pydantic import BaseModel, UUID4

from src.config.db.models import InvoiceStatus, PaymentStatus, Invoice, InvoiceDocument
from src.core.dtos.organization_dtos import OrganizationDto, to_organization_dto


class InvoiceDocumentDto(BaseModel):
	id: UUID4
	invoice_id: UUID4
	document_id: UUID4
	description: str
	filename: str
	location: str
	original_name: str
	size: str


class InvoiceDto(BaseModel):
	id: UUID4
	reference_number: str
	status: InvoiceStatus
	total_amount: float
	due_date: date
	description: str
	organization_id: UUID4
	created_at: datetime
	updated_at: datetime
	invoice_documents: List[InvoiceDocumentDto] = []
	organization: Optional[OrganizationDto] = None


class PaymentDto(BaseModel):
	id: UUID4
	amount: float
	organization_id: UUID4
	invoice_id: UUID4
	transaction_number: str
	payment_mode_id: UUID4
	status: PaymentStatus
	paid_by: str
	organization: Optional[OrganizationDto] = None
	invoice: Optional[InvoiceDto] = None
	payment_mode: str


def to_invoice_document_dto(row: InvoiceDocument) -> InvoiceDocumentDto:
	invoice_document = InvoiceDocumentDto(
		id=row.id,
		invoice_id=row.invoice_id,
		document_id=row.document_id,
		description=row.description,
		filename=row.document.filename,
		location=row.document.location,
		original_name=row.document.original_name,
		size=row.document.size,
	)

	return invoice_document


def to_invoice_dto(row: Invoice, extras: str = "") -> InvoiceDto:
	invoice = InvoiceDto(
		id=row.id,
		reference_number=row.reference_number,
		status=row.status,
		total_amount=row.total_amount,
		due_date=row.due_date,
		description=row.description,
		organization_id=row.organization_id,
		created_at=row.created_at,
		updated_at=row.updated_at,
		invoice_documents=[to_invoice_document_dto(row) for row in row.invoice_documents],
	)

	if extras:
		segments = extras.split(",")
		if "organization" in segments:
			invoice.organization = to_organization_dto(row.organization)

	return invoice
