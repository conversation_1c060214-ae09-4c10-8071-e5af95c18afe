from datetime import datetime

from pydantic import BaseModel, UUID4

from src.config.db.models import NotificationType, Notification


class NotificationDto(BaseModel):
	id: UUID4
	title: UUID4
	message: str
	type: NotificationType
	activity_id: UUID4
	sender_id: UUID4
	priority: int
	created_at: datetime
	updated_at: datetime


def to_notification_dto(row: Notification, extras: str = "") -> NotificationDto:
	notification = NotificationDto(
		id=row.id,
		title=row.title,
		message=row.message,
		type=row.type,
		activity_id=row.activity_id,
		sender_id=row.sender_id,
		priority=row.priority,
		created_at=row.created_at,
		updated_at=row.updated_at,
	)

	return notification
