import datetime
from typing import List, Optional

from pydantic import UUID4, BaseModel, ConfigDict, computed_field

from src.modules.users.users_schema import UserBaseDto
from src.modules.roles.roles_schema import RoleBaseDto
from src.config.db.models import Template
from src.config.db.models.template_stage import TemplateStage
from src.config.db.models.template_stage_role import TemplateStageRole
from src.config.db.models.template_stage_trigger import ActionMode, TemplateStageTrigger
from src.config.db.models.workflow import Workflow
from src.core.dtos.application_dtos import ApplicationDto


class TemplateStageRoleDto(BaseModel):
	model_config = ConfigDict(from_attributes=True)
  
	id: UUID4
	is_active: bool = True
	role: Optional[RoleBaseDto] = None

class TemplateStageTriggerDto(BaseModel):
	id: UUID4
	template_stage_id: UUID4
	trigger_id: UUID4
	action_mode: ActionMode
	is_active: bool = True
	trigger: Optional[str] = None


class TemplateStageDto(BaseModel):
	model_config = ConfigDict(from_attributes=True)
  
	id: UUID4
	template_id: UUID4
	name: str
	description: Optional[str] = None
	is_active: bool = True
	position: int
	triggers: List[TemplateStageTriggerDto] = []
	roles: List[TemplateStageRoleDto] = []


class WorkflowStageDto(BaseModel):
	model_config = ConfigDict(from_attributes=True)
  
	id: UUID4
	approver: Optional[UserBaseDto] = None
	created_at: datetime.datetime

class TemplateDto(BaseModel):
	model_config = ConfigDict(from_attributes=True)
  
	id: UUID4
	name: str
	code: str
	description: Optional[str] = None
	is_active: bool = True
	stages: List[TemplateStageDto] = []
 
class WorkflowDto(BaseModel):
	model_config = ConfigDict(from_attributes=True)
  
	id: UUID4
	template: Optional[TemplateDto] = None
	next_stage: Optional[TemplateStageDto]
	approvals: List[WorkflowStageDto] = []

def to_workflow_dto(row: Workflow) -> WorkflowDto:
	workflow = WorkflowDto(
		id=row.id,
		template_id=row.template_id,
		application_id=row.application_id,
		# application=to_application_dto(row.application) if row.application else None,
		template=to_template_dto(row.template) if row.template else None,
	)

	return workflow


def to_template_dto(row: Template) -> TemplateDto:
	template = TemplateDto(
		id=row.id,
		name=row.name,
		code=row.code,
		description=row.description,
		is_active=row.is_active,
	)

	return template


def to_template_stage_role_dto(row: TemplateStageRole) -> TemplateStageRoleDto:
	role = TemplateStageRoleDto(
		id=row.id,
		template_stage_id=row.template_stage_id,
		role_id=row.role_id,
		is_active=row.is_active,
	)

	return role


def to_template_stage_dto(row: TemplateStage) -> TemplateStageDto:
	stage = TemplateStageDto(
		id=row.id,
		template_id=row.template_id,
		name=row.name,
		description=row.description,
		is_active=row.is_active,
		position=row.position,
	)

	return stage


def to_template_stage_trigger_dto(row: TemplateStageTrigger) -> TemplateStageTriggerDto:
	trigger = TemplateStageTriggerDto(
		id=row.id,
		template_stage_id=row.template_stage_id,
		function_id=row.function_id,
		action_mode=row.action_mode,
		is_active=row.is_active,
		trigger=row.function.display_value if row.function else None,
	)

	return trigger
