from datetime import datetime
from typing import Optional

from pydantic import BaseModel, UUID4, ConfigDict

from src.config.db.models import ApplicationType, ApplicationStatus, Application
from src.core.dtos.organization_dtos import OrganizationDto, to_organization_dto


class ApplicationDto(BaseModel):
	model_config = ConfigDict(from_attributes=True)
  
	id: UUID4
	type: ApplicationType
	status: ApplicationStatus
	code: str
	organization: OrganizationDto = None
	created_at: datetime
	updated_at: datetime


def to_application_dto(row: Application, extras=None) -> ApplicationDto:
	application = ApplicationDto(
		id=row.id,
		type=row.type,
		status=row.status,
		code=row.code,
		organization_id=row.organization_id,
		created_at=row.created_at,
		updated_at=row.updated_at,
	)

	if extras:
		segments = extras.split(",")

		if "organization" in segments:
			application.organization = to_organization_dto(row.organization)

	return application
