from datetime import datetime

from pydantic import BaseModel, UUID4

from src.config.db.models import Complaint


class ComplaintDTO(BaseModel):
	id: UUID4
	title: str
	summary: str
	priority: int
	organization_id: UUID4
	is_anonymous: bool
	category_id: UUID4
	complainant_id: UUID4
	created_at: datetime
	updated_at: datetime


def to_complaint_dto(row: Complaint) -> ComplaintDTO:
	return ComplaintDTO(
		id=row.id,
		title=row.title,
		summary=row.summary,
		priority=row.priority,
		organization_id=row.organization_id,
		is_anonymous=row.is_anonymous,
		category_id=row.category_id,
		complainant_id=row.complainant_id,
		created_at=row.created_at,
		updated_at=row.updated_at,
	)
