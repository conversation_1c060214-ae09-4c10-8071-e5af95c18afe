from datetime import datetime
from typing import Optional

from pydantic import UUID4, BaseModel

from src.config.db.models import ActivityVisibility, Activity


class ActivityDto(BaseModel):
	id: UUID4
	title: str
	category_id: UUID4
	visibility: ActivityVisibility
	venue: str
	district_id: UUID4
	longitude: Optional[str]
	latitude: Optional[str]
	map_pin: Optional[str]
	start_date: datetime
	end_date: datetime
	summary: str
	created_at: datetime
	updated_at: datetime


def to_activity_dto(row: Activity) -> ActivityDto:
	return ActivityDto(
		id=row.id,
		title=row.title,
		category_id=row.category_id,
		visibility=row.visibility,
		venue=row.venue,
		district_id=row.district_id,
		longitude=row.longitude,
		latitude=row.latitude,
		map_pin=row.map_pin,
		start_date=row.start_date,
		end_date=row.end_date,
		summary=row.summary,
		created_at=row.created_at,
		updated_at=row.updated_at,
	)
