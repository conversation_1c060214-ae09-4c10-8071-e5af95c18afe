from datetime import datetime

from pydantic import BaseModel, UUID4

from src.config.db.models import LoadableItem, District, Region, Country


class CountryDto(BaseModel):
	id: UUID4
	name: str
	dial_code: str
	short_code: str
	flag: str


class RegionDto(BaseModel):
	id: UUID4
	name: str
	code: str
	created_at: datetime
	updated_at: datetime


class DistrictDto(BaseModel):
	id: UUID4
	name: str
	code: str
	region_id: UUID4
	created_at: datetime
	updated_at: datetime
	region_name: str


class LoadableItemDto(BaseModel):
	id: UUID4
	type: str
	code: str
	display_value: str
	description: str
	created_at: datetime
	updated_at: datetime


def to_country_dto(row: Country) -> CountryDto:
	return CountryDto(id=row.id, name=row.name, dial_code=row.dial_code, short_code=row.short_code, flag=row.flag)


def to_region_dto(row: Region) -> RegionDto:
	return RegionDto(id=row.id, name=row.name, code=row.code, created_at=row.created_at, updated_at=row.updated_at)


def to_district_dto(row: District) -> DistrictDto:
	return DistrictDto(
		id=row.id,
		name=row.name,
		code=row.code,
		region_id=row.region_id,
		region_name=row.region.name if row.region else None,
		created_at=row.created_at,
		updated_at=row.updated_at,
	)


def to_loadable_item_dto(row: LoadableItem) -> LoadableItemDto:
	return LoadableItemDto(
		id=row.id,
		type=row.type,
		code=row.code,
		display_value=row.display_value,
		description=row.description,
		created_at=row.created_at,
		updated_at=row.updated_at,
	)
