from datetime import datetime
from typing import Dict, Generic, List, Optional, TypeVar

from pydantic import UUID4, BaseModel, Field

DataT = TypeVar("DataT")


class BaseResponse(BaseModel, Generic[DataT]):
	data: Optional[DataT] = Field(default=None, description="The response payload")
	success: bool = Field(default=True, description="Indicates if the request was successful")
	errors: List[Dict[str, str]] = Field(default_factory=list, description="List of error messages")

	class Config:
		arbitrary_types_allowed = True


class BaseRequest(BaseModel):
	segments: Optional[str] = Field(default=None, description="Additional data segments")
	start_date: Optional[datetime] = Field(default=None, description="The start date to filter the data")
	end_date: Optional[datetime] = Field(default=None, description="The end date to filter the data")
	page: int = Field(default=1, ge=1, description="Current page")
	size: int = Field(default=15, ge=1, description="Number of items per page")


class FilterResponse(BaseModel, Generic[DataT]):
	data: DataT
	total_count: int


class Pagination(BaseModel, Generic[DataT]):
	data: List[Optional[DataT]] = Field(default=None, description="The response payload")
	success: bool = Field(default=True, description="Indicates if the request was successful")
	errors: List[Dict[str, str]] = Field(default_factory=list, description="List of error messages")
	page: Optional[int] = Field(default=None, description="Current page")
	size: Optional[int] = Field(default=None, description="Number of items per page")
	total: Optional[int] = Field(default=None, description="Total items count")

	@classmethod
	def from_query_result(cls, data: List[DataT], paginated_result) -> "Pagination[DataT]":
		"""
		Parse data and paginated result into a Pagination model.
		Assumes paginated_result has attributes: page, size, total
		"""
		return cls(
			data=data,
			page=getattr(paginated_result, "page", None),
			size=getattr(paginated_result, "size", None),
			total=getattr(paginated_result, "total", None),
		)


class CurrentUser(BaseModel):
	id: UUID4
	username: str
	session_token: Optional[str] = None
	role_id: Optional[UUID4] = None
	role_code: Optional[str] = None


class VoidRequest(BaseModel):
	void_reason: str
