# src/core/utils/decorators.py
from functools import wraps
from typing import Callable, ParamSpec, TypeVar

from src.core.exceptions.api import ApiException
from src.core.http.responses import bad_request

P = ParamSpec("P")
T = TypeVar("T")


def require_draft_status(organization_service_attr: str = "organization_service"):
	"""
	Decorator to ensure organization is in DRAFT status before allowing operations.

	Args:
	    organization_service_attr: Name of the organization service attribute in the handler
	"""

	def decorator(func: Callable[P, T]) -> Callable[P, T]:
		@wraps(func)
		def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
			organization_id = None

			if "organization_id" in kwargs:
				organization_id = kwargs["organization_id"]
			else:
				if args and len(args) > 0:
					organization_id = args[0]

			if not organization_id:
				raise bad_request("Organization ID is required")

			from src.config.db.models.organization import OrganizationStatus
			from src.modules.organization.organization_service import OrganizationService

			service = OrganizationService()

			try:
				organization = service.get_organization_by_id(organization_id)
				if not organization:
					raise bad_request("Organization not found")

				if organization.status != OrganizationStatus.DRAFT.value:
					raise bad_request(
						f"Operation not allowed. Organization status is '{organization.status}'. Only DRAFT organizations can be modified."
					)

				return func(*args, **kwargs)

			except ApiException as e:
				raise bad_request(e.message)

		return wrapper

	return decorator
