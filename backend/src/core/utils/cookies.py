from fastapi import Response
from src.config.settings import SESSION_ACCESS_COOKIE, COOKIE_SETTINGS, SESSION_REFRESH_COOKIE, SESSION_TYPE_KEY_COOKIE
from src.core.dtos.auth_dtos import AuthType


def set_auth_cookies(response: Response, access_token: str, refresh_token: str, auth_type: AuthType):
	response.set_cookie(key=SESSION_ACCESS_COOKIE, value=access_token, **COOKIE_SETTINGS)
	response.set_cookie(key=SESSION_REFRESH_COOKIE, value=refresh_token, **COOKIE_SETTINGS)
	response.set_cookie(
		key=SESSION_TYPE_KEY_COOKIE,
		value=auth_type.value,
		httponly=True,
		secure=COOKIE_SETTINGS["secure"],
		samesite=COOKIE_SETTINGS["samesite"],
		path="/",
	)
