from functools import wraps
from typing import Call<PERSON>, <PERSON>tional, TypeVar, Union

from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request, status
from starlette.requests import Request as StarletteRequest

from src.config.settings import SESSION_ACCESS_COOKIE
from src.core.context.auth_context import clear_current_user, set_current_user
from src.core.http.responses import unauthorized, forbidden
from src.core.logger.internal_logger import get_logger
from src.core.shared_schema import CurrentUser
from src.modules.auth.auth_service import AuthService

T = TypeVar("T")


class AuthGuard:
	"""
	Enhanced Authentication guard that provides context-aware user access
	"""

	def __init__(self):
		self.auth_service = AuthService()
		self.logger = get_logger(__name__)

	def _extract_request(self, args: tuple, kwargs: dict) -> Optional[Union[Request, StarletteRequest]]:
		"""Extract Request object from function arguments."""
		# Check positional arguments
		for arg in args:
			if isinstance(arg, (Request, StarletteRequest)):
				return arg

		for key in ["request", "_"]:
			if key in kwargs and isinstance(kwargs[key], (Request, StarletteRequest)):
				return kwargs[key]

		return None

	def _get_token_from_request(
		self, request: Union[Request, StarletteRequest], auth_type: str = "cookie"
	) -> Optional[str]:
		"""Extract token from request based on authentication type."""
		if auth_type == "cookie":
			return request.cookies.get(SESSION_ACCESS_COOKIE)
		elif auth_type == "bearer":
			auth_header = request.headers.get("Authorization")
			if auth_header and auth_header.startswith("Bearer "):
				return auth_header.split(" ")[1]
		return None

	async def _authenticate_user(self, token: str) -> CurrentUser:
		"""Authenticate user with token and return user object."""
		user = await self.auth_service.get_current_user(token)
		if user is None:
			raise unauthorized("Authentication required. Please log in to access this resource.")
		return user

	def _create_auth_decorator(
		self, require_auth: bool = True, permission: Optional[str] = None, auth_type: str = "cookie"
	) -> Callable:
		"""
		Create authentication decorator with configurable options.

		Args:
			require_auth: Whether authentication is required
			permission: Required permission (if any)
			auth_type: Type of authentication ("cookie" or "bearer")
		"""

		def decorator(func: Callable) -> Callable:
			@wraps(func)
			async def wrapper(*args, **kwargs):
				request = self._extract_request(args, kwargs)

				if not request:
					if require_auth:
						raise HTTPException(
							status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
							detail="Request object not found in decorator context",
						)
					else:
						clear_current_user()
						return await func(*args, **kwargs)

				token = self._get_token_from_request(request, auth_type)

				if not token:
					if require_auth:
						raise unauthorized("Authentication required. Please log in to access this resource.")
					else:
						clear_current_user()
						return await func(*args, **kwargs)

				try:
					user = await self._authenticate_user(token)

					if permission and not await self.has_permission(user, permission):
						raise forbidden(f"Insufficient permissions. Required: {permission}")

					set_current_user(user)
					return await func(*args, **kwargs)

				except HTTPException:
					clear_current_user()
					raise
				except Exception:
					return await func(*args, **kwargs)
				finally:
					clear_current_user()

			return wrapper

		return decorator

	def authenticated(self, func: Callable) -> Callable:
		"""
		Decorator that ensures authentication and sets user context.
		Automatically injects the current user and sets context.
		Can be used on controller methods.
		"""
		return self._create_auth_decorator(require_auth=True, auth_type="cookie")(func)

	def optional_authenticated(self, func: Callable) -> Callable:
		"""
		Decorator that optionally sets user context if authenticated.
		Does not require authentication - sets None if not authenticated.
		"""
		return self._create_auth_decorator(require_auth=False, auth_type="bearer")(func)

	def can_(self, permission: str) -> Callable:
		"""
		Decorator that ensures authentication AND specific permission.
		First authenticates the user, then checks if they have the required permission.
		"""
		return self._create_auth_decorator(require_auth=True, permission=permission, auth_type="cookie")

	async def current_user(self, token: str) -> CurrentUser:
		"""
		Dependency that retrieves and sets the current authenticated user in context.
		"""
		try:
			user = await self._authenticate_user(token)
			set_current_user(user)
			return user
		except Exception:
			clear_current_user()
			raise unauthorized("Authentication required. Please log in to access this resource.")

	async def current_user_id(self, token: str) -> int:
		"""Dependency that retrieves only the current user ID."""
		user = await self.current_user(token)
		return user.user_id

	async def has_permission(self, user: CurrentUser, permission: str) -> bool:
		"""Check if user has specific permission"""
		if not user.role_code:
			return False

		cached_permissions = await self.auth_service.cache_service.get(f"role_permissions:{user.role_code}")

		if cached_permissions is None:
			if user.role_id:
				await self.auth_service.cache_role_permissions_cache(str(user.role_id), user.role_code)
				cached_permissions = await self.auth_service.cache_service.get(f"role_permissions:{user.role_code}")

		if cached_permissions:
			if isinstance(cached_permissions, str):
				permissions = cached_permissions.split(",")
			else:
				permissions = cached_permissions
			return permission.lower() in [p.lower() for p in permissions]

		return False


auth_guard = AuthGuard()
