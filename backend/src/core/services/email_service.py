import asyncio
import datetime
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional

from jinja2 import Environment, FileSystemLoader
from mailjet_rest import Client

from src.config.settings import APP_NAME, MAILJET_PRIVATE_KEY, MAILJET_PUBLIC_KEY, SENDER_EMAIL, SENDER_NAME
from src.core.logger.internal_logger import get_logger


class EmailPriority(Enum):
	"""Email priority levels for Mailjet delivery optimization."""

	LOW = 1
	NORMAL = 2
	HIGH = 3
	CRITICAL = 4


class EmailTemplate(Enum):
	"""Predefined email templates available in the system."""

	WELCOME = "welcome_email.html"
	ACCOUNT_CONFIRMATION = "account_confirmation.html"
	EMAIL_CONFIRMED = "email_confirmed.html"
	FORGOT_PASSWORD = "forgot_password.html"
	RESET_PASSWORD = "reset_password.html"
	RESET_PASSWORD_SUCCESS = "reset_password_success.html"
	TWO_FA_CODE = "2fa_code.html"


@dataclass
class EmailAttachment:
	"""Represents an email attachment with metadata."""

	filename: str
	content: bytes
	content_type: str = "application/octet-stream"
	content_id: Optional[str] = None


@dataclass
class EmailRecipient:
	"""Enhanced recipient with personalization support."""

	email: str
	name: Optional[str] = None
	variables: Dict[str, Any] = field(default_factory=dict)


@dataclass
class EmailMessage:
	"""Comprehensive email message structure with advanced features."""

	subject: str
	recipients: List[EmailRecipient]
	sender_email: str = SENDER_EMAIL
	sender_name: Optional[str] = SENDER_NAME
	html_content: Optional[str] = None
	text_content: Optional[str] = None
	template: Optional[EmailTemplate] = None
	template_variables: Dict[str, Any] = field(default_factory=dict)
	attachments: List[EmailAttachment] = field(default_factory=list)
	reply_to: Optional[str] = None
	priority: EmailPriority = EmailPriority.NORMAL
	tracking_pixel: bool = True
	click_tracking: bool = True
	custom_headers: Dict[str, str] = field(default_factory=dict)
	tags: List[str] = field(default_factory=list)
	campaign_id: Optional[str] = None


class EmailService:
	"""
	Email service currently using Mailjet:
	- Template rendering with Jinja2
	- batch processing
	- Comprehensive error handling
	- Delivery tracking and analytics
	- Rate limiting and retry mechanisms
	"""

	def __init__(self):
		self.logger = get_logger(__name__)
		self._client = None
		self._template_env = None
		self._initialize_client()
		self._initialize_template_engine()

	def _initialize_client(self) -> None:
		"""Initialize Mailjet client with secure configuration."""
		try:
			if not MAILJET_PUBLIC_KEY or not MAILJET_PRIVATE_KEY:
				raise ValueError("Mailjet API credentials are not configured")

			self._client = Client(auth=(MAILJET_PUBLIC_KEY, MAILJET_PRIVATE_KEY), version="v3.1")

			# self._validate_credentials()
			self.logger.info("Mailjet client initialized successfully")

		except Exception as e:
			self.logger.error(f"Failed to initialize Mailjet client: {str(e)}")
			raise

	def _initialize_template_engine(self) -> None:
		"""Initialize Jinja2 template engine for email templates."""
		try:
			template_path = Path("src/config/templates/emails")
			if template_path.exists():
				self._template_env = Environment(
					loader=FileSystemLoader(str(template_path)), autoescape=True, trim_blocks=True, lstrip_blocks=True
				)
				self.logger.info("Template engine initialized successfully")
			else:
				self.logger.warning(f"Template directory not found: {template_path}")

		except Exception as e:
			self.logger.error(f"Failed to initialize template engine: {str(e)}")

	def _validate_credentials(self) -> None:
		"""Validate Mailjet API credentials."""
		try:
			result = self._client.account.get()
			if result.status_code != 200:
				raise ValueError("Invalid Mailjet API credentials")
		except Exception as e:
			raise ValueError(f"Credential validation failed: {str(e)}")

	def send_email(self, message: EmailMessage) -> Dict[str, Any]:
		"""
		Send a single email with comprehensive error handling.

		Args:
			message: EmailMessage instance with all email details

		Returns:
			Dict containing delivery status and tracking information
		"""
		try:
			self.logger.info(f"Preparing to send email to {len(message.recipients)} recipients")

			if message.template and not message.html_content:
				# message.template_variables.setdefault("year", datetime.datetime.now().year)
				# message.template_variables.setdefault("app_name", APP_NAME)
				message.html_content = self._render_template(message.template, message.template_variables)

			payload = self._build_mailjet_payload(message)
			result = self._client.send.create(data=payload)

			return self._process_send_response(result, message)

		except Exception as e:
			self.logger.error(f"Email sending failed: {str(e)}")
			return {"success": False, "error": str(e), "message_id": None, "delivery_status": "failed"}

	def send_bulk_emails(
		self, messages: List[EmailMessage], batch_size: int = 50, delay_between_batches: float = 1.0
	) -> List[Dict[str, Any]]:
		"""
		Send multiple emails in optimized batches with rate limiting.

		Args:
			messages: List of EmailMessage instances
			batch_size: Number of emails per batch
			delay_between_batches: Delay in seconds between batches

		Returns:
			List of delivery results for each email
		"""
		results = []
		total_messages = len(messages)

		self.logger.info(f"Starting bulk email send: {total_messages} messages in batches of {batch_size}")

		for i in range(0, total_messages, batch_size):
			batch = messages[i : i + batch_size]
			batch_number = (i // batch_size) + 1
			total_batches = (total_messages + batch_size - 1) // batch_size

			self.logger.info(f"Processing batch {batch_number}/{total_batches}")

			batch_tasks = [self.send_email(message) for message in batch]
			batch_results = asyncio.gather(*batch_tasks, return_exceptions=True)

			for result in batch_results:
				if isinstance(result, Exception):
					results.append(
						{"success": False, "error": str(result), "message_id": None, "delivery_status": "failed"}
					)
				else:
					results.append(result)

			if i + batch_size < total_messages:
				asyncio.sleep(delay_between_batches)

		success_count = sum(1 for r in results if r.get("success", False))
		self.logger.info(f"Bulk email completed: {success_count}/{total_messages} successful")

		return results

	def send_template_email(
		self,
		template: EmailTemplate,
		recipients: List[EmailRecipient],
		subject: str,
		sender_email: str,
		template_variables: Dict[str, Any] = None,
		sender_name: Optional[str] = None,
		**kwargs,
	) -> Dict[str, Any]:
		"""
		Convenience method for sending template-based emails.

		Args:
			template: EmailTemplate enum value
			recipients: List of email recipients
			subject: Email subject line
			sender_email: Sender email address
			template_variables: Variables for template rendering
			sender_name: Optional sender name
			**kwargs: Additional EmailMessage parameters

		Returns:
			Delivery result dictionary
		"""
		message = EmailMessage(
			subject=subject,
			recipients=recipients,
			sender_email=sender_email,
			sender_name=sender_name,
			template=template,
			template_variables=template_variables or {},
			**kwargs,
		)
		message.template_variables.setdefault("year", datetime.datetime.now().year)
		message.template_variables.setdefault("app_name", APP_NAME)

		return self.send_email(message)

	def _render_template(self, template: EmailTemplate, variables: Dict[str, Any]) -> str:
		"""
		Render email template with provided variables.

		Args:
			template: EmailTemplate enum value
			variables: Template variables dictionary

		Returns:
			Rendered HTML content
		"""
		if not self._template_env:
			raise ValueError("Template engine not initialized")

		try:
			jinja_template = self._template_env.get_template(template.value)
			return jinja_template.render(**variables)

		except Exception as e:
			self.logger.error(f"Template rendering failed for {template.value}: {str(e)}")
			raise ValueError(f"Template rendering failed: {str(e)}")

	def _build_mailjet_payload(self, message: EmailMessage) -> Dict[str, Any]:
		"""
		Build Mailjet API payload from EmailMessage.

		Args:
			message: EmailMessage instance

		Returns:
			Mailjet-compatible payload dictionary
		"""
		to_list = []
		for recipient in message.recipients:
			recipient_data = {"Email": recipient.email}
			if recipient.name:
				recipient_data["Name"] = recipient.name
			if recipient.variables:
				recipient_data["Vars"] = recipient.variables
			to_list.append(recipient_data)

		mailjet_message = {
			"From": {"Email": message.sender_email, "Name": message.sender_name or message.sender_email},
			"To": to_list,
			"Subject": message.subject,
			"Priority": message.priority.value,
			"TrackOpens": "enabled" if message.tracking_pixel else "disabled",
			"TrackClicks": "enabled" if message.click_tracking else "disabled",
		}

		if message.html_content:
			mailjet_message["HTMLPart"] = message.html_content
		if message.text_content:
			mailjet_message["TextPart"] = message.text_content

		if message.reply_to:
			mailjet_message["ReplyTo"] = {"Email": message.reply_to}

		if message.custom_headers:
			mailjet_message["Headers"] = message.custom_headers

		if message.tags:
			mailjet_message["Tags"] = message.tags

		if message.campaign_id:
			mailjet_message["CustomCampaign"] = message.campaign_id

		if message.attachments:
			attachments = []
			inline_attachments = []

			for attachment in message.attachments:
				attachment_data = {
					"ContentType": attachment.content_type,
					"Filename": attachment.filename,
					"Base64Content": self._encode_attachment(attachment.content),
				}

				if attachment.content_id:
					attachment_data["ContentID"] = attachment.content_id
					inline_attachments.append(attachment_data)
				else:
					attachments.append(attachment_data)

			if attachments:
				mailjet_message["Attachments"] = attachments
			if inline_attachments:
				mailjet_message["InlinedAttachments"] = inline_attachments

		return {"Messages": [mailjet_message]}

	def _encode_attachment(self, content: bytes) -> str:
		"""Encode attachment content to base64."""
		import base64

		return base64.b64encode(content).decode("utf-8")

	def _process_send_response(self, result, message: EmailMessage) -> Dict[str, Any]:
		"""
		Process Mailjet send response and extract relevant information.

		Args:
			result: Mailjet API response
			message: Original EmailMessage

		Returns:
			Processed response dictionary
		"""
		try:
			if result.status_code == 200:
				response_data = result.json()
				message_info = response_data.get("Messages", [{}])[0]

				return {
					"success": True,
					"message_id": message_info.get("MessageID"),
					"message_uuid": message_info.get("MessageUUID"),
					"message_href": message_info.get("MessageHref"),
					"delivery_status": "sent",
					"recipient_count": len(message.recipients),
					"timestamp": message_info.get("SubmittedAt"),
				}
			else:
				error_data = result.json() if hasattr(result, "json") else {}
				error_message = error_data.get("ErrorMessage", f"HTTP {result.status_code}")

				self.logger.error(f"Mailjet API error: {error_message}")

				return {
					"success": False,
					"error": error_message,
					"status_code": result.status_code,
					"message_id": None,
					"delivery_status": "failed",
				}

		except Exception as e:
			self.logger.error(f"Response processing failed: {str(e)}")
			return {
				"success": False,
				"error": f"Response processing failed: {str(e)}",
				"message_id": None,
				"delivery_status": "failed",
			}

	def get_delivery_status(self, message_id: str) -> Dict[str, Any]:
		"""
		Get delivery status for a specific message.

		Args:
			message_id: Mailjet message ID

		Returns:
			Delivery status information
		"""
		try:
			result = self._client.message.get(id=message_id)

			if result.status_code == 200:
				data = result.json().get("Data", [{}])[0]
				return {
					"success": True,
					"message_id": message_id,
					"status": data.get("Status"),
					"delivered_at": data.get("ArrivedAt"),
					"opened_at": data.get("OpenedAt"),
					"clicked_at": data.get("ClickedAt"),
					"bounce_reason": data.get("StateDescription"),
				}
			else:
				return {
					"success": False,
					"error": f"Failed to get status: HTTP {result.status_code}",
					"message_id": message_id,
				}

		except Exception as e:
			self.logger.error(f"Status check failed for message {message_id}: {str(e)}")
			return {"success": False, "error": str(e), "message_id": message_id}

	def get_client_stats(self) -> Dict[str, Any]:
		"""
		Get Mailjet account statistics and limits.

		Returns:
			Account statistics dictionary
		"""
		try:
			result = self._client.account.get()

			if result.status_code == 200:
				data = result.json().get("Data", [{}])[0]
				return {
					"success": True,
					"account_id": data.get("ID"),
					"email_address": data.get("Email"),
					"username": data.get("Username"),
					"credits_remaining": data.get("CreditsRemaining"),
					"max_allowed_api_keys": data.get("MaxAllowedAPIKeys"),
					"locale": data.get("Locale"),
				}
			else:
				return {"success": False, "error": f"Failed to get stats: HTTP {result.status_code}"}

		except Exception as e:
			self.logger.error(f"Stats retrieval failed: {str(e)}")
			return {"success": False, "error": str(e)}


# Singleton instance for dependency injection
email_service = EmailService()


def send_welcome_email(
	recipient_email: str, recipient_name: str, sender_email: str, template_vars: dict
) -> Dict[str, Any]:
	"""Send welcome email using predefined template."""

	return email_service.send_template_email(
		template=EmailTemplate.WELCOME,
		recipients=[EmailRecipient(email=recipient_email, name=recipient_name)],
		subject=f"Welcome to {APP_NAME}!",
		sender_email=sender_email,
		template_variables=template_vars,
	)


def send_2fa_code(
	recipient_email: str,
	code: str,
	sender_email: str,
	username=str,
	expires_in_minutes: int = 15,
) -> Dict[str, Any]:
	"""Send 2FA code email."""

	return email_service.send_template_email(
		template=EmailTemplate.TWO_FA_CODE,
		recipients=[EmailRecipient(email=recipient_email)],
		subject="Your Security Code",
		sender_email=sender_email,
		template_variables={
			"code": code,
			"username": username,
			"expires_in_minutes": expires_in_minutes,
			"year": datetime.now().year,
		},
		priority=EmailPriority.HIGH,
	)


def send_password_reset(
	recipient_email: str, reset_link: str, sender_email: str, expires_in_hours: int = 24
) -> Dict[str, Any]:
	"""Send password reset email."""

	return email_service.send_template_email(
		template=EmailTemplate.RESET_PASSWORD,
		recipients=[EmailRecipient(email=recipient_email)],
		subject="Password Reset Request",
		sender_email=sender_email,
		template_variables={
			"reset_link": reset_link,
			"expires_in_hours": expires_in_hours,
			"year": datetime.now().year,
		},
		priority=EmailPriority.HIGH,
	)
