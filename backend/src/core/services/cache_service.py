import json
import pickle
from datetime import <PERSON><PERSON><PERSON>
from typing import Any, Dict, List, Optional, Union

from src.config.redis_config import get_redis_config
from src.core.logger.internal_logger import get_logger


class CacheService:
	"""Redis implementation of cache service"""

	def __init__(self):
		"""
		Initializes the CacheService with an optional Redis client.
		If no client is provided, it uses the client from redis_config.
		"""
		redis_config_instance = get_redis_config()

		if redis_config_instance is None:
			raise RuntimeError("Redis configuration not initialized. Call initialize_redis_config() first.")

		self.redis_client = redis_config_instance.get_redis_client()
		self._connected = False
		self.logger = get_logger(__name__)

	async def _ensure_connected(self) -> None:
		"""Ensure Redis connection is established"""
		if not self._connected:
			try:
				await self.redis_client.ping()
				self._connected = True
			except Exception as e:
				self.logger.error(f"Failed to connect to Redis: {e}")
				raise ConnectionError(f"Failed to connect to Redis: {e}")

	def _serialize_value(self, value: Any) -> Union[str, bytes]:
		"""
		Serializes a value for storage in Redis.
		Uses JSON for basic types (str, int, float, bool, None, list, dict) and pickle for others.
		"""
		if isinstance(value, (str, int, float, bool, type(None), list, dict)):
			try:
				return json.dumps(value).encode("utf-8")
			except (TypeError, ValueError):
				return pickle.dumps(value)
		else:
			return pickle.dumps(value)

	def _deserialize_value(self, value: Union[str, bytes]) -> Any:
		"""
		Deserializes a value retrieved from Redis.
		Attempts to deserialize as JSON first, then as pickle.
		"""
		if isinstance(value, bytes):
			try:
				decoded_value = value.decode("utf-8")
				return json.loads(decoded_value)
			except (UnicodeDecodeError, json.JSONDecodeError):
				try:
					return pickle.loads(value)
				except Exception as e_pickle:
					self.logger.error(f"Both JSON and pickle deserialization failed: {e_pickle}")
					return value
		else:
			try:
				return json.loads(value)
			except json.JSONDecodeError:
				self.logger.error("JSON deserialization failed on string value")
				return value

	async def get(self, key: str) -> Optional[Any]:
		"""
		Gets a value from the Redis cache.

		Args:
			key (str): The key of the value to retrieve.

		Returns:
			Optional[Any]: The deserialized value if found, otherwise None.
		"""
		await self._ensure_connected()
		try:
			value = await self.redis_client.get(key)

			if value is None:
				return None
			return self._deserialize_value(value)
		except Exception as e:
			self.logger.error(f"Failed to get key '{key}' from cache: {e}")
			raise RuntimeError(f"Failed to get key '{key}' from cache: {e}")

	async def set(self, key: str, value: Any, expire: Optional[Union[int, timedelta]] = None) -> bool:
		"""
		Sets a value in the Redis cache with an optional expiration.

		Args:
			key (str): The key to set.
			value (Any): The value to store.
			expire (Optional[Union[int, timedelta]]): Expiration time in seconds or as a timedelta object.

		Returns:
			bool: True if the value was set successfully, False otherwise.
		"""
		await self._ensure_connected()
		try:
			serialized_value = self._serialize_value(value)
			if expire:
				if isinstance(expire, timedelta):
					expire_seconds = int(expire.total_seconds())
				else:
					expire_seconds = expire
				result = await self.redis_client.setex(key, expire_seconds, serialized_value)
			else:
				result = await self.redis_client.set(key, serialized_value)
			return bool(result)
		except Exception as e:
			self.logger.error(f"Failed to set key '{key}' in cache: {e}")
			raise RuntimeError(f"Failed to set key '{key}' in cache: {e}")

	async def delete(self, key: str) -> bool:
		"""
		Deletes a key from the Redis cache.

		Args:
			key (str): The key to delete.

		Returns:
			bool: True if the key was deleted successfully, False otherwise.
		"""
		await self._ensure_connected()
		try:
			result = await self.redis_client.delete(key)
			return bool(result)
		except Exception as e:
			self.logger.error(f"Failed to delete key '{key}' from cache: {e}")
			raise RuntimeError(f"Failed to delete key '{key}' from cache: {e}")

	async def exists(self, key: str) -> bool:
		"""
		Checks if a key exists in the Redis cache.

		Args:
			key (str): The key to check.

		Returns:
			bool: True if the key exists, False otherwise.
		"""
		await self._ensure_connected()
		try:
			result = await self.redis_client.exists(key)
			return bool(result)
		except Exception as e:
			self.logger.error(f"Failed to check existence of key '{key}' in cache: {e}")
			raise RuntimeError(f"Failed to check existence of key '{key}' in cache: {e}")

	async def clear(self) -> bool:
		"""
		Clears all keys from the Redis cache.

		Returns:
			bool: True if the cache was cleared successfully, False otherwise.
		"""
		await self._ensure_connected()
		try:
			result = await self.redis_client.flushdb()
			return bool(result)
		except Exception as e:
			self.logger.error(f"Failed to clear cache: {e}")
			raise RuntimeError(f"Failed to clear cache: {e}")

	async def close(self) -> None:
		"""
		Closes the Redis connection.
		"""
		if self.redis_client:
			try:
				await self.redis_client.close()
				self._connected = False
			except Exception as e:
				self.logger.error(f"Failed to close Redis connection: {e}")
				# For close, logging might be enough as the connection might already be broken.
				pass

	async def get_many(self, keys: List[str]) -> Dict[str, Any]:
		"""
		Gets multiple values from the cache.

		Args:
			keys (List[str]): A list of keys to retrieve.

		Returns:
			Dict[str, Any]: A dictionary mapping existing keys to their deserialized values.
		"""
		await self._ensure_connected()
		try:
			values = await self.redis_client.mget(keys)
			result = {}
			for key, value in zip(keys, values):
				if value is not None:
					result[key] = self._deserialize_value(value)
			return result
		except Exception as e:
			self.logger.error(f"Failed to get multiple keys from cache: {e}")
			raise RuntimeError(f"Failed to get multiple keys from cache: {e}")

	async def set_many(self, mapping: Dict[str, Any], expire: Optional[Union[int, timedelta]] = None) -> bool:
		"""
		Sets multiple values in the cache with an optional expiration.

		Args:
			mapping (Dict[str, Any]): A dictionary of key-value pairs to set.
			expire (Optional[Union[int, timedelta]]): Expiration time in seconds or as a timedelta object.

		Returns:
			bool: True if all values were set successfully, False otherwise.
		"""
		await self._ensure_connected()
		try:
			pipeline = self.redis_client.pipeline()
			for key, value in mapping.items():
				serialized_value = self._serialize_value(value)
				if expire:
					if isinstance(expire, timedelta):
						expire_seconds = int(expire.total_seconds())
					else:
						expire_seconds = expire
					pipeline.setex(key, expire_seconds, serialized_value)
				else:
					pipeline.set(key, serialized_value)
			results = await pipeline.execute()
			return all(results)
		except Exception as e:
			self.logger.error(f"Failed to set multiple keys in cache: {e}")
			raise RuntimeError(f"Failed to set multiple keys in cache: {e}")

	async def increment(self, key: str, amount: int = 1) -> int:
		"""
		Increments a numeric value in the cache. If the key does not exist, it's set to 'amount'.

		Args:
			key (str): The key of the numeric value to increment.
			amount (int): The amount to increment by (default is 1).

		Returns:
			int: The new value after incrementing.
		"""
		await self._ensure_connected()
		try:
			result = await self.redis_client.incrby(key, amount)
			return int(result)
		except Exception as e:
			self.logger.error(f"Failed to increment key '{key}' in cache: {e}")
			raise RuntimeError(f"Failed to increment key '{key}' in cache: {e}")

	async def expire(self, key: str, expire: Union[int, timedelta]) -> bool:
		"""
		Sets the expiration for a key.

		Args:
			key (str): The key for which to set the expiration.
			expire (Union[int, timedelta]): Expiration time in seconds or as a timedelta object.

		Returns:
			bool: True if the expiration was set successfully, False otherwise.
		"""
		await self._ensure_connected()
		try:
			if isinstance(expire, timedelta):
				expire_seconds = int(expire.total_seconds())
			else:
				expire_seconds = expire
			result = await self.redis_client.expire(key, expire_seconds)
			return bool(result)
		except Exception as e:
			self.logger.error(f"Failed to set expiration for key '{key}': {e}")
			raise RuntimeError(f"Failed to set expiration for key '{key}': {e}")

	async def get_ttl(self, key: str) -> int:
		"""
		Gets the time to live (TTL) for a key in seconds.

		Args:
			key (str): The key to get the TTL for.

		Returns:
			int: The TTL in seconds. -1 if the key exists but has no associated expire. -2 if the key does not exist.
		"""
		await self._ensure_connected()
		try:
			result = await self.redis_client.ttl(key)
			return int(result)
		except Exception as e:
			self.logger.error(f"Failed to get TTL for key '{key}': {e}")
			raise RuntimeError(f"Failed to get TTL for key '{key}': {e}")


cache_service = CacheService()
