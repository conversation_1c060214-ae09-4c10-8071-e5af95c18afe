import io
from datetime import <PERSON><PERSON><PERSON>
from typing import Binary<PERSON>, Dict, List, Optional, Union

from minio import Minio
from minio.commonconfig import CopySource
from minio.deleteobjects import DeleteObject
from minio.error import S3Error

from src.config.settings import MINIO_ACCESS_KEY, MINIO_BUCKET, MINIO_ENDPOINT, MINIO_SECRET_KEY, MINIO_SECURE
from src.core.logger.internal_logger import get_logger


class StorageService:
	"""
	A comprehensive service class for handling MinIO operations.
	Provides methods for bucket management, object operations, and utilities.
	"""

	def __init__(
		self, endpoint: str, access_key: str, secret_key: str, secure: bool = False, bucket: str = MINIO_BUCKET
	):
		"""
		Initialize MinIO service.

		Args:
			endpoint: MinIO server endpoint (e.g., 'localhost:9000')
			access_key: MinIO access key
			secret_key: MinIO secret key
			secure: Use HTTPS if True, HTTP if False
			region: MinIO region (optional)
		"""
		self.endpoint = endpoint
		self.access_key = access_key
		self.secret_key = secret_key
		self.secure = secure
		self.logger = get_logger(__name__)
		self.bucket = bucket

		self.client = Minio(endpoint=endpoint, access_key=access_key, secret_key=secret_key, secure=secure)

		self._initialize_default_bucket()
		self.logger.info("MinIO Client initialized successfully")

	@classmethod
	def from_env(cls) -> "StorageService":
		"""
		Create MinIOService instance from environment variables.

		Environment variables:
			MINIO_ENDPOINT: MinIO server endpoint
			MINIO_ACCESS_KEY: MinIO access key
			MINIO_SECRET_KEY: MinIO secret key
			MINIO_SECURE: Use HTTPS (default: False)
		"""
		endpoint = MINIO_ENDPOINT
		access_key = MINIO_ACCESS_KEY
		secret_key = MINIO_SECRET_KEY
		secure = MINIO_SECURE
		bucket = MINIO_BUCKET

		if not all([endpoint, access_key, secret_key]):
			raise ValueError(
				"Missing required environment variables: MINIO_ENDPOINT, MINIO_ACCESS_KEY, MINIO_SECRET_KEY"
			)

		return cls(endpoint, access_key, secret_key, secure, bucket)

	def _initialize_default_bucket(self):
		if not self.bucket_exists(MINIO_BUCKET):
			return self.create_bucket(MINIO_BUCKET)

	def create_bucket(self, bucket_name: str, region: str = None) -> bool:
		"""
		Create a new bucket.

		Args:
			bucket_name: Name of the bucket to create
			region: Region for the bucket (optional)

		Returns:
			True if bucket was created successfully, False otherwise
		"""
		try:
			if not self.bucket_exists(bucket_name):
				self.client.make_bucket(bucket_name)
				self.logger.info(f"Bucket '{bucket_name}' created successfully")
				return True
			else:
				self.logger.info(f"Bucket '{bucket_name}' already exists")
				return True
		except S3Error as e:
			self.logger.error(f"Failed to create bucket '{bucket_name}': {e}")
			return False

	def bucket_exists(self, bucket_name: str) -> bool:
		"""
		Check if a bucket exists.

		Args:
			bucket_name: Name of the bucket to check

		Returns:
			True if bucket exists, False otherwise
		"""
		try:
			return self.client.bucket_exists(bucket_name)
		except S3Error as e:
			self.logger.error(f"Error checking bucket existence '{bucket_name}': {e}")
			return False

	def list_buckets(self) -> List[str]:
		"""
		List all buckets.

		Returns:
			List of bucket names
		"""
		try:
			buckets = self.client.list_buckets()
			return [bucket.name for bucket in buckets]
		except S3Error as e:
			self.logger.error(f"Failed to list buckets: {e}")
			return []

	def delete_bucket(self, bucket_name: str) -> bool:
		"""
		Delete an empty bucket.

		Args:
			bucket_name: Name of the bucket to delete

		Returns:
			True if bucket was deleted successfully, False otherwise
		"""
		try:
			self.client.remove_bucket(bucket_name)
			self.logger.info(f"Bucket '{bucket_name}' deleted successfully")
			return True
		except S3Error as e:
			self.logger.error(f"Failed to delete bucket '{bucket_name}': {e}")
			return False

	def upload_file(
		self,
		bucket_name: str,
		object_name: str,
		file_path: str,
		content_type: str = None,
		metadata: Dict[str, str] = None,
	) -> bool:
		"""
		Upload a file to MinIO.

		Args:
			bucket_name: Name of the bucket
			object_name: Name of the object in the bucket
			file_path: Path to the local file
			content_type: MIME type of the file (optional)
			metadata: Additional metadata (optional)

		Returns:
			True if file was uploaded successfully, False otherwise
		"""
		try:
			self.client.fput_object(
				bucket_name=bucket_name,
				object_name=object_name,
				file_path=file_path,
				content_type=content_type,
				metadata=metadata,
			)
			self.logger.info(f"File '{file_path}' uploaded as '{object_name}' to bucket '{bucket_name}'")
			return True
		except S3Error as e:
			self.logger.error(f"Failed to upload file '{file_path}': {e}")
			return False

	def upload_data(
		self,
		object_name: str,
		data: Union[bytes, str, BinaryIO],
		content_type: str = None,
		metadata: Dict[str, str] = None,
		bucket_name: str = MINIO_BUCKET,
	) -> bool:
		"""
		Upload data to MinIO.

		Args:
			bucket_name: Name of the bucket
			object_name: Name of the object in the bucket
			data: Data to upload (bytes, string, or file-like object)
			content_type: MIME type of the data (optional)
			metadata: Additional metadata (optional)

		Returns:
			True if data was uploaded successfully, False otherwise
		"""
		try:
			if isinstance(data, str):
				data = data.encode("utf-8")

			if isinstance(data, bytes):
				data = io.BytesIO(data)

			data.seek(0, 2)
			length = data.tell()
			data.seek(0)

			self.client.put_object(
				bucket_name=bucket_name,
				object_name=object_name,
				data=data,
				length=length,
				content_type=content_type,
				metadata=metadata,
			)
			self.logger.info(f"Data uploaded as '{object_name}' to bucket '{bucket_name}'")
			return True
		except S3Error as e:
			self.logger.error(f"Failed to upload data as '{object_name}': {e}")
			return False

	def download_file(self, bucket_name: str, object_name: str, file_path: str) -> bool:
		"""
		Download a file from MinIO.

		Args:
			bucket_name: Name of the bucket
			object_name: Name of the object in the bucket
			file_path: Path where the file should be saved

		Returns:
			True if file was downloaded successfully, False otherwise
		"""
		try:
			self.client.fget_object(bucket_name, object_name, file_path)
			self.logger.info(f"File '{object_name}' downloaded from bucket '{bucket_name}' to '{file_path}'")
			return True
		except S3Error as e:
			self.logger.error(f"Failed to download file '{object_name}': {e}")
			return False

	def download_data(self, bucket_name: str, object_name: str) -> Optional[bytes]:
		"""
		Download data from MinIO.

		Args:
			bucket_name: Name of the bucket
			object_name: Name of the object in the bucket

		Returns:
			Downloaded data as bytes, None if failed
		"""
		try:
			response = self.client.get_object(bucket_name, object_name)
			data = response.read()
			response.close()
			response.release_conn()
			self.logger.info(f"Data '{object_name}' downloaded from bucket '{bucket_name}'")
			return data
		except S3Error as e:
			self.logger.error(f"Failed to download data '{object_name}': {e}")
			return None

	def object_exists(self, bucket_name: str, object_name: str) -> bool:
		"""
		Check if an object exists in a bucket.

		Args:
			bucket_name: Name of the bucket
			object_name: Name of the object

		Returns:
			True if object exists, False otherwise
		"""
		try:
			self.client.stat_object(bucket_name, object_name)
			return True
		except S3Error:
			return False

	def delete_object(self, bucket_name: str, object_name: str) -> bool:
		"""
		Delete an object from a bucket.

		Args:
			bucket_name: Name of the bucket
			object_name: Name of the object to delete

		Returns:
			True if object was deleted successfully, False otherwise
		"""
		try:
			self.client.remove_object(bucket_name, object_name)
			self.logger.info(f"Object '{object_name}' deleted from bucket '{bucket_name}'")
			return True
		except S3Error as e:
			self.logger.error(f"Failed to delete object '{object_name}': {e}")
			return False

	def delete_objects(self, bucket_name: str, object_names: List[str]) -> bool:
		"""
		Delete multiple objects from a bucket.

		Args:
			bucket_name: Name of the bucket
			object_names: List of object names to delete

		Returns:
			True if all objects were deleted successfully, False otherwise
		"""
		try:
			delete_object_list = [DeleteObject(name) for name in object_names]
			errors = self.client.remove_objects(bucket_name, delete_object_list)

			error_list = list(errors)
			if error_list:
				for error in error_list:
					self.logger.error(f"Failed to delete object '{error.name}': {error}")
				return False

			self.logger.info(f"Successfully deleted {len(object_names)} objects from bucket '{bucket_name}'")
			return True
		except S3Error as e:
			self.logger.error(f"Failed to delete objects: {e}")
			return False

	def list_objects(
		self, bucket_name: str, prefix: str = None, recursive: bool = False, max_keys: int = None
	) -> List[Dict]:
		"""
		List objects in a bucket.

		Args:
			bucket_name: Name of the bucket
			prefix: Filter objects by prefix (optional)
			recursive: List objects recursively (optional)
			max_keys: Maximum number of objects to return (optional)

		Returns:
			List of object information dictionaries
		"""
		try:
			objects = self.client.list_objects(bucket_name, prefix=prefix, recursive=recursive)

			object_list = []
			count = 0

			for obj in objects:
				if max_keys and count >= max_keys:
					break

				object_info = {
					"name": obj.object_name,
					"size": obj.size,
					"last_modified": obj.last_modified,
					"etag": obj.etag,
					"content_type": obj.content_type,
				}
				object_list.append(object_info)
				count += 1

			return object_list
		except S3Error as e:
			self.logger.error(f"Failed to list objects in bucket '{bucket_name}': {e}")
			return []

	def copy_object(
		self,
		source_bucket: str,
		source_object: str,
		dest_bucket: str,
		dest_object: str,
		metadata: Dict[str, str] = None,
	) -> bool:
		"""
		Copy an object from one location to another.

		Args:
			source_bucket: Source bucket name
			source_object: Source object name
			dest_bucket: Destination bucket name
			dest_object: Destination object name
			metadata: Additional metadata for the copied object (optional)

		Returns:
			True if object was copied successfully, False otherwise
		"""
		try:
			copy_source = CopySource(source_bucket, source_object)
			self.client.copy_object(
				bucket_name=dest_bucket, object_name=dest_object, source=copy_source, metadata=metadata
			)
			self.logger.info(f"Object copied from '{source_bucket}/{source_object}' to '{dest_bucket}/{dest_object}'")
			return True
		except S3Error as e:
			self.logger.error(f"Failed to copy object: {e}")
			return False

	def get_object_info(self, bucket_name: str, object_name: str) -> Optional[Dict]:
		"""
		Get information about an object.

		Args:
			bucket_name: Name of the bucket
			object_name: Name of the object

		Returns:
			Object information dictionary, None if failed
		"""
		try:
			stat = self.client.stat_object(bucket_name, object_name)
			return {
				"name": object_name,
				"size": stat.size,
				"last_modified": stat.last_modified,
				"etag": stat.etag,
				"content_type": stat.content_type,
				"metadata": stat.metadata,
			}
		except S3Error as e:
			self.logger.error(f"Failed to get object info for '{object_name}': {e}")
			return None

	def generate_presigned_url(
		self, bucket_name: str, object_name: str, expires: timedelta = timedelta(hours=1), method: str = "GET"
	) -> Optional[str]:
		"""
		Generate a presigned URL for an object.

		Args:
			bucket_name: Name of the bucket
			object_name: Name of the object
			expires: URL expiration time (default: 1 hour)
			method: HTTP method (GET, PUT, POST, DELETE)

		Returns:
			Presigned URL string, None if failed
		"""
		try:
			if method.upper() == "GET":
				url = self.client.presigned_get_object(bucket_name, object_name, expires)
			elif method.upper() == "PUT":
				url = self.client.presigned_put_object(bucket_name, object_name, expires)
			else:
				raise ValueError(f"Unsupported method: {method}")

			return url
		except S3Error as e:
			self.logger.error(f"Failed to generate presigned URL: {e}")
			return None

	def get_bucket_policy(self, bucket_name: str) -> Optional[str]:
		"""
		Get bucket policy.

		Args:
			bucket_name: Name of the bucket

		Returns:
			Bucket policy as JSON string, None if failed
		"""
		try:
			policy = self.client.get_bucket_policy(bucket_name)
			return policy
		except S3Error as e:
			self.logger.error(f"Failed to get bucket policy for '{bucket_name}': {e}")
			return None

	def set_bucket_policy(self, bucket_name: str, policy: str) -> bool:
		"""
		Set bucket policy.

		Args:
			bucket_name: Name of the bucket
			policy: Policy as JSON string

		Returns:
			True if policy was set successfully, False otherwise
		"""
		try:
			self.client.set_bucket_policy(bucket_name, policy)
			self.logger.info(f"Bucket policy set for '{bucket_name}'")
			return True
		except S3Error as e:
			self.logger.error(f"Failed to set bucket policy for '{bucket_name}': {e}")
			return False

	def health_check(self) -> bool:
		"""
		Perform a health check on the MinIO service.

		Returns:
			True if service is healthy, False otherwise
		"""
		try:
			self.client.list_buckets()
			return True
		except Exception as e:
			self.logger.error(f"MinIO health check failed: {e}")
			return False


storage_service = StorageService.from_env()
