from fastapi import Depends

from src.core.exceptions.api import ApiException
from src.core.guards.auth_guard import auth_guard
from src.core.http.responses import bad_request
from src.core.shared_schema import BaseResponse, CurrentUser

from .settings_schema import (
	CountryFilter,
	DistrictFilter,
	LoadableItemDto,
	LoadableItemFilter,
	LoadableItemRequest,
	RegionFilter,
)
from .settings_service import SettingsService

settings_service = SettingsService()


def fetch_countries_handler(filter: CountryFilter = Depends(CountryFilter)):
	try:
		return settings_service.retrieve_countries(filter)
	except ApiException as e:
		return bad_request(e.message)


def fetch_districts_handler(filter: DistrictFilter = Depends(DistrictFilter)):
	try:
		return settings_service.retrieve_districts(filter)
	except ApiException as e:
		return bad_request(e.message)


def fetch_regions_handler(filter: RegionFilter = Depends(RegionFilter)):
	try:
		return settings_service.retrieve_regions(filter)
	except ApiException as e:
		return bad_request(e.message)


def fetch_loadable_item_handler(filter: LoadableItemFilter = Depends(LoadableItemFilter)):
	try:
		return settings_service.retrieve_loadable_items(filter)
	except ApiException as e:
		return bad_request(e.message)


def fetch_permissions_handler():
	"""Fetch all permissions (loadable items with type='PERMISSION')"""
	try:
		filter = LoadableItemFilter(type="PERMISSION")
		return settings_service.retrieve_loadable_items(filter)
	except ApiException as e:
		raise bad_request(e.message)


def create_loadable_item_handler(data: LoadableItemRequest, user: CurrentUser = Depends(auth_guard.current_user)):
	try:
		item = settings_service.add_loadable_item(user, data)

		return BaseResponse[LoadableItemDto](data=item)
	except ApiException as e:
		return bad_request(e.message)


def update_loadable_item_handler(data: LoadableItemRequest, user: CurrentUser = Depends(auth_guard.current_user)):
	try:
		item = settings_service.update_loadable_item(user, data)

		return BaseResponse[LoadableItemDto](data=item)
	except ApiException as e:
		return bad_request(e.message)


def delete_loadable_item_handler(id: str, user: CurrentUser = Depends(auth_guard.current_user)):
	try:
		deleted = settings_service.delete_loadable_item(user, id)

		return BaseResponse[bool](data=deleted)
	except ApiException as e:
		return bad_request(e.message)
