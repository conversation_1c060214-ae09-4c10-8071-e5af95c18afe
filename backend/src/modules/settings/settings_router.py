from fastapi import APIRouter

import src.modules.settings.settings_controller as controller
import src.modules.settings.settings_schema as schema
from src.core.shared_schema import BaseResponse, Pagination

router = APIRouter(tags=["settings"])

router.add_api_route(
	path="/countries",
	endpoint=controller.fetch_countries_handler,
	response_model=Pagination[schema.CountryDto],
	methods=["GET"],
	description="Retrieve countries list",
)

router.add_api_route(
	path="/regions",
	endpoint=controller.fetch_regions_handler,
	response_model=Pagination[schema.RegionDto],
	methods=["GET"],
	description="Retrieve regions list",
)

router.add_api_route(
	path="/districts",
	endpoint=controller.fetch_districts_handler,
	response_model=Pagination[schema.DistrictDto],
	methods=["GET"],
	description="Retrieve district list",
)

router.add_api_route(
	path="/loadable_items",
	endpoint=controller.fetch_loadable_item_handler,
	response_model=Pagination[schema.LoadableItemDto],
	methods=["GET"],
	description="Retrieve loadable items list",
)

router.add_api_route(
	path="/loadable_items",
	endpoint=controller.create_loadable_item_handler,
	response_model=BaseResponse[schema.LoadableItemDto],
	methods=["POST"],
	description="Create loadable items list",
)

router.add_api_route(
	path="/loadable_items/{id}",
	endpoint=controller.update_loadable_item_handler,
	response_model=BaseResponse[schema.LoadableItemDto],
	methods=["PUT"],
	description="Update loadable items list",
)

router.add_api_route(
	path="/permissions",
	endpoint=controller.fetch_permissions_handler,
	response_model=Pagination[schema.LoadableItemDto],
	methods=["GET"],
	description="Retrieve permissions list",
)

router.add_api_route(
	path="/loadable_items/{id}",
	endpoint=controller.delete_loadable_item_handler,
	response_model=BaseResponse[bool],
	methods=["DELETE"],
	description="Delete loadable items list",
)
