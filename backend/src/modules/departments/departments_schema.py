from typing import Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict

from src.core.shared_schema import BaseRequest
from src.core.utils.common import super_to_optional


class DepartmentBaseDto(BaseModel):
	model_config = ConfigDict(from_attributes=True)
	name: str
	code: str
	description: Optional[str] = None


class DepartmentResponse(DepartmentBaseDto):
	id: UUID


class DepartmentCreate(DepartmentBaseDto):
	pass


class DepartmentUpdateDto(DepartmentBaseDto):
	name: Optional[str] = None
	code: Optional[str] = None
	description: Optional[str] = None


DepartmentUpdate = super_to_optional(DepartmentUpdateDto)


class DepartmentFilters(BaseRequest):
	"""Filters for departments."""

	name: Optional[str] = None
	code: Optional[str] = None
	description: Optional[str] = None
