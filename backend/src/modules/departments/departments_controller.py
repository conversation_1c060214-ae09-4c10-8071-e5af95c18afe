from typing import Any
from uuid import UUID
from fastapi import Depends, HTTPException, status
from fastapi_pagination.ext.sqlalchemy import paginate
from src.core.exceptions.api import ApiException
from src.modules.departments.departments_schema import DepartmentResponse, DepartmentFilters, DepartmentCreate
from src.modules.departments.departments_service import DepartmentsService

service = DepartmentsService()


def index(filters: DepartmentFilters = Depends(DepartmentFilters)):
	"""
	Get and search for departments with pagination.

	Args:
	    filters (DepartmentFilters): Query filters for departments.

	Returns:
	    Page[DepartmentResponse]: Paginated list of departments.
	"""
	return paginate(service.find_departments(filters))


def create(department: DepartmentCreate) -> DepartmentResponse:
	"""
	Create a new department.

	Args:
	    department (DepartmentCreate): Department data to create.

	Returns:
	    DepartmentResponse: Created department data.
	"""
	return service.create_department(department)


def get_by_id(department_id: UUID) -> DepartmentResponse:
	"""
	Get a department by ID.

	Args:
	    department_id (UUID): Department ID.

	Returns:
	    DepartmentResponse: Department data.
	"""
	return service.get_department_by_id(department_id)


def update(department_id: UUID, updates: DepartmentCreate) -> DepartmentResponse:
	"""
	Update a department.

	Args:
	    department_id (UUID): Department ID to update.
	    updates (DepartmentUpdate): Updates to apply.

	Returns:
	    DepartmentResponse: Updated department data.
	"""
	return service.update_department(department_id=department_id, updates=updates)


def delete(department_id: UUID, delete_reason: str):
	"""
	Delete a department (soft delete).

	Args:
	    department_id (UUID): Department ID to delete.
	"""
	service.delete_department(department_id, delete_reason)
