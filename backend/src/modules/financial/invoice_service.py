from fastapi_pagination import Params
from fastapi_pagination.ext.sqlalchemy import paginate

from src.config.db.models import Invoice
from src.core.base.base_repository import BaseRepository
from sqlalchemy.orm import Session as OrmSession

from src.core.dtos.financial_dtos import InvoiceDto, to_invoice_dto
from src.core.logger.internal_logger import get_logger
from src.core.shared_schema import Pagination
from src.modules.financial.financial_schema import InvoiceFilter


class InvoiceService(BaseRepository):
	def __init__(self, db: OrmSession = None):
		if db is not None:
			self.db = db
		else:
			super().__init__()

		self.logger = get_logger(__name__)

	def retrieve_invoices(self, filter: InvoiceFilter) -> Pagination[InvoiceDto]:
		try:
			query = self.db.query(Invoice)

			if filter.organization_id:
				query = query.filter(Invoice.organization_id == filter.organization_id)

			if filter.start_date:
				query = query.filter(Invoice.created_at >= filter.start_date)

			if filter.end_date:
				query = query.filter(Invoice.created <= filter.end_date)

			query_result = paginate(query, Params(page=filter.page, size=filter.size))
			data = [to_invoice_dto(row) for row in query_result.items]

			return Pagination.from_query_result(data, query_result)
		except Exception as e:
			self.logger.error(f"Failed to fetch invoice items : {str(e)}")
			raise


invoice_service = InvoiceService()
