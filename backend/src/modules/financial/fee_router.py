from typing import List

from fastapi import APIRouter, status
from pydantic import UUID4

from src.core.dtos.fee_dtos import FeeDto
from src.core.shared_schema import BaseResponse, Pagination
from src.modules.financial import fee_controller as controller

fee_router_v1 = APIRouter(tags=["fees"])

# Get all fees with filtering and pagination
fee_router_v1.add_api_route(
    path="/fees",
    methods=["GET"],
    endpoint=controller.fetch_fees_handler,
    response_model=Pagination[FeeDto],
    summary="Retrieve paginated list of fees",
    description="Get all fees with optional filtering by name, status, category, currency, etc."
)

# Get specific fee by ID
fee_router_v1.add_api_route(
    path="/fees/{fee_id}",
    methods=["GET"],
    endpoint=controller.get_fee_handler,
    response_model=BaseResponse[FeeDto],
    summary="Get fee by ID",
    description="Retrieve a specific fee by its ID with related data"
)

# Create new fee
fee_router_v1.add_api_route(
    path="/fees",
    methods=["POST"],
    endpoint=controller.create_fee_handler,
    response_model=BaseResponse[FeeDto],
    status_code=status.HTTP_201_CREATED,
    summary="Create new fee",
    description="Create a new fee with specified parameters"
)

# Update existing fee
fee_router_v1.add_api_route(
    path="/fees/{fee_id}",
    methods=["PUT"],
    endpoint=controller.update_fee_handler,
    response_model=BaseResponse[FeeDto],
    summary="Update fee",
    description="Update an existing fee's properties"
)

# Delete fee (soft delete)
fee_router_v1.add_api_route(
    path="/fees/{fee_id}",
    methods=["DELETE"],
    endpoint=controller.delete_fee_handler,
    summary="Delete fee",
    description="Soft delete a fee (mark as voided)"
)

# Activate scheduled fee
fee_router_v1.add_api_route(
    path="/fees/{fee_id}/activate",
    methods=["POST"],
    endpoint=controller.activate_fee_handler,
    response_model=BaseResponse[FeeDto],
    summary="Activate scheduled fee",
    description="Manually activate a scheduled fee"
)