from typing import Optional

from pydantic import Field

from src.config.db.models.currency import CurrencyStatus
from src.core.shared_schema import BaseRequest


class CurrencyFilter(BaseRequest):
    """Filter parameters for currency queries"""

    name: Optional[str] = Field(default=None, description="Filter by currency name")
    code: Optional[str] = Field(default=None, description="Filter by currency code")
    status: Optional[CurrencyStatus] = Field(default=None, description="Filter by currency status")
    is_default: Optional[bool] = Field(default=None, description="Filter by default currency flag")
    search: Optional[str] = Field(default=None, description="Search in name or code")
