from datetime import datetime
from typing import Optional
from uuid import UUID

from fastapi_pagination import Params
from fastapi_pagination.ext.sqlalchemy import paginate

from src.config.db.models.currency import Currency, CurrencyStatus
from src.core.base.base_repository import BaseRepository
from src.core.dtos.currency_dtos import CurrencyDto, CurrencyCreateRequest, CurrencyUpdateRequest, to_currency_dto
from src.core.exceptions.api import ApiException
from src.core.shared_schema import CurrentUser, Pagination
from src.modules.financial.currency_schema import CurrencyFilter


class CurrencyService(BaseRepository):
    """Service for managing currencies"""

    def __init__(self):
        super().__init__()

    async def retrieve_currencies(self, filter: CurrencyFilter) -> Pagination[CurrencyDto]:
        """
        Retrieve paginated list of currencies with filtering
        """
        try:
            query = self.db.query(Currency)

            # Apply filters
            if filter.name:
                query = query.filter(Currency.name.ilike(f"%{filter.name}%"))

            if filter.code:
                query = query.filter(Currency.code.ilike(f"%{filter.code}%"))

            if filter.status:
                query = query.filter(Currency.status == filter.status)

            if filter.is_default is not None:
                query = query.filter(Currency.is_default == filter.is_default)

            if filter.search:
                search_term = f"%{filter.search}%"
                query = query.filter(
                    Currency.name.ilike(search_term) |
                    Currency.code.ilike(search_term)
                )

            # Order by name
            query = query.order_by(Currency.name.asc())

            # Paginate
            paginated_result = paginate(query, Params(page=filter.page, size=filter.size))
            currencies = [to_currency_dto(currency) for currency in paginated_result.items]

            return Pagination.from_query_result(currencies, paginated_result)

        except Exception as e:
            self.logger.error(f"Failed to retrieve currencies: {str(e)}")
            raise e
            # raise ApiException("Failed to retrieve currencies")

    def get_currency_by_id(self, currency_id: UUID) -> Optional[Currency]:
        """
        Get currency by ID
        """
        try:
            return self.db.query(Currency).filter(Currency.id == currency_id).first()
        except Exception as e:
            self.logger.error(f"Failed to get currency by ID {currency_id}: {str(e)}")
            raise ApiException("Failed to retrieve currency")

    def create_currency(self, request: CurrencyCreateRequest) -> Currency:
        """
        Create a new currency
        """
        try:
            # Check if currency with same name or code already exists
            existing_currency = self.db.query(Currency).filter(
                (Currency.name == request.name) | (Currency.code == request.code)
            ).first()

            if existing_currency:
                if existing_currency.name == request.name:
                    raise ApiException(f"Currency with name '{request.name}' already exists")
                if existing_currency.code == request.code:
                    raise ApiException(f"Currency with code '{request.code}' already exists")

            # If setting as default, unset other default currencies
            if request.is_default:
                self.db.query(Currency).filter(Currency.is_default == True).update(
                    {"is_default": False, "updated_at": datetime.now()}
                )

            # Create new currency
            currency = Currency(
                name=request.name,
                code=request.code,
                exchange_rate=request.exchange_rate,
                is_default=request.is_default,
                status=request.status,
                created_by=self.current_user.id if self.current_user else None,
                updated_by=self.current_user.id if self.current_user else None,
            )

            self.db.add(currency)
            self.db.commit()
            self.db.refresh(currency)

            self.logger.info(f"Currency created successfully: {currency.name} ({currency.code})")
            return currency

        except ApiException:
            self.db.rollback()
            raise
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Failed to create currency: {str(e)}")
            raise ApiException("Failed to create currency")

    def update_currency(self, currency_id: UUID, request: CurrencyUpdateRequest) -> Optional[Currency]:
        """
        Update an existing currency
        """
        try:
            currency = self.get_currency_by_id(currency_id)
            if not currency:
                return None

            # Check for duplicate name or code (excluding current currency)
            if request.name or request.code:
                existing_query = self.db.query(Currency).filter(Currency.id != currency_id)
                
                if request.name:
                    existing_query = existing_query.filter(Currency.name == request.name)
                if request.code:
                    existing_query = existing_query.filter(Currency.code == request.code)
                
                existing_currency = existing_query.first()
                if existing_currency:
                    if request.name and existing_currency.name == request.name:
                        raise ApiException(f"Currency with name '{request.name}' already exists")
                    if request.code and existing_currency.code == request.code:
                        raise ApiException(f"Currency with code '{request.code}' already exists")

            # If setting as default, unset other default currencies
            if request.is_default:
                self.db.query(Currency).filter(
                    Currency.is_default == True,
                    Currency.id != currency_id
                ).update({"is_default": False, "updated_at": datetime.now()})

            # Update fields
            update_data = {}
            if request.name is not None:
                update_data["name"] = request.name
            if request.code is not None:
                update_data["code"] = request.code
            if request.exchange_rate is not None:
                update_data["exchange_rate"] = request.exchange_rate
            if request.is_default is not None:
                update_data["is_default"] = request.is_default
            if request.status is not None:
                update_data["status"] = request.status

            if update_data:
                update_data["updated_by"] = self.current_user.id if self.current_user else None
                update_data["updated_at"] = datetime.now()

                for key, value in update_data.items():
                    setattr(currency, key, value)

            self.db.commit()
            self.db.refresh(currency)

            self.logger.info(f"Currency updated successfully: {currency.name} ({currency.code})")
            return currency

        except ApiException:
            self.db.rollback()
            raise
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Failed to update currency {currency_id}: {str(e)}")
            raise ApiException("Failed to update currency")

    def delete_currency(self, currency_id: UUID) -> bool:
        """
        Delete a currency (hard delete)
        """
        try:
            currency = self.get_currency_by_id(currency_id)
            if not currency:
                return False

            # Check if currency is being used
            # Note: This would need to check relationships like fees, funding_sources, etc.
            # For now, we'll allow deletion but in production you might want to add these checks

            self.db.delete(currency)
            self.db.commit()

            self.logger.info(f"Currency deleted successfully: {currency.name} ({currency.code})")
            return True

        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Failed to delete currency {currency_id}: {str(e)}")
            raise ApiException("Failed to delete currency")


# Create service instance
currency_service = CurrencyService()
