from fastapi import APIRouter

from src.core.dtos.financial_dtos import InvoiceDto
from src.core.shared_schema import Pagination
from src.modules.financial import financial_controller as controller
from src.modules.financial.fee_router import fee_router_v1
from src.modules.financial.currency_router import currency_router_v1

financial_router_v1 = APIRouter(tags=["financial"])

financial_router_v1.add_api_route(
	path="/invoices",
	methods=["GET"],
	endpoint=controller.fetch_invoices_handler,
	response_model=Pagination[InvoiceDto],
)

# Include fee routes
financial_router_v1.include_router(fee_router_v1)

# Include currency routes
financial_router_v1.include_router(currency_router_v1)
