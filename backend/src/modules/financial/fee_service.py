import json
from datetime import datetime
from typing import List, Optional

from fastapi_pagination import Params
from fastapi_pagination.ext.sqlalchemy import paginate
from sqlalchemy import and_, or_
from sqlalchemy.orm import Session as OrmSession, joinedload

from src.config.db.models import <PERSON><PERSON>, <PERSON>eChangeLog, FeeStatus
from src.config.db.models.application import Application, ApplicationType
from src.config.db.models.application_fee import ApplicationFee
from src.config.db.models.loadable_item import LoadableItem
from src.core.base.base_repository import BaseRepository
from src.core.dtos.fee_dtos import FeeDto, FeeCreateRequest, FeeUpdateRequest, to_fee_dto
from src.core.exceptions.api import ApiException
from src.core.logger.internal_logger import get_logger
from src.core.shared_schema import CurrentUser, Pagination
from src.modules.financial.fee_schema import FeeFilter


class FeeService(BaseRepository):
	"""
	Service class for managing fee calculations, versioning, and scheduling.
	"""

	def __init__(self, db: OrmSession = None):
		if db is not None:
			self.db = db
		else:
			super().__init__()

		self.logger = get_logger(__name__)

	async def get_active_fee_for_organization(
		self,
		fee_category_id: str,
		organization_income: Optional[float] = None,
		currency_id: Optional[str] = None,
		as_of_date: Optional[datetime] = None,
	) -> Optional[Fee]:
		"""
		Get the active fee for an organization based on income.

		Priority:
		1. Income-based fee that matches the organization's income range
		2. Default fee (based_on_income = False) as fallback

		Args:
		    fee_category_id: The fee category ID
		    organization_income: Organization's total income
		    currency_id: Currency ID (optional)
		    as_of_date: Date to check for active fee (defaults to now)

		Returns:
		    Fee object or None if no appropriate fee found
		"""
		if as_of_date is None:
			as_of_date = datetime.now()

		try:
			query = self.db.query(Fee).filter(
				Fee.fee_category_id == fee_category_id,
				Fee.status == FeeStatus.ACTIVE,
				Fee.effective_from <= as_of_date,
				(Fee.effective_to.is_(None) | (Fee.effective_to > as_of_date)),
			)

			if currency_id:
				query = query.filter(Fee.currency_id == currency_id)

			fees = query.all()

			if not fees:
				return None

			if organization_income is not None:
				income_based_fee = self._find_best_income_based_fee(fees, organization_income)
				if income_based_fee:
					return income_based_fee

			default_fee = self._find_default_fee(fees)
			return default_fee

		except Exception as e:
			self.logger.error(f"Failed to find appropriate fee: {str(e)}")
			raise ApiException("Failed to find appropriate fee")

	def _find_best_income_based_fee(self, fees: List[Fee], organization_income: float) -> Optional[Fee]:
		"""
		Find the best matching income-based fee for the given organization income.

		Args:
		    fees: List of available fees
		    organization_income: Organization's income

		Returns:
		    Best matching income-based fee or None
		"""
		income_based_fees = [fee for fee in fees if fee.based_on_income]

		matching_fees = []
		for fee in income_based_fees:
			if self._income_matches_fee_range(organization_income, fee):
				matching_fees.append(fee)

		if not matching_fees:
			return None

		matching_fees.sort(key=lambda f: f.min_income, reverse=True)
		return matching_fees[0]

	async def _income_matches_fee_range(self, organization_income: float, fee: Fee) -> bool:
		"""
		Check if organization income falls within the fee's income range.

		Args:
		    organization_income: Organization's income
		    fee: Fee object to check

		Returns:
		    True if income matches the fee range
		"""
		min_income_check = fee.min_income <= organization_income

		max_income_check = fee.max_income is None or fee.max_income == 0 or organization_income <= fee.max_income

		return min_income_check and max_income_check

	def _find_default_fee(self, fees: List[Fee]) -> Optional[Fee]:
		"""
		Find the default fee (based_on_income = False).

		Args:
		    fees: List of available fees

		Returns:
		    Default fee or None
		"""
		default_fees = [fee for fee in fees if not fee.based_on_income]

		if default_fees:
			default_fees.sort(key=lambda f: f.version, reverse=True)
			return default_fees[0]

		return None

	async def calculate_fee_amount(self, fee: Fee, organization_income: Optional[float] = None) -> float:
		"""
		Calculate the actual fee amount based on the fee configuration.

		Args:
		    fee: The Fee object
		    organization_income: Organization's income (required if fee is income-based)

		Returns:
		    Calculated fee amount

		Raises:
		    ValueError: If organization_income is required but not provided
		"""
		if fee.based_on_income and organization_income is None:
			raise ValueError("Organization income is required for income-based fees")

		return fee.amount

	async def get_fee_amount_for_organization(
		self,
		fee_category_id: str,
		organization_income: Optional[float] = None,
		currency_id: Optional[str] = None,
		as_of_date: Optional[datetime] = None,
	) -> Optional[float]:
		"""
		Get the calculated fee amount for an organization.

		Args:
		    fee_category_id: The fee category ID
		    organization_income: Organization's total income
		    currency_id: Currency ID (optional)
		    as_of_date: Date to check for active fee (defaults to now)

		Returns:
		    Calculated fee amount or None if no fee found
		"""
		fee = self.get_active_fee_for_organization(fee_category_id, organization_income, currency_id, as_of_date)

		if fee:
			data = await self.calculate_fee_amount(fee, organization_income)
			return data

		return None

	async def create_application_fee(
		self, application: Application, organization_income: Optional[float] = None
	) -> ApplicationFee:
		"""
		Calculate and create an application fee based on application type and organization income.

		Args:
		    application: The application object
		    organization_income: Organization's total income

		Returns:
		    Created ApplicationFee object

		Raises:
		    ApiException: If fee calculation fails
		"""
		try:
			fee_category = await self._get_fee_category_for_application(application)

			if not fee_category:
				raise ApiException(f"No fee category found for application type: {application.type}")

			active_fee = await self.get_active_fee_for_organization(
				fee_category_id=fee_category.id, organization_income=organization_income
			)

			if not active_fee:
				raise ApiException("No active fee found for this organization")

			calculated_amount = await self.calculate_fee_amount(active_fee, organization_income)

			application_fee = ApplicationFee(
				application_id=application.id,
				fee_id=active_fee.id,
				amount=calculated_amount,
				organization_income=organization_income,
				created_by=self.current_user.id,
			)

			self.db.add(application_fee)
			self.db.flush()

			self.logger.info(f"Created application fee: {application_fee.id} for application: {application.id}")
			return application_fee

		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to create application fee: {str(e)}")
			raise ApiException("Failed to create application fee")

	async def _get_fee_category_for_application(self, application: Application) -> Optional[LoadableItem]:
		"""
		Get the appropriate fee category for an application type.

		Args:
		    application: The application object

		Returns:
		    LoadableItem (fee category) or None
		"""
		fee_category_mapping = {
			ApplicationType.ORGANIZATION_REGISTRATION.value: "FC01",
			ApplicationType.LICENCE_RENEWAL.value: "FC02",
			ApplicationType.PERMIT_APPLICATION.value: "FC03",
		}

		category_code = fee_category_mapping.get(application.type)

		if category_code:
			return self.db.query(LoadableItem).filter(LoadableItem.code == category_code).first()

		return None

	async def schedule_fee_update(
		self,
		fee_id: str,
		new_values: dict,
		effective_date: datetime,
		change_reason: Optional[str] = None,
		changed_by: Optional[str] = None,
	) -> str:
		"""
		Schedule a fee update without affecting existing applications.

		Args:
		    fee_id: Current fee ID to update
		    new_values: Dictionary of new values
		    effective_date: When the new fee should become active
		    change_reason: Reason for the change
		    changed_by: User ID who made the change

		Returns:
		    New fee ID (the scheduled version)

		Raises:
		    ApiException: If scheduling fails
		"""
		try:
			current_fee = await self.db.query(Fee).filter(Fee.id == fee_id).first()
			if not current_fee:
				raise ValueError(f"Fee with ID {fee_id} not found")

			new_fee = Fee(
				amount=new_values.get("amount", current_fee.amount),
				fee_category_id=current_fee.fee_category_id,
				currency_id=current_fee.currency_id,
				name=new_values.get("name", current_fee.name),
				based_on_income=new_values.get("based_on_income", current_fee.based_on_income),
				min_income=new_values.get("min_income", current_fee.min_income),
				max_income=new_values.get("max_income", current_fee.max_income),
				description=new_values.get("description", current_fee.description),
				status=FeeStatus.SCHEDULED,
				version=current_fee.version + 1,
				effective_from=effective_date,
				effective_to=None,
				replaces_fee_id=current_fee.id,
			)

			self.db.add(new_fee)
			self.db.flush()

			# Log the change
			self._log_fee_change(
				fee_id=new_fee.id,
				change_type="SCHEDULED",
				old_values=self._extract_fee_values(current_fee),
				new_values=new_values,
				change_reason=change_reason,
				scheduled_date=effective_date,
				changed_by=changed_by,
			)

			self.db.commit()
			self.logger.info(f"Scheduled fee update: {new_fee.id}")
			return new_fee.id

		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to schedule fee update: {str(e)}")
			raise ApiException("Failed to schedule fee update")

	async def activate_scheduled_fees(self) -> int:
		"""
		Activate fees that are scheduled to become active.
		This should be run as a scheduled job.

		Returns:
		    Number of fees activated

		Raises:
		    ApiException: If activation fails
		"""
		try:
			now = datetime.now()

			scheduled_fees = (
				self.db.query(Fee).filter(Fee.status == FeeStatus.SCHEDULED, Fee.effective_from <= now).all()
			)

			activated_count = 0

			for scheduled_fee in scheduled_fees:
				if scheduled_fee.replaces_fee_id:
					self._expire_fee(scheduled_fee.replaces_fee_id, now)

				scheduled_fee.status = FeeStatus.ACTIVE
				self._log_fee_change(
					fee_id=scheduled_fee.id,
					change_type="ACTIVATED",
					change_reason="Scheduled activation",
					change_timestamp=now,
				)

				activated_count += 1

			self.db.commit()
			self.logger.info(f"Activated {activated_count} scheduled fees")
			return activated_count

		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to activate scheduled fees: {str(e)}")
			raise ApiException("Failed to activate scheduled fees")

	def _expire_fee(self, fee_id: str, expiry_date: datetime) -> None:
		"""
		Expire a fee by setting its effective_to date and status.

		Args:
		    fee_id: Fee ID to expire
		    expiry_date: Date when the fee expires
		"""
		fee = self.db.query(Fee).filter(Fee.id == fee_id).first()
		if fee:
			fee.effective_to = expiry_date
			fee.status = FeeStatus.EXPIRED

	def _log_fee_change(
		self,
		fee_id: str,
		change_type: str,
		old_values: Optional[dict] = None,
		new_values: Optional[dict] = None,
		change_reason: Optional[str] = None,
		scheduled_date: Optional[datetime] = None,
		changed_by: Optional[str] = None,
		change_timestamp: Optional[datetime] = None,
	) -> None:
		"""
		Log a fee change to the change log table.

		Args:
		    fee_id: Fee ID
		    change_type: Type of change
		    old_values: Previous values (optional)
		    new_values: New values (optional)
		    change_reason: Reason for the change (optional)
		    scheduled_date: Scheduled date (optional)
		    changed_by: User who made the change (optional)
		    change_timestamp: Timestamp of the change (optional)
		"""
		change_log = FeeChangeLog(
			fee_id=fee_id,
			change_type=change_type,
			old_values=json.dumps(old_values) if old_values else None,
			new_values=json.dumps(new_values) if new_values else None,
			change_reason=change_reason,
			scheduled_date=scheduled_date,
			changed_by=changed_by,
			change_timestamp=change_timestamp or datetime.now(),
		)

		self.db.add(change_log)

	def _extract_fee_values(self, fee: Fee) -> dict:
		"""
		Extract fee values for logging purposes.

		Args:
		    fee: Fee object

		Returns:
		    Dictionary of fee values
		"""
		return {
			"amount": fee.amount,
			"name": fee.name,
			"based_on_income": fee.based_on_income,
			"min_income": fee.min_income,
			"max_income": fee.max_income,
			"description": fee.description,
		}

	def get_fee_history(self, fee_category_id: str, currency_id: Optional[str] = None) -> List[Fee]:
		"""
		Get the history of fees for a specific category.

		Args:
		    fee_category_id: Fee category ID
		    currency_id: Currency ID (optional)

		Returns:
		    List of Fee objects ordered by version descending
		"""
		query = self.db.query(Fee).filter(Fee.fee_category_id == fee_category_id)

		if currency_id:
			query = query.filter(Fee.currency_id == currency_id)

		return query.order_by(Fee.version.desc()).all()

	async def retrieve_fees(self, filter: FeeFilter) -> Pagination[FeeDto]:
		"""
		Retrieve paginated list of fees with filtering
		"""
		try:
			query = self.db.query(Fee).options(
				joinedload(Fee.category),
				joinedload(Fee.currency)
			)

			# Apply filters
			if filter.name:
				query = query.filter(Fee.name.ilike(f"%{filter.name}%"))

			if filter.status:
				query = query.filter(Fee.status == filter.status)

			if filter.fee_category_id:
				query = query.filter(Fee.fee_category_id == filter.fee_category_id)

			if filter.currency_id:
				query = query.filter(Fee.currency_id == filter.currency_id)

			if filter.based_on_income is not None:
				query = query.filter(Fee.based_on_income == filter.based_on_income)

			if filter.effective_from_start:
				query = query.filter(Fee.effective_from >= filter.effective_from_start)

			if filter.effective_from_end:
				query = query.filter(Fee.effective_from <= filter.effective_from_end)

			if filter.active_only:
				now = datetime.now()
				query = query.filter(
					and_(
						Fee.status == FeeStatus.ACTIVE,
						Fee.effective_from <= now,
						or_(Fee.effective_to.is_(None), Fee.effective_to > now)
					)
				)

			if not filter.include_expired:
				query = query.filter(Fee.status != FeeStatus.EXPIRED)

			if filter.version:
				query = query.filter(Fee.version == filter.version)

			# Apply voided filter
			query = query.filter(Fee.voided == False)

			# Order by created_at desc
			query = query.order_by(Fee.created_at.desc())

			# Get paginated results
			fees = paginate(query, Params(page=filter.page, size=filter.size))

			# Convert to DTOs
			fee_dtos = [to_fee_dto(fee, extras="category,currency") for fee in fees.items]

			return Pagination.from_query_result(
				fee_dtos, fees
			)

		except Exception as e:
			self.logger.error(f"Failed to retrieve fees: {str(e)}")
			raise ApiException("Failed to retrieve fees")

	async def get_fee_by_id(self, fee_id: str) -> Optional[Fee]:
		"""Get fee by ID"""
		try:
			return self.db.query(Fee).options(
				joinedload(Fee.category),
				joinedload(Fee.currency)
			).filter(
				Fee.id == fee_id,
				Fee.voided == False
			).first()
		except Exception as e:
			self.logger.error(f"Failed to get fee by ID: {str(e)}")
			raise ApiException("Failed to get fee")

	async def create_fee(self, request: FeeCreateRequest) -> Fee:
		"""Create a new fee"""
		try:
			# Set effective_from to now if not provided
			effective_from = request.effective_from or datetime.now()

			fee = Fee(
				amount=request.amount,
				fee_category_id=request.fee_category_id,
				currency_id=request.currency_id,
				name=request.name,
				based_on_income=request.based_on_income,
				min_income=request.min_income,
				max_income=request.max_income,
				description=request.description,
				effective_from=effective_from,
				status=request.status,
				version=1
			)

			self.db.add(fee)
			self.db.commit()
			self.db.refresh(fee)

			# Log the creation
			self._log_fee_change(
				fee_id=fee.id,
				change_type="CREATED",
				change_reason="Fee created",
				change_timestamp=datetime.now(),
			)

			return fee

		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to create fee: {str(e)}")
			raise ApiException("Failed to create fee")

	async def update_fee(self, fee_id: str, request: FeeUpdateRequest) -> Fee:
		"""Update an existing fee"""
		try:
			fee = self.db.query(Fee).filter(Fee.id == fee_id).first()
			if not fee:
				raise ApiException("Fee not found")

			# Store old values for logging
			old_values = {
				"amount": fee.amount,
				"name": fee.name,
				"based_on_income": fee.based_on_income,
				"min_income": fee.min_income,
				"max_income": fee.max_income,
				"description": fee.description,
				"status": fee.status
			}
   
			print(request.model_dump().items())

			for k, v in request.model_dump().items():
				if v is not None and Fee.__dict__.get(k) is not None:
					print(f"Setting {k} to {v}")
					setattr(fee, k, v)

			self.db.commit()
			self.db.refresh(fee)

			# Log the changes
			changes = {}
			for key, old_value in old_values.items():
				new_value = getattr(fee, key)
				if old_value != new_value:
					changes[key] = {"old": old_value, "new": new_value}

			if changes:
				self._log_fee_change(
					fee_id=fee.id,
					change_type="UPDATED",
					change_reason="Fee updated",
					change_timestamp=datetime.now(),
					old_values=old_values,
					new_values={k: v["new"] for k, v in changes.items()}
				)

			return fee

		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to update fee: {str(e)}")
			# raise ApiException("Failed to update fee")
			raise e

	async def delete_fee(self, fee_id: str) -> bool:
		"""Soft delete a fee"""
		try:
			fee = self.db.query(Fee).filter(Fee.id == fee_id).first()
			if not fee:
				return False

			self.db.delete(fee)
			self.db.commit()

			# Log the deletion
			self._log_fee_change(
				fee_id=fee.id,
				change_type="DELETED",
				change_reason="Fee deleted",
				change_timestamp=datetime.now(),
			)

			return True

		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to delete fee: {str(e)}")
			raise ApiException("Failed to delete fee")

	def activate_fee(self, fee_id: str, current_user: CurrentUser) -> Optional[Fee]:
		"""Activate a scheduled fee"""
		try:
			fee = self.get_fee_by_id(fee_id)
			if not fee or fee.status != FeeStatus.SCHEDULED:
				return None

			now = datetime.now()

			# Expire the previous fee if it exists
			if fee.replaces_fee_id:
				self._expire_fee(fee.replaces_fee_id, now)

			# Activate the scheduled fee
			fee.status = FeeStatus.ACTIVE
			fee.updated_by = current_user.id
			fee.updated_at = now

			self.db.commit()
			self.db.refresh(fee)

			# Log the activation
			self._log_fee_change(
				fee_id=fee.id,
				change_type="ACTIVATED",
				change_reason="Fee manually activated",
				change_timestamp=now,
				changed_by=current_user.id
			)

			return fee

		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to activate fee: {str(e)}")
			raise ApiException("Failed to activate fee")

	def _log_fee_change(
		self,
		fee_id: str,
		change_type: str,
		change_reason: str,
		change_timestamp: datetime,
		changed_by: str = None,
		old_values: dict = None,
		new_values: dict = None
	):
		"""Log fee changes"""
		try:
			change_log = FeeChangeLog(
				fee_id=fee_id,
				change_type=change_type,
				change_reason=change_reason,
				change_timestamp=change_timestamp,
				changed_by=changed_by,
				old_values=json.dumps(old_values) if old_values else None,
				new_values=json.dumps(new_values) if new_values else None
			)

			self.db.add(change_log)
			# Note: Don't commit here as this is called within other transactions

		except Exception as e:
			self.logger.error(f"Failed to log fee change: {str(e)}")
			# Don't raise exception as this is logging only


fee_service = FeeService()
