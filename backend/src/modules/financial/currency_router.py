from fastapi import APIRouter, status

from src.core.dtos.currency_dtos import CurrencyDto
from src.core.shared_schema import BaseResponse, Pagination
from src.modules.financial import currency_controller as controller

# Create router
currency_router_v1 = APIRouter(prefix="/currencies", tags=["currencies"])

# Get all currencies with filtering
currency_router_v1.add_api_route(
    path="",
    methods=["GET"],
    endpoint=controller.fetch_currencies_handler,
    response_model=Pagination[CurrencyDto],
    summary="Get currencies",
    description="Retrieve paginated list of currencies with optional filtering"
)

# Get specific currency by ID
currency_router_v1.add_api_route(
    path="/{currency_id}",
    methods=["GET"],
    endpoint=controller.get_currency_handler,
    response_model=BaseResponse[CurrencyDto],
    summary="Get currency by ID",
    description="Retrieve a specific currency by its ID"
)

# Create new currency
currency_router_v1.add_api_route(
    path="",
    methods=["POST"],
    endpoint=controller.create_currency_handler,
    response_model=BaseResponse[CurrencyDto],
    status_code=status.HTTP_201_CREATED,
    summary="Create currency",
    description="Create a new currency"
)

# Update existing currency
currency_router_v1.add_api_route(
    path="/{currency_id}",
    methods=["PUT"],
    endpoint=controller.update_currency_handler,
    response_model=BaseResponse[CurrencyDto],
    summary="Update currency",
    description="Update an existing currency"
)

# Delete currency
currency_router_v1.add_api_route(
    path="/{currency_id}",
    methods=["DELETE"],
    endpoint=controller.delete_currency_handler,
    summary="Delete currency",
    description="Delete a currency"
)
