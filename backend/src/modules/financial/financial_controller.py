from fastapi import Depends
from starlette.requests import Request

from src.core.dtos.financial_dtos import InvoiceDto
from src.core.guards.auth_guard import auth_guard
from src.core.shared_schema import Pagination
from src.modules.financial.financial_schema import InvoiceFilter
from src.modules.financial.invoice_service import invoice_service


@auth_guard.authenticated
def fetch_invoices_handler(
	_: Request,
	filter: InvoiceFilter = Depends(InvoiceFilter),
) -> Pagination[InvoiceDto]:
	return invoice_service.retrieve_invoices(filter)
