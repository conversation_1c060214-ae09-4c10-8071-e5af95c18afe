from fastapi import Depends
from pydantic import UUID4
from starlette.requests import Request

from src.core.context.auth_context import get_current_user
from src.core.dtos.currency_dtos import CurrencyDto, CurrencyCreateRequest, CurrencyUpdateRequest, to_currency_dto
from src.core.exceptions.api import ApiException
from src.core.guards.auth_guard import auth_guard
from src.core.http.responses import bad_request
from src.core.shared_schema import BaseResponse, Pagination
from src.modules.financial.currency_schema import CurrencyFilter
from src.modules.financial.currency_service import currency_service


@auth_guard.authenticated
async def fetch_currencies_handler(
    _: Request,
    filter: CurrencyFilter = Depends(CurrencyFilter),
) -> Pagination[CurrencyDto]:
    """Retrieve paginated list of currencies with filtering"""
    return await currency_service.retrieve_currencies(filter)


@auth_guard.authenticated
async def get_currency_handler(
    currency_id: UUID4,
    _: Request,
) -> BaseResponse[CurrencyDto]:
    """Get a specific currency by ID"""
    currency = await currency_service.get_currency_by_id(currency_id)
    if not currency:
        raise ApiException("Currency not found")
    
    currency_dto = to_currency_dto(currency)
    return BaseResponse[CurrencyDto](data=currency_dto)


@auth_guard.authenticated
async def create_currency_handler(
    request: CurrencyCreateRequest,
    _: Request,
) -> BaseResponse[CurrencyDto]:
    """Create a new currency"""
    currency = await currency_service.create_currency(request)
    currency_dto = to_currency_dto(currency)
    return BaseResponse[CurrencyDto](data=currency_dto)


@auth_guard.authenticated
async def update_currency_handler(
    currency_id: UUID4,
    request: CurrencyUpdateRequest,
    _: Request,
) -> BaseResponse[CurrencyDto]:
    """Update an existing currency"""
    currency = await currency_service.update_currency(currency_id, request)
    currency_dto = to_currency_dto(currency)
    return BaseResponse[CurrencyDto](data=currency_dto)


@auth_guard.authenticated
async def delete_currency_handler(
    currency_id: UUID4,
    _: Request,
) -> BaseResponse[bool]:
    """Delete a currency"""
    await currency_service.delete_currency(currency_id)
    return BaseResponse(success=True)