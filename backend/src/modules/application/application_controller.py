from fastapi.params import Depends
from fastapi_pagination.ext.sqlalchemy import paginate
from starlette.requests import Request

from src.core.dtos.workflow_dtos import WorkflowDto
from src.core.dtos.application_dtos import ApplicationDto
from src.core.guards.auth_guard import auth_guard
from src.core.shared_schema import Pagination, VoidRequest
from src.modules.application.application_schema import ApplicationFilter
from src.modules.application.application_service import application_service


@auth_guard.can_("read.application")
async def fetch_applications_handler(
	_: Request,
	filter: ApplicationFilter = Depends(ApplicationFilter),
) -> Pagination[ApplicationDto]:
	return await application_service.retrieve_applications(filter)


@auth_guard.can_("create.application")
async def create_application_handler(
	_: Request,
	data: ApplicationDto,
) -> ApplicationDto:
	application = await application_service.submit_application_for_review(data)
	return application


@auth_guard.can_("delete.application")
async def delete_application_handler(_: Request, application_id: str, payload: VoidRequest) -> None:
	await application_service.delete_application(application_id, payload)
	return None

@auth_guard.can_("read.application")
async def fetch_single_application_handler(_: Request, application_id: str) -> ApplicationDto:
  application = await application_service.get_application_by_id(application_id)
  return application

async def fetch_application_workflow_handler(_: Request, application_id: str) -> WorkflowDto:
	workflow = await application_service.get_application_workflow(application_id)
	return workflow
