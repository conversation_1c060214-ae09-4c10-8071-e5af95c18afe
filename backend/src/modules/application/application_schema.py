from typing import Optional

from pydantic import UUID4, Field

from src.config.db.models import ApplicationStatus, ApplicationType
from src.core.shared_schema import BaseRequest


class ApplicationFilter(BaseRequest):
	organization_id: Optional[UUID4] = Field(default=None, description="Organization ID")
	status: Optional[ApplicationStatus] = Field(default=None, description="Application status")
	type: Optional[ApplicationType] = Field(default=None, description="Application type")
