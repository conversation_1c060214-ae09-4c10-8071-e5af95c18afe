from typing import Optional, List
from uuid import UUID
from pydantic import BaseModel, ConfigDict, EmailStr

from src.modules.roles.roles_schema import RoleResponse
from src.modules.departments.departments_schema import DepartmentResponse
from src.config.db.models.base import Gender
from src.config.db.models.account import AccountStatus, AccountType
from src.core.shared_schema import BaseRequest


class AccountBaseDto(BaseModel):
	model_config = ConfigDict(from_attributes=True)

	handle: str
	type: AccountType
	status: AccountStatus


class UserBaseDto(BaseModel):
	model_config = ConfigDict(from_attributes=True)

	first_name: str
	middle_name: Optional[str]
	last_name: str
	email: EmailStr
	gender: Gender


class AccountResponse(AccountBaseDto):
	id: UUID


class UserResponse(UserBaseDto):
	model_config = ConfigDict(from_attributes=True)
	id: UUID
	account: AccountResponse
	roles: List[RoleResponse] = []
	departments: List[DepartmentResponse] = []


class UserUpdate(BaseModel):
	"""Schema for updating a user"""

	model_config = ConfigDict(from_attributes=True)

	first_name: Optional[str] = None
	middle_name: Optional[str] = None
	last_name: Optional[str] = None
	email: Optional[EmailStr] = None
	gender: Optional[Gender] = None
	handle: Optional[str] = None
	type: Optional[AccountType] = None
	is_external: Optional[bool] = None
	status: Optional[AccountStatus] = None


class UserFilters(BaseRequest):
	"""Filters for users."""

	first_name: Optional[str] = None
	middle_name: Optional[str] = None
	last_name: Optional[str] = None
	email: Optional[str] = None
	handle: Optional[str] = None
	type: Optional[AccountType] = None
	is_external: Optional[bool] = None
	status: Optional[AccountStatus] = None


# User-Department Assignment Schemas
class UserDepartmentAssignRequest(BaseModel):
	"""Schema for assigning a user to departments"""

	model_config = ConfigDict(from_attributes=True)

	user_id: UUID
	department_ids: List[UUID]


class UserDepartmentResponse(BaseModel):
	"""Schema for user-department assignment response"""

	model_config = ConfigDict(from_attributes=True)

	id: UUID
	user_id: UUID
	department_id: UUID


class UserDepartmentsResponse(BaseModel):
	"""Schema for multiple user-department assignments response"""

	model_config = ConfigDict(from_attributes=True)

	user_id: UUID
	assignments: List[UserDepartmentResponse]


# User-Role Assignment Schemas
class UserRoleAssignRequest(BaseModel):
	"""Schema for assigning roles to a user"""

	model_config = ConfigDict(from_attributes=True)

	user_id: UUID
	role_ids: List[UUID]


class UserRoleResponse(BaseModel):
	"""Schema for user-role assignment response"""

	model_config = ConfigDict(from_attributes=True)

	id: UUID
	user_id: UUID
	role_id: UUID


class UserRolesResponse(BaseModel):
	"""Schema for multiple user-role assignments response"""

	model_config = ConfigDict(from_attributes=True)

	user_id: UUID
	assignments: List[UserRoleResponse]
