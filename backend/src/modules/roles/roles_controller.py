from typing import Any, List
from uuid import UUID
from fastapi import Depends, HTTPException, status
from fastapi_pagination.ext.sqlalchemy import paginate
from src.core.exceptions.api import ApiException
from src.modules.roles.roles_schema import (
	RoleResponse,
	RoleFilters,
	RoleCreate,
	RoleUpdate,
	RolePermissionAssignRequest,
)
from src.modules.roles.roles_service import RolesService

service = RolesService()


def index(filters: RoleFilters = Depends(RoleFilters)) -> list[RoleResponse]:
	"""
	Get and search for roles with pagination.

	Args:
	    filters (RoleFilters): Query filters for roles.

	Returns:
	    Page[RoleResponse]: Paginated list of roles.
	"""
	return paginate(service.find_roles(filters))


def create(role: RoleCreate) -> RoleResponse:
	"""
	Create a new role.

	Args:
	    role (RoleCreate): Role data to create.

	Returns:
	    RoleResponse: Created role data.
	"""
	return service.create_role(role)


def get_by_id(role_id: UUID) -> RoleResponse:
	"""
	Get a role by ID.

	Args:
	    role_id (UUID): Role ID.

	Returns:
	    RoleResponse: Role data.
	"""
	return service.get_role_by_id(role_id)


def update(role_id: UUID, updates: RoleUpdate) -> RoleResponse:
	"""
	Update a role.

	Args:
	    role_id (UUID): Role ID to update.
	    updates (RoleUpdate): Updates to apply.

	Returns:
	    RoleResponse: Updated role data.
	"""
	return service.update_role(role_id, updates)


def delete(role_id: UUID, delete_reason: str):
	"""
	Delete a role (soft delete).

	Args:
	    role_id (UUID): Role ID to delete.
	    delete_reason (str): Reason for deletion.
	"""
	service.delete_role(role_id, delete_reason)


def assign_permissions_to_role(request: RolePermissionAssignRequest) -> RoleResponse:
	"""
	Assign multiple permissions to a role.

	Args:
	    request (RolePermissionAssignRequest): Assignment request data.

	Returns:
	    RolePermissionsResponse: Assignment response data.
	"""
	return service.assign_permissions_to_role(request)


def unassign_permissions_from_role(role_id: UUID, permission_ids: List[UUID] = None) -> None:
	"""
	Unassign permissions from a role.

	Args:
	    role_id (UUID): Role ID to unassign permissions from.
	    permission_ids (List[UUID], optional): Specific permission IDs to unassign.
	"""
	service.unassign_permissions_from_role(role_id, permission_ids)
