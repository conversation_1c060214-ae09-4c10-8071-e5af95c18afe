from fastapi import Depends
from starlette.requests import Request

from src.core.dtos.notification_dtos import NotificationDto
from src.core.guards.auth_guard import auth_guard
from src.core.shared_schema import Pagination
from src.modules.notifications.notification_schema import NotificationFilter
from src.modules.notifications.notification_service import notification_service


@auth_guard.authenticated
async def fetch_notifications_handler(
	_: Request,
	filter: NotificationFilter = Depends(NotificationFilter),
) -> Pagination[NotificationDto]:
	notifications = await notification_service.retrieve_notifications(filter)
	return notifications
