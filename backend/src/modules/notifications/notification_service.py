from fastapi_pagination import Params
from fastapi_pagination.ext.sqlalchemy import paginate

from src.config.db.models import Notification
from src.core.base.base_repository import BaseRepository
from sqlalchemy.orm import Session as OrmSession

from src.core.dtos.notification_dtos import NotificationDto, to_notification_dto
from src.core.logger.internal_logger import get_logger
from src.core.shared_schema import Pagination
from src.modules.notifications.notification_schema import NotificationFilter


class NotificationService(BaseRepository):
	def __init__(self, db: OrmSession = None):
		if db is not None:
			self.db = db
		else:
			super().__init__()

		self.logger = get_logger(__name__)

	async def retrieve_notifications(self, filter: NotificationFilter) -> Pagination[NotificationDto]:
		try:
			query = self.db.query(Notification)

			if filter.priority is not None:
				query = query.filter(Notification.priority == filter.priority)

			if filter.start_date:
				query = query.filter(Notification.created_at >= filter.start_date)

			if filter.end_date:
				query = query.filter(Notification.created_at <= filter.end_date)

			query_result = paginate(query, Params(page=filter.page, size=filter.size))
			data = [to_notification_dto(row) for row in query_result.items]

			return Pagination.from_query_result(data, query_result)
		except Exception as e:
			self.logger.error(f"Failed to retrieve notifications: {str(e)}")
			raise


notification_service = NotificationService()
