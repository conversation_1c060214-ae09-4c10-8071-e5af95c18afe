from fastapi import APIRouter

from src.core.dtos.notification_dtos import NotificationDto
from src.core.shared_schema import Pagination
from src.modules.notifications import notification_controller as controller

notification_router_v1 = APIRouter(tags=["notifications"])

notification_router_v1.add_api_route(
	path="/",
	methods=["GET"],
	endpoint=controller.fetch_notifications_handler,
	response_model=Pagination[NotificationDto],
)
