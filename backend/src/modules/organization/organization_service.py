import secrets
import string
from typing import List, Optional

from fastapi import UploadFile
from fastapi_pagination import Params
from fastapi_pagination.ext.sqlalchemy import paginate
from pydantic import UUID4
from sqlalchemy import and_, or_
from sqlalchemy.exc import IntegrityError

from src.config.db.models import (
	BankDetail,
	FundingSource,
	LocationActivity,
	Organization,
	OrganizationAuditor,
	OrganizationDonor,
	OrganizationProject,
	OrganizationSector,
	OrganizationStaff,
	TargetGroup,
)
from src.config.db.models.account import Account, AccountType
from src.config.db.models.director import Director
from src.config.db.models.organization import OrganizationStatus
from src.core.base.base_repository import BaseRepository
from src.core.dtos.organization_dtos import (
	OrganizationDto,
	to_director_dto,
	DirectorDto,
	LocationActivityDto,
	to_location_activity_dto,
	TargetGroupDto,
	to_target_group_dto,
	OrganizationStaffDto,
	to_organization_staff_dto,
	OrganizationProjectDto,
	to_organization_project_dto,
	OrganizationDonorDto,
	to_organization_donor_dto,
	OrganizationAuditorDto,
	to_organization_auditor_dto,
	FundingSourceDto,
	to_funding_source_dto,
	BankDetailsDto,
	to_bank_details_dto,
	OrganizationSectorDto,
	to_organization_sector_dto,
)
from src.core.exceptions.api import ApiException
from src.core.logger.internal_logger import get_logger
from src.core.shared_schema import CurrentUser, Pagination, VoidRequest
from src.core.utils import serializer
from src.core.utils.common import capitalize_words
from src.modules.application.application_service import ApplicationService
from src.modules.document.document_service import DocumentService
from src.modules.settings.settings_service import settings_service

from .membership_service import MembershipService
from .organization_schema import (
	BankDetailsRequest,
	DirectorRequest,
	FundingSourceRequest,
	LocationActivityRequest,
	OrganizationAuditorRequest,
	OrganizationDonorRequest,
	OrganizationFilter,
	OrganizationProjectRequest,
	OrganizationRequest,
	OrganizationStaffRequest,
	SupportingDocumentRequest,
	TargetGroupRequest,
)


class OrganizationService(BaseRepository):
	def __init__(self):
		super().__init__()
		self.logger = get_logger(__name__)
		self.document_service = DocumentService(db=self.db)
		self.application_service = ApplicationService(db=self.db)
		self.settings_service = settings_service
		self.membership_service = MembershipService(db=self.db)

	@staticmethod
	async def _generate_registration_number() -> str:
		"""Generate a unique registration number for the organization."""
		# Generate a random alphanumeric string
		prefix = "ORG"
		random_part = "".join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(8))
		return f"{prefix}{random_part}"

	async def _validate_organization_data(self, data: OrganizationRequest) -> None:
		"""Validate organization data before creation."""

		if (
			self.db.query(Organization)
			.join(Account, Organization.account)
			.filter(Organization.name == data.name)
			.first()
		):
			raise ApiException("Organization with this name already exists")

		if not self.settings_service.get_district_by_id(data.district_id):
			raise ApiException("Not a valid district")

		organization_type = self.settings_service.get_loadable_item_by_id(data.organization_type_id)
		registration_type = self.settings_service.get_loadable_item_by_id(data.registration_type_id)

		if not organization_type or organization_type.type != "ORGANIZATION_TYPE":
			raise ApiException("Invalid organization type")

		if not registration_type or registration_type.type != "REGISTRATION_TYPE":
			raise ApiException("Invalid registration type")

		if data.annual_income < 0:
			raise ApiException("Annual income cannot be negative")

	async def retrieve_organizations(self, filter: OrganizationFilter) -> Pagination[OrganizationDto]:
		"""Retrieve organizations based on filter criteria."""
		try:
			query = self.db.query(Organization).join(Account, Organization.account)

			if filter.abbreviation:
				query = query.filter(Organization.abbreviation.ilike(f"%{filter.abbreviation}%"))

			if filter.district_id:
				query = query.filter(Organization.district_id == filter.district_id)

			if filter.name:
				query = query.filter(Organization.name.ilike(f"%{filter.name}%"))

			if filter.registration_number:
				query = query.filter(Organization.registration_number.ilike(f"%{filter.registration_number}%"))

			if filter.registration_type_id:
				query = query.filter(Organization.registration_type_id == filter.registration_type_id)

			if filter.status:
				query = query.filter(Organization.status == filter.status)

			paginated_result = paginate(query, Params(page=filter.page, size=filter.size))
			organizations = [serializer.to_organization_dto(row) for row in paginated_result.items]

			return Pagination.from_query_result(organizations, paginated_result)

		except Exception as e:
			self.logger.error(f"Failed to retrieve organizations: {str(e)}")
			raise ApiException("Failed to retrieve organizations")

	async def add_directors(self, org_id: UUID4, payload: List[DirectorRequest]) -> List[Director]:
		try:
			existing = (
				self.db.query(Director)
				.filter(
					and_(
						Director.organization_id == org_id,
						or_(Director.email == payload.email, Director.phone == payload.phone),
					)
				)
				.first()
			)

			if existing:
				raise ApiException("Director with the same phone or email already exists")

			directors_count = self.db.query(Director).filter(Director.organization_id == org_id).count()
			total_directors = directors_count + len(payload)

			if total_directors < 5:
				raise ApiException("You must provide 5 or more directors")

			directors = [
				Director(
					organization_id=org_id,
					fullname=director.fullname,
					email=director.email,
					phone=director.phone,
					avatar=director.avatar,
					national_identifier=director.national_identifier,
					passport_number=director.passport_number,
					gender=director.gender,
					position=director.position,
					country_id=director.country_id,
					occupation=director.occupation,
					timeframe=director.timeframe,
					qualification_id=director.qualification_id,
					created_by=self.current_user.id,
				)
				for director in payload
			]

			self.db.add_all(directors)
			self.db.flush()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return directors
		except ApiException as e:
			raise e
		except Exception as e:
			self.logger.error(f"Failed to add directors to an organizations: {str(e)}")
			raise e

	async def add_organization_sectors(self, org_id: UUID4, payload: List[UUID4]):
		try:
			organization_sectors = []
			for sector in payload:
				existing = (
					self.db.query(OrganizationSector)
					.filter(and_(OrganizationSector.sector_id == sector, OrganizationSector.organization_id == org_id))
					.first()
				)
				loadable_item = self.settings_service.get_loadable_item_by_id(sector)
				if not loadable_item:
					raise ApiException("Cannot add unknown sector to organization")

				if loadable_item.type != "SECTOR":
					raise ApiException("You have selected an invalid sector")

				if existing:
					raise ApiException(f"{loadable_item.name} sector was already added to the organization")

				organization_sectors.append(
					OrganizationSector(organization_id=org_id, sector_id=sector, created_by=self.current_user.id)
				)

			self.db.add_all(organization_sectors)
			self.db.flush()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return organization_sectors
		except Exception as e:
			self.logger.error(f"Failed to add organization sectors: {str(e)}")
			raise e

	async def add_location_activities(self, org_id: UUID4, items: List[LocationActivityRequest]):
		try:
			location_activities: List[LocationActivity] = []
			for item in items:
				existing = (
					self.db.query(LocationActivity)
					.filter(
						and_(
							LocationActivity.organization_id == org_id,
							LocationActivity.vdc_id == item.vdc_id,
							LocationActivity.adc_id == item.adc_id,
						),
					)
					.first()
				)

				if existing:
					raise ApiException("Location activity already exists")

				location_activities.append(
					LocationActivity(
						organization_id=org_id,
						district_id=item.district_id,
						vdc_id=item.vdc_id,
						adc_id=item.adc_id,
						created_by=self.current_user.id,
					)
				)

			self.db.add_all(location_activities)
			self.db.flush()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return location_activities
		except Exception as e:
			self.logger.error(f"Failed to add location activities: {str(e)}")
			raise e

	async def add_target_groups(self, org_id: UUID4, items: List[TargetGroupRequest]):
		try:
			target_groups: List[TargetGroup] = []
			for item in items:
				existing = (
					self.db.query(TargetGroup)
					.filter(
						and_(TargetGroup.organization_id == org_id, TargetGroup.type_id == item.type_id),
					)
					.first()
				)

				if existing:
					raise ApiException(f"Target group {existing.type.name} already exists")

				target_groups.append(
					TargetGroup(
						organization_id=org_id,
						type_id=item.type_id,
						is_active=item.is_active,
						created_by=self.current_user.id,
					)
				)

			self.db.add_all(target_groups)
			self.db.flush()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return target_groups
		except Exception as e:
			self.logger.error(f"Failed to add target groups: {str(e)}")
			raise e

	async def add_organization_staff(self, org_id: UUID4, items: List[OrganizationStaffRequest]):
		try:
			staff: List[OrganizationStaff] = []
			for item in items:
				existing = (
					self.db.query(OrganizationStaff)
					.filter(
						and_(
							OrganizationStaff.organization_id == org_id,
							OrganizationStaff.staff_type_id == item.staff_type_id,
						),
					)
					.first()
				)

				if existing:
					raise ApiException(f"Staff {existing.staff.name} already exists")

				staff.append(
					OrganizationStaff(
						organization_id=org_id,
						staff_type_id=item.staff_type_id,
						is_active=item.is_active,
						total=item.total,
						created_by=self.current_user.id,
					)
				)

			self.db.add_all(staff)
			self.db.flush()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return staff
		except Exception as e:
			self.logger.error(f"Failed to add organization staff: {str(e)}")
			raise e

	async def add_organization_projects(self, org_id: UUID4, items: List[OrganizationProjectRequest]):
		try:
			projects: List[OrganizationProject] = []
			for item in items:
				existing = (
					self.db.query(OrganizationProject)
					.filter(
						and_(
							OrganizationProject.organization_id == org_id,
							OrganizationProject.thematic_area_id == item.thematic_area_id,
						),
					)
					.first()
				)

				if existing:
					raise ApiException(f"Donor {existing.thematic_area.name} already exists")

				projects.append(
					OrganizationProject(
						organization_id=org_id,
						name=capitalize_words(item.name),
						is_active=item.is_active,
						number_of_beneficiaries=item.number_of_beneficiaries,
						thematic_area_id=item.thematic_area_id,
						created_by=self.current_user.id,
					)
				)

			self.db.add_all(projects)
			self.db.flush()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return projects
		except Exception as e:
			self.logger.error(f"Failed to add organization projects: {str(e)}")
			raise e

	async def add_organization_donor(self, org_id: UUID4, items: List[OrganizationDonorRequest]):
		try:
			donors = []
			for item in items:
				existing = (
					self.db.query(OrganizationDonor)
					.filter(
						and_(OrganizationDonor.organization_id == org_id, OrganizationDonor.donor_id == item.donor_id),
					)
					.first()
				)

				if existing:
					raise ApiException(f"Donor {existing.donor.name} already exists")

				donors.append(
					OrganizationDonor(
						organization_id=org_id,
						donor_id=item.donor_id,
						amount=item.amount,
						created_by=self.current_user.id,
					)
				)

			self.db.add_all(donors)
			self.db.flush()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return self.db.query(OrganizationDonor).filter(OrganizationDonor.organization_id == org_id).all()

		except Exception as e:
			self.logger.error(f"Failed to add organization donor: {str(e)}")
			raise e

	async def add_organization_auditor(self, org_id: UUID4, items: List[OrganizationAuditorRequest]):
		try:
			auditors = []
			for item in items:
				existing_auditor = (
					self.db.query(OrganizationAuditor)
					.filter(
						or_(
							and_(
								OrganizationAuditor.organization_id == org_id, OrganizationAuditor.email == item.email
							),
							and_(
								OrganizationAuditor.organization_id == org_id, OrganizationAuditor.phone == item.phone
							),
						)
					)
					.first()
				)

				if existing_auditor:
					raise ApiException("Auditor with the same email or phone already exists")

				auditors.append(
					OrganizationAuditor(
						organization_id=org_id,
						name=item.name,
						email=item.email,
						phone=item.phone,
						address=item.address,
						created_by=self.current_user.id,
					)
				)

			self.db.add_all(auditors)
			self.db.flush()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return auditors
		except Exception as e:
			self.logger.error(f"Failed to add organization auditor: {str(e)}")
			raise e

	async def add_funding_sources(self, org_id: UUID4, items: List[FundingSourceRequest]):
		try:
			funding_sources: List[FundingSource] = []
			for item in items:
				funding_sources.append(
					FundingSource(
						organization_id=org_id,
						donor_id=item.donor_id,
						currency_id=item.currency_id,
						contact_person=capitalize_words(item.contact_person),
						amount=item.amount,
						created_by=self.current_user.id,
					)
				)

			self.db.add_all(funding_sources)
			self.db.flush()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return funding_sources
		except Exception as e:
			self.logger.error(f"Failed to add organization funding sources: {str(e)}")
			raise e

	async def add_bank_details(self, org_id: UUID4, items: List[BankDetailsRequest]):
		try:
			bank_details: List[BankDetail] = []
			for item in items:
				bank_details.append(
					BankDetail(
						organization_id=org_id,
						account_number=item.account_number,
						branch_name=item.branch_name,
						bank_id=item.bank_id,
						created_by=self.current_user.id,
					)
				)

			self.db.add_all(bank_details)
			self.db.flush()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return bank_details
		except Exception as e:
			self.logger.error(f"Failed to add bank details: {str(e)}")
			raise e

	async def create_account(self, data: OrganizationRequest) -> Account:
		account = Account(type=AccountType.ORG, handle=data.abbreviation, created_by=self.current_user.id)
		self.db.add(account)
		self.db.flush()
		return account

	async def add_related_entities(
		self,
		organization_id: UUID4,
		data: OrganizationRequest,
		supporting_documents: List[UploadFile],
	):
		application = await self.application_service.create_organization_registration(
			organization_id, data.annual_income
		)

		uploaded_documents = SupportingDocumentRequest()
		uploaded_documents.documents = supporting_documents
		uploaded_documents.document_types = data.document_types

		await self.add_directors(organization_id, data.directors)
		await self.document_service.add_application_documents(application.id, uploaded_documents)
		await self.add_organization_sectors(organization_id, data.sectors)
		await self.membership_service.create_fault_member(organization_id)
		await self.add_organization_auditor(organization_id, data.auditors)
		await self.add_organization_projects(organization_id, data.projects)
		await self.add_organization_donor(organization_id, data.donors)
		await self.add_organization_staff(organization_id, data.staffs)
		await self.add_bank_details(organization_id, data.bank_details)
		await self.add_target_groups(organization_id, data.target_groups)
		await self.add_funding_sources(organization_id, data.funding_sources)
		await self.add_location_activities(organization_id, data.location_activities)

		return application

	async def create_organization(
		self, data: OrganizationRequest, supporting_documents: List[UploadFile]
	) -> OrganizationDto:
		"""Create a new organization."""
		try:
			with self.db.begin_nested():
				await self._validate_organization_data(data)
				registration_number = self._generate_registration_number()

				while (
					self.db.query(Organization).filter(Organization.registration_number == registration_number).first()
				):
					registration_number = self._generate_registration_number()

				account = await self.create_account(data)

				organization = Organization(
					name=data.name,
					abbreviation=data.abbreviation,
					organization_type_id=data.organization_type_id,
					registration_number=registration_number,
					district_id=data.district_id,
					financial_start_month=data.financial_start_month,
					financial_end_month=data.financial_end_month,
					charity_number=data.charity_number if data.charity_number else None,
					annual_income=float(data.annual_income),
					registration_type_id=data.registration_type_id,
					account_id=account.id,
					biography=data.biography,
					vision=data.vision,
					motto=data.motto,
					objectives=data.objectives if data.objectives else None,
					status=OrganizationStatus.DRAFT,
					created_by=self.current_user.id,
				)

				self.db.add(organization)
				self.db.flush()

				application = await self.add_related_entities(organization.id, data, supporting_documents)

				if application:
					self.db.commit()

			return serializer.to_organization_dto(organization)

		except Exception as e:
			self.db.rollback()
			self.logger.error(str(e))
			raise

	async def get_organization_by_id(self, organization_id: str) -> Optional[OrganizationDto]:
		"""Retrieve a single organization by ID."""
		try:
			organization = self.db.query(Organization).filter(Organization.id == organization_id).first()

			if not organization:
				return None

			return serializer.to_organization_dto(organization)

		except Exception as e:
			self.logger.error(f"Failed to retrieve organization by ID {organization_id}: {str(e)}")
			raise ApiException("Failed to retrieve organization")

	async def update_organization(
		self, organization_id: str, user: CurrentUser, data: OrganizationRequest
	) -> OrganizationDto:
		"""Update an existing organization."""
		try:
			with self.db.begin():
				organization = self.db.query(Organization).filter(Organization.id == organization_id).first()

				if not organization:
					raise ApiException("Organization not found")

				organization.name = data.name
				organization.abbreviation = data.abbreviation
				organization.organization_type_id = data.organization_type_id
				organization.district_id = data.district_id
				organization.financial_start_month = data.financial_start_month
				organization.financial_end_month = data.financial_end_month
				organization.charity_number = data.charity_number if data.charity_number else None
				organization.annual_income = float(data.annual_income)
				organization.registration_type_id = data.registration_type_id
				organization.biography = data.biography
				organization.vision = data.vision
				organization.motto = data.motto
				organization.objectives = data.objectives if data.objectives else None
				organization.updated_by = user.id

				self.db.commit()
				self.db.refresh(organization)

				self.logger.info(f"Successfully updated organization: {organization.name} (ID: {organization.id})")
				return serializer.to_organization_dto(organization)

		except IntegrityError as e:
			self.db.rollback()
			self.logger.error(f"Database integrity error while updating organization: {str(e)}")
			raise ApiException("Failed to update organization: Duplicate data detected")

		except ApiException:
			self.db.rollback()
			raise

		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Unexpected error while updating organization: {str(e)}")
			raise ApiException("Failed to update organization")

	async def delete_organization(self, organization_id: str, user: CurrentUser, payload: VoidRequest) -> bool:
		"""Delete an organization (soft delete by changing status)."""
		try:
			organization = self.db.query(Organization).filter(Organization.id == organization_id).first()

			if not organization:
				raise ApiException("Organization not found")

			if organization.status is not OrganizationStatus.DRAFT.value:
				raise ApiException("Organization cannot be deleted. Request for deregistration")

			organization.status = OrganizationStatus.INACTIVE
			organization.voided = True
			organization.voided_by = user.id
			organization.void_reason = payload.void_reason

			self.db.commit()

			self.logger.info(f"Successfully deleted organization: {organization.name} (ID: {organization.id})")
			return True

		except ApiException:
			self.db.rollback()
			raise

		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to delete organization {organization_id}: {str(e)}")
			raise ApiException("Failed to delete organization")

	# RETRIEVE METHODS

	async def get_directors(self, org_id: UUID4) -> List[DirectorDto]:
		try:
			rows = self.db.query(Director).filter(Director.organization_id == org_id).all()
			return [to_director_dto(row) for row in rows]
		except Exception as e:
			self.logger.error(f"Failed to get directors: {str(e)}")
			raise

	async def get_organization_sectors(self, org_id: UUID4) -> List[OrganizationSectorDto]:
		try:
			rows = self.db.query(OrganizationSector).filter(OrganizationSector.organization_id == org_id).all()
			return [to_organization_sector_dto(row) for row in rows]
		except Exception as e:
			self.logger.error(f"Failed to get sectors: {str(e)}")
			raise

	async def get_location_activities(self, org_id: UUID4) -> List[LocationActivityDto]:
		try:
			rows = self.db.query(LocationActivity).filter(LocationActivity.organization_id == org_id).all()
			return [to_location_activity_dto(row) for row in rows]
		except Exception as e:
			self.logger.error(f"Failed to get location activities: {str(e)}")
			raise

	async def get_target_groups(self, org_id: UUID4) -> List[TargetGroupDto]:
		try:
			rows = self.db.query(TargetGroup).filter(TargetGroup.organization_id == org_id).all()
			return [to_target_group_dto(row) for row in rows]
		except Exception as e:
			self.logger.error(f"Failed to get target groups: {str(e)}")
			raise

	async def get_organization_staff(self, org_id: UUID4) -> List[OrganizationStaffDto]:
		try:
			rows = self.db.query(OrganizationStaff).filter(OrganizationStaff.organization_id == org_id).all()
			return [to_organization_staff_dto(row) for row in rows]
		except Exception as e:
			self.logger.error(f"Failed to get organization staff: {str(e)}")
			raise

	async def get_organization_projects(self, org_id: UUID4) -> List[OrganizationProjectDto]:
		try:
			rows = self.db.query(OrganizationProject).filter(OrganizationProject.organization_id == org_id).all()
			return [to_organization_project_dto(row) for row in rows]
		except Exception as e:
			self.logger.error(f"Failed to get organization projects: {str(e)}")
			raise

	async def get_organization_donors(self, org_id: UUID4) -> List[OrganizationDonorDto]:
		try:
			rows = self.db.query(OrganizationDonor).filter(OrganizationDonor.organization_id == org_id).all()
			return [to_organization_donor_dto(row) for row in rows]
		except Exception as e:
			self.logger.error(f"Failed to get organization donors: {str(e)}")
			raise

	async def get_organization_auditors(self, org_id: UUID4) -> List[OrganizationAuditorDto]:
		try:
			rows = self.db.query(OrganizationAuditor).filter(OrganizationAuditor.organization_id == org_id).all()
			return [to_organization_auditor_dto(row) for row in rows]
		except Exception as e:
			self.logger.error(f"Failed to get organization auditors: {str(e)}")
			raise

	async def get_funding_sources(self, org_id: UUID4) -> List[FundingSourceDto]:
		try:
			rows = self.db.query(FundingSource).filter(FundingSource.organization_id == org_id).all()
			return [to_funding_source_dto(row) for row in rows]
		except Exception as e:
			self.logger.error(f"Failed to get funding sources: {str(e)}")
			raise

	async def get_bank_details(self, org_id: UUID4) -> List[BankDetailsDto]:
		try:
			rows = self.db.query(BankDetail).filter(BankDetail.organization_id == org_id).all()
			return [to_bank_details_dto(row) for row in rows]
		except Exception as e:
			self.logger.error(f"Failed to get bank details: {str(e)}")
			raise

	# EDIT METHODS

	async def update_director(self, org_id: UUID4, director_id: UUID4, payload: DirectorRequest) -> Director:
		try:
			director = self.db.query(Director).filter(Director.id == director_id).first()

			if not director:
				raise ApiException("Director not found")

			existing = (
				self.db.query(Director)
				.filter(
					and_(
						Director.organization_id == org_id,
						or_(Director.email == payload.email, Director.phone == payload.phone),
					)
				)
				.first()
			)

			if existing:
				raise ApiException("Director with the same phone or email already exists")

			director.fullname = payload.fullname
			director.email = payload.email
			director.phone = payload.phone
			director.avatar = payload.avatar
			director.national_identifier = payload.national_identifier
			director.passport_number = payload.passport_number
			director.gender = payload.gender
			director.position = payload.position
			director.country_id = payload.country_id
			director.occupation = payload.occupation
			director.timeframe = payload.timeframe
			director.qualification_id = payload.qualification_id
			director.updated_by = self.current_user.id

			self.db.flush()
			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return director
		except ApiException as e:
			raise e
		except Exception as e:
			self.logger.error(f"Failed to edit director: {str(e)}")
			raise e

	async def update_organization_sector(self, sector_id: UUID4, payload: UUID4) -> OrganizationSector:
		try:
			org_sector = self.db.query(OrganizationSector).filter(OrganizationSector.id == sector_id).first()

			if not org_sector:
				raise ApiException("Organization sector not found")

			loadable_item = self.settings_service.get_loadable_item_by_id(payload)
			if not loadable_item:
				raise ApiException("Cannot update to unknown sector")
			if loadable_item.type != "SECTOR":
				raise ApiException("You have selected an invalid sector")

			existing = (
				self.db.query(OrganizationSector)
				.filter(
					OrganizationSector.sector_id == payload,
					OrganizationSector.organization_id == org_sector.organization_id,
					OrganizationSector.id != sector_id,
				)
				.first()
			)
			if existing:
				raise ApiException(f"{loadable_item.name} sector already exists for this organization")

			org_sector.sector_id = payload
			org_sector.updated_by = self.current_user.id

			self.db.flush()
			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return org_sector
		except ApiException as e:
			raise e
		except Exception as e:
			self.logger.error(f"Failed to edit organization sector: {str(e)}")
			raise e

	async def update_location_activity(self, activity_id: UUID4, payload: LocationActivityRequest) -> LocationActivity:
		try:
			activity = self.db.query(LocationActivity).filter(LocationActivity.id == activity_id).first()

			if not activity:
				raise ApiException("Location activity not found")

			existing = (
				self.db.query(LocationActivity)
				.filter(
					LocationActivity.organization_id == activity.organization_id,
					LocationActivity.vdc_id == payload.vdc_id,
					LocationActivity.adc_id == payload.adc_id,
					LocationActivity.id != activity_id,
				)
				.first()
			)
			if existing:
				raise ApiException("Location activity already exists for this combination")

			activity.district_id = payload.district_id
			activity.vdc_id = payload.vdc_id
			activity.adc_id = payload.adc_id
			activity.updated_by = self.current_user.id

			self.db.flush()
			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return activity
		except ApiException as e:
			raise e
		except Exception as e:
			self.logger.error(f"Failed to edit location activity: {str(e)}")
			raise e

	async def update_target_group(self, group_id: UUID4, payload: TargetGroupRequest) -> TargetGroup:
		try:
			target_group = self.db.query(TargetGroup).filter(TargetGroup.id == group_id).first()

			if not target_group:
				raise ApiException("Target group not found")

			existing = (
				self.db.query(TargetGroup)
				.filter(
					TargetGroup.organization_id == target_group.organization_id,
					TargetGroup.type_id == payload.type_id,
					TargetGroup.id != group_id,
				)
				.first()
			)
			if existing:
				raise ApiException("Target group with this type already exists")

			target_group.type_id = payload.type_id
			target_group.is_active = payload.is_active
			target_group.updated_by = self.current_user.id

			self.db.flush()
			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return target_group
		except ApiException as e:
			raise e
		except Exception as e:
			self.logger.error(f"Failed to edit target group: {str(e)}")
			raise e

	async def update_organization_staff(self, staff_id: UUID4, payload: OrganizationStaffRequest) -> OrganizationStaff:
		try:
			staff = self.db.query(OrganizationStaff).filter(OrganizationStaff.id == staff_id).first()

			if not staff:
				raise ApiException("Organization staff not found")

			existing = (
				self.db.query(OrganizationStaff)
				.filter(
					OrganizationStaff.organization_id == staff.organization_id,
					OrganizationStaff.staff_type_id == payload.staff_type_id,
					OrganizationStaff.id != staff_id,
				)
				.first()
			)
			if existing:
				raise ApiException("Staff with this type already exists")

			staff.staff_type_id = payload.staff_type_id
			staff.is_active = payload.is_active
			staff.total = payload.total
			staff.updated_by = self.current_user.id

			self.db.flush()
			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return staff
		except ApiException as e:
			raise e
		except Exception as e:
			self.logger.error(f"Failed to edit organization staff: {str(e)}")
			raise e

	async def update_organization_project(
		self, project_id: UUID4, payload: OrganizationProjectRequest
	) -> OrganizationProject:
		try:
			project = self.db.query(OrganizationProject).filter(OrganizationProject.id == project_id).first()

			if not project:
				raise ApiException("Organization project not found")

			existing = (
				self.db.query(OrganizationProject)
				.filter(
					OrganizationProject.organization_id == project.organization_id,
					OrganizationProject.thematic_area_id == payload.thematic_area_id,
					OrganizationProject.id != project_id,
				)
				.first()
			)
			if existing:
				raise ApiException("Project with this thematic area already exists")

			project.name = capitalize_words(payload.name)
			project.is_active = payload.is_active
			project.thematic_area_id = payload.thematic_area_id
			project.updated_by = self.current_user.id
			project.number_of_beneficiaries = payload.number_of_beneficiaries

			self.db.flush()
			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return project
		except ApiException as e:
			raise e
		except Exception as e:
			self.logger.error(f"Failed to edit organization project: {str(e)}")
			raise e

	async def update_organization_donor(self, donor_id: UUID4, payload: OrganizationDonorRequest) -> OrganizationDonor:
		try:
			org_donor = self.db.query(OrganizationDonor).filter(OrganizationDonor.id == donor_id).first()

			if not org_donor:
				raise ApiException("Organization donor not found")

			existing = (
				self.db.query(OrganizationDonor)
				.filter(
					OrganizationDonor.organization_id == org_donor.organization_id,
					OrganizationDonor.donor_id == payload.donor_id,
					OrganizationDonor.id != donor_id,
				)
				.first()
			)
			if existing:
				raise ApiException("Donor already exists for this organization")

			org_donor.donor_id = payload.donor_id
			org_donor.amount = payload.amount
			org_donor.updated_by = self.current_user.id

			self.db.flush()
			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return org_donor
		except ApiException as e:
			raise e
		except Exception as e:
			self.logger.error(f"Failed to edit organization donor: {str(e)}")
			raise e

	async def update_organization_auditor(
		self, auditor_id: UUID4, payload: OrganizationAuditorRequest
	) -> OrganizationAuditor:
		try:
			auditor = self.db.query(OrganizationAuditor).filter(OrganizationAuditor.id == auditor_id).first()

			if not auditor:
				raise ApiException("Organization auditor not found")

			existing = (
				self.db.query(OrganizationAuditor)
				.filter(
					OrganizationAuditor.organization_id == auditor.organization_id,
					or_(OrganizationAuditor.email == payload.email, OrganizationAuditor.phone == payload.phone),
					OrganizationAuditor.id != auditor_id,
				)
				.first()
			)
			if existing:
				raise ApiException("Auditor with the same email or phone already exists")

			auditor.name = payload.name
			auditor.email = payload.email
			auditor.phone = payload.phone
			auditor.address = payload.address
			auditor.updated_by = self.current_user.id

			self.db.flush()
			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return auditor
		except ApiException as e:
			raise e
		except Exception as e:
			self.logger.error(f"Failed to edit organization auditor: {str(e)}")
			raise e

	async def update_funding_source(self, source_id: UUID4, payload: FundingSourceRequest) -> FundingSource:
		try:
			funding_source = self.db.query(FundingSource).filter(FundingSource.id == source_id).first()

			if not funding_source:
				raise ApiException("Funding source not found")

			funding_source.donor_id = payload.donor_id
			funding_source.currency_id = payload.currency_id
			funding_source.contact_person = capitalize_words(payload.contact_person)
			funding_source.amount = payload.amount
			funding_source.updated_by = self.current_user.id

			self.db.flush()
			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return funding_source
		except Exception as e:
			self.logger.error(f"Failed to edit funding source: {str(e)}")
			raise e

	async def update_bank_detail(self, detail_id: UUID4, payload: BankDetailsRequest) -> BankDetail:
		try:
			bank_detail = self.db.query(BankDetail).filter(BankDetail.id == detail_id).first()

			if not bank_detail:
				raise ApiException("Bank detail not found")

			bank_detail.account_number = payload.account_number
			bank_detail.branch_name = payload.branch_name
			bank_detail.bank_id = payload.bank_id
			bank_detail.updated_by = self.current_user.id

			self.db.flush()
			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return bank_detail
		except Exception as e:
			self.logger.error(f"Failed to edit bank detail: {str(e)}")
			raise e

	# DELETE METHODS (Soft Delete - Set voided to True)

	async def delete_director(self, director_id: UUID4) -> bool:
		try:
			director = self.db.query(Director).filter(Director.id == director_id).first()

			if not director:
				raise ApiException("Director not found")

			# Check if deleting this director would result in less than 5 directors
			remaining_directors = (
				self.db.query(Director)
				.filter(
					Director.organization_id == director.organization_id,
					Director.id != director_id,
				)
				.count()
			)

			if remaining_directors < 4:
				raise ApiException("Cannot delete director. Organization must have at least 5 directors")

			director.voided = True
			director.updated_by = self.current_user.id

			self.db.flush()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return True
		except ApiException as e:
			raise e
		except Exception as e:
			self.logger.error(f"Failed to delete director: {str(e)}")
			raise e

	async def delete_organization_sector(self, sector_id: UUID4) -> bool:
		try:
			org_sector = self.db.query(OrganizationSector).filter(OrganizationSector.id == sector_id).first()

			if not org_sector:
				raise ApiException("Organization sector not found")

			org_sector.voided = True
			org_sector.updated_by = self.current_user.id

			self.db.flush()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return True
		except ApiException as e:
			raise e
		except Exception as e:
			self.logger.error(f"Failed to delete organization sector: {str(e)}")
			raise e

	async def delete_location_activity(self, activity_id: UUID4) -> bool:
		try:
			activity = self.db.query(LocationActivity).filter(LocationActivity.id == activity_id).first()

			if not activity:
				raise ApiException("Location activity not found")

			activity.voided = True
			activity.updated_by = self.current_user.id

			self.db.flush()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return True
		except ApiException as e:
			raise e
		except Exception as e:
			self.logger.error(f"Failed to delete location activity: {str(e)}")
			raise e

	async def delete_target_group(self, group_id: UUID4) -> bool:
		try:
			target_group = self.db.query(TargetGroup).filter(TargetGroup.id == group_id).first()

			if not target_group:
				raise ApiException("Target group not found")

			target_group.voided = True
			target_group.updated_by = self.current_user.id

			self.db.flush()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return True
		except ApiException as e:
			raise e
		except Exception as e:
			self.logger.error(f"Failed to delete target group: {str(e)}")
			raise e

	async def delete_organization_staff(self, staff_id: UUID4) -> bool:
		try:
			staff = self.db.query(OrganizationStaff).filter(OrganizationStaff.id == staff_id).first()

			if not staff:
				raise ApiException("Organization staff not found")

			staff.voided = True
			staff.updated_by = self.current_user.id

			self.db.flush()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return True
		except ApiException as e:
			raise e
		except Exception as e:
			self.logger.error(f"Failed to delete organization staff: {str(e)}")
			raise e

	async def delete_organization_project(self, project_id: UUID4) -> bool:
		try:
			project = self.db.query(OrganizationProject).filter(OrganizationProject.id == project_id).first()

			if not project:
				raise ApiException("Organization project not found")

			project.voided = True
			project.updated_by = self.current_user.id

			self.db.flush()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return True
		except ApiException as e:
			raise e
		except Exception as e:
			self.logger.error(f"Failed to delete organization project: {str(e)}")
			raise e

	async def delete_organization_donor(self, donor_id: UUID4) -> bool:
		try:
			org_donor = self.db.query(OrganizationDonor).filter(OrganizationDonor.id == donor_id).first()

			if not org_donor:
				raise ApiException("Organization donor not found")

			org_donor.voided = True
			org_donor.updated_by = self.current_user.id

			self.db.flush()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return True
		except ApiException as e:
			raise e
		except Exception as e:
			self.logger.error(f"Failed to delete organization donor: {str(e)}")
			raise e

	async def delete_organization_auditor(self, auditor_id: UUID4) -> bool:
		try:
			auditor = self.db.query(OrganizationAuditor).filter(OrganizationAuditor.id == auditor_id).first()

			if not auditor:
				raise ApiException("Organization auditor not found")

			auditor.voided = True
			auditor.updated_by = self.current_user.id

			self.db.flush()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return True
		except ApiException as e:
			raise e
		except Exception as e:
			self.logger.error(f"Failed to delete organization auditor: {str(e)}")
			raise e

	async def delete_funding_source(self, source_id: UUID4) -> bool:
		try:
			funding_source = self.db.query(FundingSource).filter(FundingSource.id == source_id).first()

			if not funding_source:
				raise ApiException("Funding source not found")

			funding_source.voided = True
			funding_source.updated_by = self.current_user.id

			self.db.flush()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return True
		except Exception as e:
			self.logger.error(f"Failed to delete funding source: {str(e)}")
			raise e

	async def delete_bank_detail(self, detail_id: UUID4) -> bool:
		try:
			bank_detail = self.db.query(BankDetail).filter(BankDetail.id == detail_id).first()

			if not bank_detail:
				raise ApiException("Bank detail not found")

			bank_detail.voided = True
			bank_detail.updated_by = self.current_user.id

			self.db.flush()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return True
		except Exception as e:
			self.logger.error(f"Failed to delete bank detail: {str(e)}")
			raise e


organization_service = OrganizationService()
