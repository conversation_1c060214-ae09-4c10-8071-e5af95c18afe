from datetime import datetime

from pydantic import UUID4
from sqlalchemy.orm import Session as OrmSession

from src.config.db.models import Member, MemberRole
from src.core.base.base_repository import BaseRepository
from src.core.exceptions.api import ApiException
from src.core.logger import logger


class MembershipService(BaseRepository):
	def __init__(self, db: OrmSession = None):
		if db is not None:
			self.db = db
		else:
			super().__init__()
		self.logger = logger

	async def create_fault_member(self, org_id: UUID4) -> Member:
		try:
			member = Member(
				organization_id=org_id,
				user_id=self.current_user.id,
				created_by=self.current_user.id,
				role=MemberRole.OWNER,
				joined_at=datetime.now(),
			)

			self.db.add(member)
			self.db.flush()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return member
		except Exception as e:
			self.logger.error(f"Failed to add organization members: {str(e)}")
			raise ApiException("Failed to add organization members")


membership_service = MembershipService()
