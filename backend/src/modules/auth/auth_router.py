from fastapi import APIRouter, status
from fastapi.responses import HTMLResponse
from fastapi.security import OAuth2PasswordBearer

import src.modules.auth.auth_controller as controller
import src.modules.auth.auth_schema as schema
from src.core.dtos.auth_dtos import AuthDto
from src.core.shared_schema import BaseResponse

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="login")

router = APIRouter(tags=["auth"])

router.add_api_route(
	path="/register",
	endpoint=controller.register,
	methods=["POST"],
	response_model=BaseResponse[AuthDto],
	status_code=status.HTTP_201_CREATED,
	summary="Register a new user",
	description="Register a new user account. Account will be inactive until email is verified.",
)

router.add_api_route(
	path="/login",
	endpoint=controller.login,
	methods=["POST"],
	response_model=BaseResponse[AuthDto],
	summary="Login to get access token",
	description="Authenticate with email and password to receive JWT tokens",
)

router.add_api_route(
	path="/2fa",
	endpoint=controller.verify_2fa,
	methods=["POST"],
	response_model=BaseResponse[AuthDto],
	summary="Post authentication",
	description="Get a new authenticated user's information",
)

router.add_api_route(
	path="/logout",
	endpoint=controller.logout,
	methods=["POST"],
	response_model=schema.LogoutResponse,
	summary="Logout user",
	description="Logout user from current session or all sessions",
)

router.add_api_route(
	path="/sessions",
	endpoint=controller.get_user_sessions,
	methods=["GET"],
	response_model=schema.UserSessionsResponse,
	summary="Get user sessions",
	description="Get all active sessions for the current user",
)

router.add_api_route(
	path="/refresh",
	endpoint=controller.refresh_token,
	methods=["POST"],
	response_class=HTMLResponse,
	summary="Refresh access token",
	description="Get a new access token using refresh token",
)

router.add_api_route(
	path="/me",
	endpoint=controller.get_current_user,
	methods=["GET"],
	response_model=schema.UserResponse,
	summary="Get current user",
	description="Get the current authenticated user's information",
)

router.add_api_route(
	path="/resend-account-verification",
	endpoint=controller.resend_verification,
	methods=["POST"],
	summary="Resend verification code email",
	description="Resend email verification link and code to user",
)

router.add_api_route(
	path="/request-password-reset",
	endpoint=controller.request_password_reset,
	methods=["POST"],
	response_model=BaseResponse[bool],
	summary="Request password reset",
	description="Request a password reset email",
)

router.add_api_route(
	path="/reset-password",
	endpoint=controller.reset_password,
	methods=["POST"],
	response_model=BaseResponse[bool],
	summary="Reset user password",
	description="Reset user password using reset token",
)
