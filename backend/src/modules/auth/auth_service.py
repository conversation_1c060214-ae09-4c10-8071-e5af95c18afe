import hashlib
import random
import secrets
import string
from datetime import datetime, timed<PERSON>ta
from typing import Dict, <PERSON>, Optional, Tu<PERSON>

from fastapi import Request
from fastapi.security import OAuth2Password<PERSON>earer
from sqlalchemy.orm import joinedload

from src.config import settings
from src.config.data.adjectives import adjectives
from src.config.db.models import (
	Account,
	AccountStatus,
	AccountType,
	AccountVerification,
	LoadableItem,
	RolePermission,
	Session,
	User,
	VerificationType,
)
from src.config.settings import CURRENT_USER_TTL, ROLE_PERMISSION_TTL, SESSION_TYPE_KEY_COOKIE
from src.core.base.base_repository import BaseRepository
from src.core.dtos.auth_dtos import AuthDto, AuthType
from src.core.exceptions.api import ApiException
from src.core.logger import logger
from src.core.services.cache_service import cache_service
from src.core.services.email_service import (
	EmailMessage,
	EmailRecipient,
	EmailTemplate,
	email_service,
	send_welcome_email,
)
from src.core.services.encryption_service import EncryptionService
from src.core.shared_schema import CurrentUser
from src.core.utils.common import is_password_strong
from src.modules.auth.auth_schema import (
	LoginRequest,
	RegistrationRequest,
	SessionInfo,
	VerificationRetryRequest,
	TwoFactorRequest,
	PasswordConfirmationRequest,
)


class AuthService(BaseRepository):
	def __init__(self):
		super().__init__()
		self.oauth2_scheme = OAuth2PasswordBearer(tokenUrl="v1/auth/login")
		self.token_expiry = settings.ACCESS_TOKEN_EXPIRE_MINUTES
		self.encryption_service = EncryptionService()
		self.cache_service = cache_service
		self.email_service = email_service

		self.REMEMBER_ME_DAYS = 30
		self.REGULAR_SESSION_DAYS = 7
		self.REMEMBER_ME_TOKEN_DAYS = 30
		self.REGULAR_TOKEN_HOURS = 24

	def _hash_token(self, token: str) -> str:
		"""Hash a token for secure storage"""
		return hashlib.sha256(token.encode()).hexdigest()

	async def generate_username(self, retries: int = 20) -> str:
		MAX_USERNAME_LENGTH = 15
		for _ in range(retries):
			choice = random.choice(adjectives)
			number = str(random.randint(100, 999))

			username = f"{choice}_{number}".lower()

			if len(username) > MAX_USERNAME_LENGTH:
				continue

			exists = self.db.query(Account).filter(Account.handle == username).first()
			if not exists:
				return username

		raise Exception("Failed to generate a unique username under 15 characters after several attempts.")

	def _extract_device_info(self, user_agent: str) -> str:
		"""Extract basic device info from user agent"""
		if not user_agent:
			return "Unknown"

		user_agent_lower = user_agent.lower()
		if "mobile" in user_agent_lower or "android" in user_agent_lower or "iphone" in user_agent_lower:
			return "Mobile"
		elif "tablet" in user_agent_lower or "ipad" in user_agent_lower:
			return "Tablet"
		else:
			return "Desktop"

	def _create_session(
		self, user: User, request: Request, key: str, tokens: dict, remember_me: bool = False
	) -> Session:
		"""Create a new user session"""
		refresh_token_hash = self._hash_token(tokens.get("refresh_token"))

		ip_address = request.client.host if request.client else "Unknown"
		user_agent = request.headers.get("user-agent", "")
		device = self._extract_device_info(user_agent)

		if remember_me:
			expires_at = datetime.now() + timedelta(days=self.REMEMBER_ME_DAYS)
		else:
			expires_at = datetime.now() + timedelta(days=self.REGULAR_SESSION_DAYS)

		session = Session(
			user_id=user.id,
			session_token=key,
			refresh_token_hash=refresh_token_hash,
			device=device,
			ip_address=ip_address,
			user_agent=user_agent,
			expires_at=expires_at,
			is_active=True,
			last_activity=datetime.now(),
			remember_me=remember_me,
		)

		self.db.add(session)
		self.db.flush()
		self.db.commit()
		return session

	def _generate_auth_tokens(self, user: User, remember_me: bool = False):
		access_delta = timedelta(hours=24) if remember_me else timedelta(minutes=self.token_expiry)
		refresh_delta = (
			timedelta(days=self.REMEMBER_ME_TOKEN_DAYS) if remember_me else timedelta(days=self.REGULAR_TOKEN_HOURS)
		)

		token_data = {
			"sub": user.account.handle,
			"user_id": str(user.id),
			"session_token": secrets.token_urlsafe(32),
			"email": user.email,
			"remember_me": remember_me,
		}

		return token_data["session_token"], self.encryption_service.generate_tokens(
			user_id=user.id,
			additional_data=token_data,
			access_token=access_delta,
			refresh_token=refresh_delta,
		)

	async def get_current_user(self, token: str) -> CurrentUser | None:
		"""
		Validate token and return current user with caching
		"""
		is_valid, payload = self.encryption_service.verify_token(token)

		if not is_valid or payload.get("type") != "access":
			return None

		user_id = payload.get("user_id")
		session_token = payload.get("session_token")

		if user_id is None or session_token is None:
			return None

		cached_user = await self.cache_service.get(f"user:{user_id}")
		if cached_user:
			return CurrentUser(**cached_user)

		user = self.db.query(User).join(Account, User.account).filter(User.id == user_id).first()

		if not user:
			return None

		session = self.db.query(Session).filter(Session.session_token == session_token).first()

		if session is None:
			return None

		if not session.is_active:
			return None

		if user.account.status != AccountStatus.ACTIVE.value and user.verified:
			return None

		role_id = user.roles[0].id if hasattr(user, "roles") and user.roles else None
		role_code = user.roles[0].code if hasattr(user, "roles") and user.roles else None

		current_user = CurrentUser(
			id=str(user.id),
			username=user.account.handle,
			role_id=str(role_id),
			role_code=role_code,
			session_token=session.session_token,
		)

		await self.cache_service.set(f"user:{user_id}", current_user.model_dump(), expire=3600)

		return current_user

	async def start_session(self, user: User, request: Request, remember_me: bool = True):
		session_token, auth_tokens = self._generate_auth_tokens(user, remember_me)

		session = self._create_session(user, request, session_token, auth_tokens, remember_me)

		current_user_data = {
			"id": str(user.id),
			"username": user.account.handle,
			"session_token": session.session_token,
			"role_id": None,
			"role_code": None,
		}

		if not user.is_external and user.verified:
			role = user.roles[0] if user.roles else None
			if role:
				await self.cache_role_permissions_cache(role.id, role.code)
				current_user_data["role_id"] = str(role.id)
				current_user_data["role_code"] = role.code

		if user.verified:
			await self.cache_service.set(f"user:{user.id}", current_user_data, expire=CURRENT_USER_TTL)

		return auth_tokens, AuthDto(
			first_name=user.first_name,
			last_name=user.last_name,
			username=user.account.handle,
			email=user.email,
			gender=user.gender,
			user_id=user.id,
			auth_type=AuthType.MAIN if user.verified else AuthType.VERIFICATION,
			name=f"{user.first_name} {user.last_name}",
		)

	def get_user_by_id(self, user_id: str) -> User | None:
		"""
		Get user by ID with account relationship loaded
		"""
		if not user_id:
			return None

		return self.db.query(User).options(joinedload(User.account)).filter(User.id == user_id).first()

	def _update_session_activity(self, user_id: str):
		"""Update last activity for user sessions"""
		try:
			self.db.query(Session).filter(Session.user_id == user_id, Session.is_active).update(
				{Session.last_activity: datetime.utcnow()}
			)
			self.db.commit()
		except Exception as e:
			logger.warning(f"Failed to update session activity: {str(e)}")

	async def _get_role_permissions(self, role_id: str) -> List[str]:
		try:
			rows = (
				self.db.query(LoadableItem)
				.join(RolePermission, RolePermission.permission_id == LoadableItem.id)
				.filter(RolePermission.role_id == role_id)
				.all()
			)

			return [row.code.lower() for row in rows]
		except Exception as e:
			self.logger.error(f"Failed to get role permissions: {str(e)}")
			raise

	async def cache_role_permissions_cache(self, role_id: str = None, role_code: str = None):
		if role_id and role_code:
			permissions = await self._get_role_permissions(role_id)
			await self.cache_service.set(f"role_permissions:{role_code}", permissions, expire=ROLE_PERMISSION_TTL)

	async def authenticate_user(self, payload: LoginRequest, request: Request):
		"""Authenticate user and create session"""
		try:
			query = self.db.query(User).join(Account, User.account)
			user: User | None = None

			if "@" in payload.identifier:
				user = query.filter(User.email == payload.identifier).first()
			else:
				user = query.filter(Account.handle == payload.identifier).first()

			if not user:
				raise ApiException("Could find user with such email or username")

			if not self.encryption_service.verify_password(payload.password, user.hashed_password):
				raise ApiException("Incorrect password")

			if user.account.status == AccountStatus.INACTIVE.value and user.verified:
				raise ApiException("Account is inactive, please contact the administrator")

			if payload.remember_me:
				self._cleanup_old_sessions(user.id, keep_active=True)

			token, auth = await self.start_session(user, request, payload.remember_me)

			return token, auth
		except Exception as e:
			logger.error(f"Authentication error: {str(e)}")
			raise ApiException(str(e))

	def _cleanup_old_sessions(self, user_id: str, keep_active: bool = False):
		"""Clean up old sessions for a user"""
		try:
			current_time = datetime.now()

			query = self.db.query(Session).filter(Session.user_id == user_id)

			if keep_active:
				query = query.filter((~Session.is_active) | (Session.expires_at <= current_time))

			old_sessions = query.all()

			for session in old_sessions:
				if session.is_active:
					session.is_active = False
					session.logged_out_at = current_time
				self.db.add(session)

			self.db.commit()

			if old_sessions:
				logger.info(f"Cleaned up {len(old_sessions)} old sessions for user {user_id}")

		except Exception as e:
			logger.error(f"Session cleanup error: {str(e)}")
			self.db.rollback()

	def logout_user(self, token: str, all_sessions: bool = False, session_id: Optional[str] = None) -> Tuple[bool, int]:
		"""
		Logout user by invalidating sessions
		Returns: (success, number_of_sessions_logged_out)
		"""
		try:
			# Get user from token
			is_valid, payload = self.encryption_service.verify_token(token)
			if not is_valid:
				return False, 0

			user_id = payload.get("user_id")
			if not user_id:
				return False, 0

			current_time = datetime.now()

			if all_sessions:
				sessions_to_logout = self.db.query(Session).filter(Session.user_id == user_id, Session.is_active).all()

				sessions_count = len(sessions_to_logout)

				for session in sessions_to_logout:
					session.is_active = False
					session.logged_out_at = current_time
					self.db.add(session)

			else:
				if session_id:
					session_to_logout = (
						self.db.query(Session)
						.filter(Session.user_id == user_id, Session.session_token == session_id, Session.is_active)
						.first()
					)
				else:
					# Get the most recently active session
					session_to_logout = (
						self.db.query(Session)
						.filter(Session.user_id == user_id, Session.is_active)
						.order_by(Session.last_activity.desc())
						.first()
					)

				if session_to_logout:
					session_to_logout.is_active = False
					session_to_logout.logged_out_at = current_time
					self.db.add(session_to_logout)
					sessions_count = 1
				else:
					sessions_count = 0

			self.db.commit()
			return True, sessions_count

		except Exception as e:
			logger.error(f"Logout error: {str(e)}")
			self.db.rollback()
			return False, 0

	def get_user_sessions(self, user_id: str, current_session_token: Optional[str] = None) -> list[SessionInfo]:
		"""Get all active sessions for a user"""
		sessions = (
			self.db.query(Session)
			.filter(Session.user_id == user_id, Session.is_active, Session.expires_at > datetime.utcnow())
			.order_by(Session.last_activity.desc())
			.all()
		)

		session_infos = []
		for session in sessions:
			session_infos.append(
				SessionInfo(
					session_id=session.session_token,
					ip_address=session.ip_address,
					user_agent=session.user_agent,
					device=session.device,
					created_at=session.created_at,
					last_activity=session.last_activity,
					is_current=(session.session_token == current_session_token),
					remember_me=session.remember_me,
					expires_at=session.expires_at,
				)
			)

		return session_infos

	def cleanup_expired_sessions(self):
		"""Clean up expired sessions - call this periodically"""
		try:
			current_time = datetime.now()

			expired_sessions = (
				self.db.query(Session).filter(Session.expires_at <= current_time, Session.is_active).all()
			)

			remember_me_count = 0
			regular_count = 0

			for session in expired_sessions:
				session.is_active = False
				session.logged_out_at = current_time
				self.db.add(session)

			if session.remember_me:
				remember_me_count += 1
			else:
				regular_count += 1

			self.db.commit()
			logger.info(
				f"Cleaned up {len(expired_sessions)} expired sessions"
				f"({remember_me_count} remember me, {regular_count} regular)"
			)

		except Exception as e:
			logger.error(f"Session cleanup error: {str(e)}")
			self.db.rollback()

	def refresh_access_token(self, refresh_token: str) -> Dict[str, str]:
		"""
		Use a refresh token to generate a new access token
		"""
		is_valid, payload = self.encryption_service.verify_token(refresh_token)

		if not is_valid or payload.get("type") != "refresh":
			raise ApiException("Invalid refresh token")

		user_id: str = payload.get("user_id")
		remember_me = payload.get("remember_me", False)

		refresh_token_hash = self._hash_token(refresh_token)
		session = (
			self.db.query(Session)
			.filter(
				Session.user_id == user_id,
				Session.refresh_token_hash == refresh_token_hash,
				Session.is_active,
				Session.expires_at > datetime.now(),
			)
			.first()
		)

		if not session:
			raise ApiException("Session expired or invalid")

		user = self.db.query(User).join(Account, User.account).where(User.id == user_id).first()

		if not user or user.account.status != AccountStatus.ACTIVE:
			raise ApiException("User was not found or inactive")

			# Update session activity
		session.last_activity = datetime.now()
		self.db.add(session)
		self.db.commit()

		token_data = {
			"sub": user.account.handle,
			"user_id": str(user.id),
			"remember_me": remember_me,
			"email": user.email,
		}

		if remember_me:
			# If remember me is true, set longer expiry for access token
			access_token_expires = timedelta(hours=24)
		else:
			# Default expiry for access token is 15 minutes
			access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)

		access_token = self.encryption_service.create_access_token(token_data, expires_delta=access_token_expires)

		return {"access_token": access_token, "token_type": "bearer", "remember_me": remember_me}

	async def initiate_password_reset(self, email: str) -> bool:
		"""
		Create a password reset token and return it
		In a real app, you would send this via email
		"""
		try:
			user = self.db.query(User).where(User.email == email).first()

			if not user:
				return True
				# raise ApiException(f"Password reset requested for non-existent email: {email}")

			verification = await self.generate_verification_code(VerificationType.PASSWORD_RESET, user.id)

			reset_token = self.encryption_service.generate_password_reset_token(email, verification.code)

			name = f"{user.first_name} {user.last_name}"
			email_message = EmailMessage(
				template=EmailTemplate.RESET_PASSWORD,
				recipients=[EmailRecipient(email=email, name=name)],
				subject=f"Password Reset Request for {settings.APP_NAME}",
				sender_email=settings.SENDER_EMAIL,
				sender_name=settings.SENDER_NAME,
				template_variables={
					"user_name": name,
					"reset_url": f"{settings.FRONTEND_URL}/auth/confirm-password?token={reset_token}",
				},
			)

			self.email_service.send_email(email_message)

			logger.info(f"Password reset email for {email}")
			return True

		except Exception as e:
			logger.error(f"Password reset error for {email}: {str(e)}")
			return True

	async def complete_password_reset(self, payload: PasswordConfirmationRequest) -> bool:
		"""
		Complete the password reset process by updating the user's password
		"""
		try:
			if not is_password_strong(payload.password):
				raise ApiException("Password should have one lowercase, one number, one special character")

			valid, token = self.encryption_service.verify_token(payload.token)

			if not valid:
				raise ApiException("Invalid token, or token expired")

			user: User | None = self.db.query(User).filter(User.email == token.get("sub")).first()

			if not user:
				return False

			verification = (
				self.db.query(AccountVerification)
				.filter(
					AccountVerification.type == VerificationType.PASSWORD_RESET,
					AccountVerification.user_id == user.id,
					AccountVerification.code == token.get("code"),
					~AccountVerification.verified,
				)
				.first()
			)

			if not verification:
				return False

			user.hashed_password = self.encryption_service.hash_password(payload.password)
			verification.verified = True

			self.db.add(verification)
			self.db.add(user)

			self.db.commit()
			self.db.refresh(user)

			self._send_password_reset_confirmation(user)

			return True
		except Exception as e:
			logger.error(f"Error validating password reset token: {str(e)}")
			raise

	def _send_password_reset_confirmation(self, user: User):
		"""Send confirmation email after password reset"""
		try:
			name = f"{user.first_name} {user.last_name}"
			email_message = EmailMessage(
				template=EmailTemplate.RESET_PASSWORD_SUCCESS,
				recipients=[EmailRecipient(email=user.email, name=name)],
				subject=f"Your {settings.APP_NAME} Password Has Been Reset",
				sender_email=settings.SENDER_EMAIL,
				sender_name=settings.SENDER_NAME,
				template_variables={
					"user_name": name,
					"app_name": settings.APP_NAME,
					"login_url": f"{settings.FRONTEND_URL}/auth/login",
					"reset_time": user.updated_at.strftime("%B %d, %Y at %I:%M %p UTC"),
				},
			)
			self.email_service.send_email(email_message)
			logger.info(f"Password reset confirmation sent to {user.email}")
		except Exception as e:
			logger.error(f"Failed to send password reset confirmation: {str(e)}")

	async def register_user(self, request: Request, payload: RegistrationRequest):
		"""
		Register a new user in the database with hashed password
		"""
		try:
			existing_user = self.db.query(User).filter(User.email == payload.email).first()

			if existing_user is not None:
				raise ApiException("User with the same email already exists")

			username = await self.generate_username()
			account = Account(
				type=AccountType.USER,
				status=AccountStatus.INACTIVE,
				handle=username,
			)

			self.db.add(account)
			self.db.flush()

			hashed_password = self.encryption_service.hash_password(payload.password)

			user = User(
				first_name=payload.first_name,
				gender=payload.gender,
				last_name=payload.last_name,
				email=str(payload.email).lower(),
				hashed_password=hashed_password,
				is_external=True,
				account_id=account.id,
			)
			user.account_id = account.id

			self.db.add(user)
			self.db.flush()
			self.db.commit()

			token, auth = await self.start_session(user, request)
			self.send_account_verification_email(user)

			return token, auth
		except Exception as e:
			logger.error(f"Registration error: {str(e)}")
			self.db.rollback()
			raise

	async def send_account_verification_email(self, user: User):
		verification = await self.generate_verification_code(VerificationType.ACCOUNT_REGISTRATION, user.id)

		email_token = self.encryption_service.generate_email_verification_token(user.email, verification.code)
		name = f"{user.first_name} {user.last_name}"
		self.email_service.send_email(
			EmailMessage(
				template=EmailTemplate.ACCOUNT_CONFIRMATION,
				recipients=[EmailRecipient(email=user.email, name=name)],
				subject=f"Welcome to {settings.APP_NAME}, please verify your email",
				sender_email=settings.SENDER_EMAIL,
				sender_name=settings.SENDER_NAME,
				template_variables={
					"user_name": name,
					"verification_url": f"{settings.FRONTEND_URL}/auth/2fa?token={email_token}",
					"code": verification.code,
				},
			)
		)

	async def retrieve_user_from_two_fa_request(self, payload: TwoFactorRequest, auth_type: str):
		user: User | None = None
		code: str = payload.code

		if payload.email:
			user = self.db.query(User).filter(User.email == payload.email).first()

		if payload.token:
			valid, token = self.encryption_service.verify_token(payload.token)

			if not valid:
				raise ApiException("Invalid token")
			code = token.get("code")
			user = self.db.query(User).filter(User.email == token.get("email")).first()

		if user is None:
			raise ApiException("User does not exist")

		type = {"VERIFICATION": VerificationType.ACCOUNT_REGISTRATION}

		verification = (
			self.db.query(AccountVerification)
			.filter(
				AccountVerification.user_id == user.id,
				AccountVerification.type == type[auth_type],
				AccountVerification.code == code,
				~AccountVerification.verified,
			)
			.first()
		)

		return user, verification

	async def verify_two_factor_code(self, request: Request, payload: TwoFactorRequest):
		try:
			auth_type = request.cookies.get(SESSION_TYPE_KEY_COOKIE)
			user, verification = await self.retrieve_user_from_two_fa_request(payload, auth_type)

			if verification is None:
				raise ApiException("Code verification failed")

			if auth_type == AuthType.VERIFICATION.value and verification.verified:
				raise ApiException("Account was already verified")

			account = self.db.query(Account).filter(Account.id == user.account_id).first()

			verification.verified = True
			user.verified = True
			account.status = AccountStatus.ACTIVE

			self.db.add(verification)
			self.db.add(user)
			self.db.add(account)
			self.db.commit()

			name = f"{user.first_name} {user.last_name}"
			send_welcome_email(
				user.email,
				settings.SENDER_NAME,
				settings.SENDER_EMAIL,
				{
					"user_name": name,
				},
			)

			tokens, auth = await self.start_session(user, request)

			return tokens, auth
		except Exception as e:
			logger.error(f"Failed to verify 2fa: {str(e)}")
			raise

	def _generate_email_verification_token(self, email: str) -> str:
		"""Generate email verification token"""
		return self.encryption_service.generate_email_verification_token(email)

	async def _send_welcome_email(self, user: User):
		"""Send welcome email after successful verification"""
		from src.core.services.email_service import send_welcome_email

		try:
			send_welcome_email(
				recipient_email=user.email,
				recipient_name=f"{user.first_name} {user.last_name}",
				sender_email=settings.FROM_EMAIL,
				user_name=user.first_name,
			)
		except Exception as e:
			logger.error(f"Failed to send welcome email to {user.email}: {str(e)}")

	async def resend_account_verification(self, request: Request, data: VerificationRetryRequest):
		"""Resend verification email to user"""
		try:
			user = self.db.query(User).filter(User.email == str(data.email)).first()

			if not user:
				raise ApiException(f"Verification resend requested for non-existent email: {data.email}")

			if user.verified:
				raise ApiException(f"Verification resend requested for already active account: {data.email}")

			auth_type = request.cookies.get(SESSION_TYPE_KEY_COOKIE)

			if auth_type != AuthType.VERIFICATION.value:
				raise ApiException("Could not resend verification email")

			await self.send_account_verification_email(user)

			return True
		except Exception as e:
			logger.error(f"Resend verification error: {str(e)}")
			raise e

	def get_remember_me_sessions(self, user_id: str) -> list[SessionInfo]:
		"""Get only remember me sessions for a user"""
		sessions = (
			self.db.query(Session)
			.filter(
				Session.user_id == user_id,
				Session.remember_me,
				Session.is_active,
				Session.expires_at > datetime.utcnow(),
			)
			.order_by(Session.last_activity.desc())
			.all()
		)

		return [
			SessionInfo(
				session_id=session.session_token,
				ip_address=session.ip_address,
				user_agent=session.user_agent,
				device=session.device,
				created_at=session.created_at,
				last_activity=session.last_activity,
				is_current=False,
				remember_me=session.remember_me,
				expires_at=session.expires_at,
			)
			for session in sessions
		]

	def revoke_remember_me_sessions(self, user_id: str) -> int:
		"""Revoke all remember me sessions for a user"""
		try:
			current_time = datetime.utcnow()

			remember_me_sessions = (
				self.db.query(Session).filter(Session.user_id == user_id, Session.remember_me, Session.is_active).all()
			)

			for session in remember_me_sessions:
				session.is_active = False
				session.logged_out_at = current_time
				self.db.add(session)

			self.db.commit()

			count = len(remember_me_sessions)
			logger.info(f"Revoked {count} remember me sessions for user {user_id}")
			return count

		except Exception as e:
			logger.error(f"Error revoking remember me sessions: {str(e)}")
			self.db.rollback()
			return 0

	def is_remember_me_session(self, session_token: str) -> bool:
		"""Check if a session is a remember me session"""
		try:
			session = self.db.query(Session).filter(Session.session_token == session_token, Session.is_active).first()

			return session.remember_me if session else False

		except Exception:
			return False

	async def generate_verification_code(
		self, type_: VerificationType, user_id: str, expiry_minutes: int = 30
	) -> AccountVerification:
		"""Generate and save a verification code for the given email and type."""

		existing_verification = (
			self.db.query(AccountVerification)
			.filter(
				AccountVerification.user_id == user_id, AccountVerification.type == type_, ~AccountVerification.verified
			)
			.first()
		)

		code = "".join(random.choices(string.ascii_uppercase + string.digits, k=6))
		new_expires_at = datetime.now() + timedelta(minutes=expiry_minutes)

		if existing_verification:
			existing_verification.code = code
			existing_verification.expires_at = new_expires_at
			verification = existing_verification
		else:
			verification = AccountVerification(
				code=code,
				type=type_,
				expires_at=new_expires_at,
				verified=False,
				user_id=user_id,
			)
			self.db.add(verification)

		self.db.commit()
		self.db.refresh(verification)
		return verification
