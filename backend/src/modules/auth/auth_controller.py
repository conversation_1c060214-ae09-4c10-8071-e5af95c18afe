from fastapi import Header, HTTPException, Request, status, Response

from src.core.dtos.auth_dtos import AuthDto
from src.core.exceptions.api import ApiException
from src.core.guards.auth_guard import auth_guard
from src.core.http.responses import bad_request, unauthorized
from src.core.shared_schema import BaseResponse
from src.core.utils.cookies import set_auth_cookies
from src.modules.auth.auth_schema import (
	LoginRequest,
	LogoutRequest,
	LogoutResponse,
	PasswordResetRequest,
	RefreshTokenRequest,
	RefreshTokenResponse,
	RegistrationRequest,
	UserSessionsResponse,
	VerificationRetryRequest,
	TwoFactorRequest,
	PasswordConfirmationRequest,
)
from src.modules.auth.auth_service import AuthService

auth_service = AuthService()


async def register(request: Request, data: RegistrationRequest, response: Response) -> BaseResponse[AuthDto]:
	"""
	Register a new user.

	Args:
		user_data: User registration data

	Returns:
		UserResponse: The newly created user
	"""
	tokens, auth = await auth_service.register_user(request, data)

	set_auth_cookies(response, tokens["access_token"], tokens["refresh_token"], auth.auth_type)

	return BaseResponse[AuthDto](data=auth)


@auth_guard.authenticated
async def resend_verification(request: Request, data: VerificationRetryRequest):
	"""
	Resend email verification link to user.

	Args:
	   resend_request: Request with user's email

	Returns:
	   JSONResponse: Confirmation message
	"""
	await auth_service.resend_account_verification(request, data)
	return BaseResponse[bool](data=True)


@auth_guard.authenticated
async def verify_2fa(request: Request, data: TwoFactorRequest, response: Response):
	tokens, auth = await auth_service.verify_two_factor_code(request, data)

	set_auth_cookies(response, tokens["access_token"], tokens["refresh_token"], auth.auth_type)

	return BaseResponse[AuthDto](data=auth)


async def login(request: Request, form_data: LoginRequest, response: Response) -> BaseResponse[AuthDto]:
	"""
	Authenticate a user and return access tokens.

	Args:
		request: Request with login details
		form_data: Form with username and password
		response: Response with access token

	Returns:
		BaseResponse[AuthDto]: Auth data with auth tokens
	"""
	tokens, auth = await auth_service.authenticate_user(form_data, request)

	if not auth:
		raise ApiException("Invalid username or password")

	set_auth_cookies(response, tokens["access_token"], tokens["refresh_token"], auth.auth_type)

	return BaseResponse[AuthDto](data=auth)


def logout(logout_request: LogoutRequest, authorization: str = Header(None, alias="Authorization")) -> LogoutResponse:
	"""
	Logout user by invalidating sessions.

	Args:
		logout_request: Logout configuration
		authorization: Authorization header with Bearer token

	Returns:
		LogoutResponse: Logout confirmation
	"""
	try:
		# Extract token from Authorization header
		if not authorization.startswith("Bearer "):
			raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid authorization header format")

		token = authorization.split(" ")[1]

		if logout_request.revoke_remember_me:
			current_user = auth_service.get_current_user(token)
			if current_user:
				remember_me_revoked = auth_service.revoke_remember_me_sessions(current_user.id)
			else:
				remember_me_revoked = 0
		else:
			remember_me_revoked = 0

		# Logout user
		success, sessions_count = auth_service.logout_user(
			token, logout_request.all_sessions, logout_request.session_id
		)

		if success:
			message = f"Successfully logged out from {sessions_count} session(s)"
			if logout_request.all_sessions:
				message = f"Successfully logged out from all {sessions_count} sessions"
			if logout_request.revoke_remember_me:
				message += f" and revoked remember me sessions: {remember_me_revoked}"

			return LogoutResponse(
				success=True,
				message=message,
				sessions_logged_out=sessions_count,
				remember_me_sessions_revoked=remember_me_revoked,
			)
		else:
			return LogoutResponse(
				success=False,
				message="Logout failed - no active session found",
				sessions_logged_out=0,
				remember_me_sessions_revoked=remember_me_revoked,
			)

	except ApiException as e:
		return bad_request(e.message)


def get_user_sessions(authorization: str) -> UserSessionsResponse:
	"""
	Get all active sessions for the current user.

	Args:
		authorization: Authorization header with Bearer token

	Returns:
		UserSessionsResponse: List of active sessions
	"""
	try:
		# Extract token from Authorization header
		if not authorization.startswith("Bearer "):
			raise unauthorized("Invalid authorization header format")

		token = authorization.split(" ")[1]

		# Get current user
		current_user = auth_service.get_current_user(token)
		if not current_user:
			raise unauthorized("Could not validate credentials")

		# Get user sessions
		sessions = auth_service.get_user_sessions(current_user.id)

		remember_me_count = sum(1 for s in sessions if s.remember_me)
		regular_count = len(sessions) - remember_me_count

		return UserSessionsResponse(
			sessions=sessions,
			total_sessions=len(sessions),
			remember_me_sessions=remember_me_count,
			regular_sessions=regular_count,
		)

	except ApiException as e:
		raise unauthorized(e.message)


def refresh_token(refresh_request: RefreshTokenRequest) -> RefreshTokenResponse:
	"""
	Enhanced refresh token with remember me support
	"""
	try:
		new_tokens = auth_service.refresh_access_token(refresh_request.refresh_token)

		return RefreshTokenResponse(
			access_token=new_tokens["access_token"],
			token_type=new_tokens["token_type"],
			expires_in=3600,  # 1 hour default
			remember_me=new_tokens.get("remember_me", False),
		)

	except ApiException as e:
		raise bad_request(e.message)


@auth_guard.authenticated
def get_current_user(_: Request) -> BaseResponse[bool]:
	"""
	Get the current authenticated user.

	Args:
		token: JWT from Authorization header

	Returns:
		UserResponse: Current user data
	"""
	return BaseResponse[bool](data=True)


async def request_password_reset(
	data: PasswordResetRequest,
) -> BaseResponse[bool]:
	"""
	Request a password reset email.

	Args:
		data: Request with user's email

	Returns:
		bool: Confirmation message
	"""
	is_sent = await auth_service.initiate_password_reset(str(data.email))
	return BaseResponse[bool](data=is_sent)


async def reset_password(data: PasswordConfirmationRequest) -> BaseResponse[bool]:
	"""
	Validate a password reset token.

	Args:
		token_validation: Token validation request

	Returns:
		PasswordResetTokenResponse: Token validation result
	"""
	verified = await auth_service.complete_password_reset(data)

	if not verified:
		raise ApiException("Invalid token")

	return BaseResponse(data=verified)
