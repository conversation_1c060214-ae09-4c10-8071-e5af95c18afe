from pydantic import UUID4
from fastapi import Depends
from src.core.exceptions.api import ApiException
from src.core.http.responses import bad_request
from src.core.shared_schema import BaseResponse, CurrentUser, VoidRequest
from src.core.guards.auth_guard import auth_guard

from .complaint_schema import ComplaintD<PERSON>, ComplaintFilter, ComplaintRequest

from .complaint_service import ComplaintService


complaint_service = ComplaintService()


def fetch_complaints_handler(filter: ComplaintFilter = Depends(ComplaintFilter)):
	try:
		return complaint_service.retrieve_complaints(filter)

	except ApiException as e:
		raise bad_request(e.message)


def create_complaints_handler(data: ComplaintRequest):
	try:
		complaint = complaint_service.create_complaint(data)
		return BaseResponse[ComplaintDTO](data=complaint)

	except ApiException as e:
		raise bad_request(e.message)


def update_complaints_handler(id: UUID4, data: ComplaintRequest):
	try:
		complaint = complaint_service.update_complaint(id, data)
		return BaseResponse[ComplaintDTO](data=complaint)

	except ApiException as e:
		raise bad_request(e.message)


def void_complaints_handler(id: UUID4, data: VoidRequest, user: CurrentUser = Depends(auth_guard.current_user)):
	try:
		complaint = complaint_service.void_complaint(id, payload=data, user=user)
		return BaseResponse[ComplaintDTO](data=complaint)

	except ApiException as e:
		raise bad_request(e.message)
