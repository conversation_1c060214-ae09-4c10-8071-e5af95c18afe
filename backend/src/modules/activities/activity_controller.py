from uuid import UUID

from fastapi import Depends, HTTPException, status
from fastapi_pagination.ext.sqlalchemy import paginate

from src.core.guards.auth_guard import auth_guard
from src.core.shared_schema import CurrentUser

from .activity_schema import ActivityDto, ActivityFilter, ActivityRequest
from .activity_service import ActivityService

activity_service = ActivityService()


def fetch_activities_handler(
	filters: ActivityFilter = Depends(ActivityFilter),
	user: CurrentUser = Depends(auth_guard.current_user),
) -> list[ActivityDto]:
	try:
		return paginate(activity_service.find_activities(filters))
	except Exception as e:
		raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


def get_activity_handler(
	id: UUID,
	user: CurrentUser = Depends(auth_guard.current_user),
) -> ActivityDto:
	try:
		return activity_service.get_activity_by_id(id)
	except Exception as e:
		raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


def create_activity_handler(
	data: ActivityRequest,
	user: CurrentUser = Depends(auth_guard.current_user),
) -> ActivityDto:
	try:
		return activity_service.create_activity(user, data)
	except Exception as e:
		raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


def update_activity_handler(
	id: UUID,
	data: ActivityRequest,
	user: CurrentUser = Depends(auth_guard.current_user),
) -> ActivityDto:
	try:
		return activity_service.update_activity(user, id, data)
	except Exception as e:
		raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


def delete_activity_handler(
	id: UUID,
	user: CurrentUser = Depends(auth_guard.current_user),
) -> None:
	try:
		activity_service.delete_activity(user, id)
		return
	except Exception as e:
		raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
		return
	except Exception as e:
		raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
