from fastapi import APIRouter, status
from fastapi_pagination import Page

import src.modules.activities.activity_controller as controller
import src.modules.activities.activity_schema as schema

router = APIRouter(tags=["activity"])

router.add_api_route(
	path="",
	endpoint=controller.fetch_activities_handler,
	response_model=Page[schema.ActivityDto],
	methods=["GET"],
	status_code=status.HTTP_200_OK,
	description="Retrieve list of activities",
)

router.add_api_route(
	path="/{id}",
	endpoint=controller.get_activity_handler,
	response_model=schema.ActivityDto,
	methods=["GET"],
	status_code=status.HTTP_200_OK,
	description="Retrieve a single activity by ID",
)

router.add_api_route(
	path="",
	endpoint=controller.create_activity_handler,
	response_model=schema.ActivityDto,
	methods=["POST"],
	status_code=status.HTTP_201_CREATED,
	description="Create a new activity",
)

router.add_api_route(
	path="/{id}",
	endpoint=controller.update_activity_handler,
	response_model=schema.ActivityDto,
	methods=["PUT"],
	status_code=status.HTTP_200_OK,
	description="Update an existing activity",
)

router.add_api_route(
	path="/{id}",
	endpoint=controller.delete_activity_handler,
	methods=["DELETE"],
	status_code=status.HTTP_204_NO_CONTENT,
	description="Delete an activity",
)
