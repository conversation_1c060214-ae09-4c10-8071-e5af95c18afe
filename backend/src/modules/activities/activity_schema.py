from datetime import datetime
from enum import Enum
from typing import Optional

from pydantic import UUID4, BaseModel, Field

from src.core.shared_schema import BaseRequest


class ActivityVisibility(str, Enum):
	ALL = "ALL"
	LIMITED = "LIMITED"


class ActivityDto(BaseModel):
	id: UUID4
	title: str
	category_id: UUID4
	visibility: ActivityVisibility
	venue: str
	district_id: UUID4
	longitude: Optional[str]
	latitude: Optional[str]
	map_pin: Optional[str]
	start_date: datetime
	end_date: datetime
	summary: str
	created_at: datetime
	updated_at: datetime


class ActivityRequest(BaseModel):
	id: Optional[UUID4] = None
	title: str
	category_id: UUID4
	visibility: ActivityVisibility
	venue: str
	district_id: UUID4
	longitude: Optional[str] = None
	latitude: Optional[str] = None
	map_pin: Optional[str] = None
	start_date: datetime
	end_date: datetime
	summary: str


class ActivityFilter(BaseRequest):
	title: Optional[str] = Field(default=None, description="Search by activity title")
	venue: Optional[str] = Field(default=None, description="Search by venue")
