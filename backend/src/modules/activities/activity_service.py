from uuid import UUID

from sqlalchemy import and_

from src.config.db.models import Activity
from src.core.base.base_repository import BaseRepository
from src.core.exceptions.api import ApiException
from src.core.shared_schema import CurrentUser
from src.core.utils import serializer

from .activity_schema import ActivityDto, ActivityFilter, ActivityRequest


class ActivityService(BaseRepository):
	def __init__(self):
		super().__init__()

	def find_activities(self, filters: ActivityFilter) -> list:
		"""
		Find activities based on filters.

		Args:
		    filters (ActivityFilter): Filters to apply.

		Returns:
		    list: List of activities matching the criteria.
		"""
		partial_filters = ["title", "venue"]

		try:
			filter_dict = filters.dict(exclude_unset=True)
			pfilters = {k: v for k, v in filter_dict.items() if k in partial_filters and v is not None}

			query = self.db.query(Activity).filter(not Activity.voided).order_by(Activity.start_date.desc())

			for field in partial_filters:
				if field in pfilters:
					value = pfilters.pop(field)
					query = query.filter(getattr(Activity, field).ilike(f"%{value}%"))

			return query

		except Exception as e:
			self.logger.error(f"Error finding activities: {e}")
			raise e

	def get_activity_by_id(self, activity_id: UUID) -> ActivityDto:
		try:
			activity = self.db.query(Activity).filter(and_(Activity.id == activity_id, not Activity.voided)).first()

			if not activity:
				raise ApiException("Activity not found")

			return serializer.to_activity_dto(activity)
		except Exception as e:
			self.logger.error(f"Error finding activities: {e}")
			raise e

	def create_activity(self, user: CurrentUser, data: ActivityRequest) -> ActivityDto:
		try:
			activity_data = data.model_dump()
			activity = Activity(**activity_data, created_by=user.id)
			self.db.add(activity)
			self.db.commit()
			self.db.refresh(activity)
			return serializer.to_activity_dto(activity)
		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Error creating activity: {e}")
			raise e

	def update_activity(self, user: CurrentUser, id: UUID, data: ActivityRequest) -> ActivityDto:
		try:
			activity = self.db.query(Activity).filter(and_(Activity.id == id, not Activity.voided)).first()

			if not activity:
				raise ApiException("Activity not found")

			for key, value in data.model_dump().items():
				setattr(activity, key, value)

			activity.updated_by = user.id
			self.db.commit()
			self.db.refresh(activity)
			return serializer.to_activity_dto(activity)

		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Error updating activity: {e}")
			raise e

	def delete_activity(self, user: CurrentUser, activity_id: UUID) -> bool:
		try:
			activity = self.db.query(Activity).filter(and_(Activity.id == activity_id, not Activity.voided)).first()
			if not activity:
				raise ApiException("Activity not found")

			activity.voided_by = user.id
			activity.voided = True
			self.db.commit()
			return True
		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Error deleting activity: {e}")
			raise e
