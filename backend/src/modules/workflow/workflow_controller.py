from typing import List

from fastapi import Depends
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from pydantic import UUID4
from starlette.requests import Request

from src.core.dtos.workflow_dtos import TemplateDto, TemplateStageDto, TemplateStageRoleDto, TemplateStageTriggerDto
from src.core.guards.auth_guard import auth_guard
from src.core.shared_schema import BaseResponse, Pagination, VoidRequest
from src.modules.workflow.workflow_schema import (
	TemplateFilter,
	TemplateRequest,
	TemplateStageFilter,
	TemplateStageRequest,
	TemplateStageRoleFilter,
	TemplateStageRoleRequest,
	TemplateStageTriggerFilter,
	TemplateStageTriggerRequest,
)
from src.modules.workflow.workflow_service import workflow_service


@auth_guard.authenticated
async def fetch_templates_handler(
	_: Request,
	filter: TemplateFilter = Depends(TemplateFilter),
) -> Pagination[TemplateDto]:
	return await workflow_service.retrieve_templates(filter)


@auth_guard.authenticated
async def get_template_handler(
	template_id: UUID4,
	   _: Request,
) -> Pagination[TemplateDto]:
	return await workflow_service.get_template_by_id(template_id)


@auth_guard.authenticated
async def create_template_handler(
	_: Request,
	payload: TemplateRequest,
) -> TemplateDto:
	new_template = await workflow_service.create_template(payload)
	return new_template


@auth_guard.authenticated
async def update_template_handler(
	_: Request,
	template_id: UUID4,
	payload: TemplateRequest,
) -> TemplateDto:
	updated_template = await workflow_service.update_template(template_id, payload)
	return updated_template


@auth_guard.can_("delete.template")
async def void_template_handler(_: Request, template_id: UUID4, payload: VoidRequest):
	success = await workflow_service.void_template(template_id, payload)
	return {"success": success}


@auth_guard.can_("create.template")
async def create_template_stage_handler(
	_: Request,
	template_id: UUID4,
	payload: TemplateStageRequest,
) -> BaseResponse[list[TemplateStageDto]]:
	return BaseResponse(data = await workflow_service.add_template_stage(template_id, payload))


@auth_guard.authenticated
async def fetch_template_stages_handler(
	_: Request,
	template_id: UUID4,
	filter: TemplateStageFilter = Depends(TemplateStageFilter),
) -> Pagination[TemplateStageDto]:
	return await workflow_service.retrieve_template_stages(template_id, filter)


@auth_guard.authenticated
async def update_template_stage_handler(
	_: Request,
	template_id: UUID4,
	stage_id: UUID4,
	payload: TemplateStageRequest,
) -> TemplateStageDto:
	updated_stage = await workflow_service.update_template_stage(stage_id, payload)
	return updated_stage


@auth_guard.can_("delete.template_stage")
async def void_template_stage_handler(_: Request, template_id: UUID4, stage_id: UUID4, payload: VoidRequest):
	success = await workflow_service.void_template_stage(stage_id, payload)
	return {"success": success}


@auth_guard.authenticated
async def fetch_template_stage_roles_handler(
	_: Request,
	template_id: UUID4,
	stage_id: UUID4,
	filter: TemplateStageRoleFilter = Depends(TemplateStageRoleFilter),
) -> Pagination[TemplateStageRoleDto]:
	roles = await workflow_service.retrieve_template_stage_roles(stage_id, filter)
	return roles


@auth_guard.can_("create.template_stage_role")
async def create_template_stage_roles_handler(
	_: Request,
	template_id: UUID4,
	stage_id: UUID4,
	payload: List[TemplateStageRoleRequest],
) -> BaseResponse[List[TemplateStageRoleDto]]:
	roles = await workflow_service.add_template_stage_roles(stage_id, payload)
	return BaseResponse(data=roles)


@auth_guard.can_("delete.template_stage_role")
async def void_template_stage_role_handler(
	_: Request,
	template_id: UUID4,
	stage_id: UUID4,
	role_id: UUID4,
	payload: VoidRequest,
):
	success = await workflow_service.void_template_stage_role(role_id, payload)
	return {"success": success}


@auth_guard.authenticated
async def fetch_template_stage_triggers_handler(
	_: Request,
	template_id: UUID4,
	stage_id: UUID4,
	filter: TemplateStageTriggerFilter = Depends(TemplateStageTriggerFilter),
) -> Pagination[TemplateStageTriggerDto]:
	triggers = await workflow_service.retrieve_template_stage_triggers(stage_id, filter)
	return triggers


@auth_guard.can_("create.template_stage_trigger")
async def create_template_stage_triggers_handler(
	_: Request,
	template_id: UUID4,
	stage_id: UUID4,
	payload: List[TemplateStageTriggerRequest],
) -> BaseResponse[List[TemplateStageTriggerDto]]:
	triggers = await workflow_service.add_template_stage_triggers(stage_id, payload)
	return BaseResponse(data=triggers)


@auth_guard.can_("delete.template_stage_trigger")
async def void_template_stage_trigger_handler(
	_: Request,
	template_id: UUID4,
	stage_id: UUID4,
	trigger_id: UUID4,
	payload: VoidRequest,
):
	success = await workflow_service.void_template_stage_trigger(trigger_id, payload)
	return {"success": success}
