from typing import List

from fastapi import APIRouter, status

from src.core.dtos.workflow_dtos import TemplateDto, TemplateStageDto, TemplateStageRoleDto, TemplateStageTriggerDto
from src.core.shared_schema import BaseResponse, Pagination
from src.modules.workflow import workflow_controller as controller

workflow_router_v1 = APIRouter(tags=["workflows"])

workflow_router_v1.add_api_route(
	path="/templates",
	methods=["GET"],
	endpoint=controller.fetch_templates_handler,
	response_model=Pagination[TemplateDto],
	summary="Retrieve a paginated list of workflow templates",
)

workflow_router_v1.add_api_route(
	path="/templates",
	methods=["POST"],
	endpoint=controller.create_template_handler,
	response_model=TemplateDto,
	status_code=status.HTTP_201_CREATED,
	summary="Create a new workflow template",
)

workflow_router_v1.add_api_route(
	path="/templates/{template_id}",
	methods=["PUT"],
	endpoint=controller.update_template_handler,
	response_model=TemplateDto,
	summary="Update an existing workflow template",
)

workflow_router_v1.add_api_route(
	path="/templates/{template_id}/void",
	methods=["DELETE"],
	endpoint=controller.void_template_handler,
	status_code=status.HTTP_204_NO_CONTENT,
	summary="Void an existing workflow template",
)

# stages
workflow_router_v1.add_api_route(
	path="/templates/{template_id}/stages",
	methods=["POST"],
	endpoint=controller.create_template_stage_handler,
	response_model=BaseResponse[list[TemplateStageDto]],
	status_code=status.HTTP_201_CREATED,
	summary="Create a new stage for a workflow template",
)

workflow_router_v1.add_api_route(
	path="/templates/{template_id}/stages",
	methods=["GET"],
	endpoint=controller.fetch_template_stages_handler,
	response_model=Pagination[TemplateStageDto],
	summary="Retrieve paginated list of stages for a workflow template",
)

workflow_router_v1.add_api_route(
	path="/templates/{template_id}/stages/{stage_id}",
	methods=["PUT"],
	endpoint=controller.update_template_stage_handler,
	response_model=TemplateStageDto,
	summary="Update an existing workflow template stage",
)

workflow_router_v1.add_api_route(
	path="/templates/{template_id}/stages/{stage_id}",
	methods=["DELETE"],
	endpoint=controller.void_template_stage_handler,
	status_code=status.HTTP_204_NO_CONTENT,
	summary="Void an existing workflow template stage",
)

# Template stage roles CRUD
workflow_router_v1.add_api_route(
	path="/templates/{template_id}/stages/{stage_id}/roles",
	methods=["GET"],
	endpoint=controller.fetch_template_stage_roles_handler,
	response_model=Pagination[TemplateStageRoleDto],
	summary="Retrieve paginated list of roles for a workflow template stage",
)

workflow_router_v1.add_api_route(
	path="/templates/{template_id}/stages/{stage_id}/roles",
	methods=["POST"],
	endpoint=controller.create_template_stage_roles_handler,
	response_model=BaseResponse[List[TemplateStageRoleDto]],
	status_code=status.HTTP_201_CREATED,
	summary="Create roles for a workflow template stage",
)

workflow_router_v1.add_api_route(
	path="/templates/{template_id}/stages/{stage_id}/roles/{role_id}",
	methods=["DELETE"],
	endpoint=controller.void_template_stage_role_handler,
	status_code=status.HTTP_204_NO_CONTENT,
	summary="Void a workflow template stage role",
)

# Template stage triggers CRUD
workflow_router_v1.add_api_route(
	path="/templates/{template_id}/stages/{stage_id}/triggers",
	methods=["GET"],
	endpoint=controller.fetch_template_stage_triggers_handler,
	response_model=Pagination[TemplateStageTriggerDto],
	summary="Retrieve paginated list of triggers for a workflow template stage",
)

workflow_router_v1.add_api_route(
	path="/templates/{template_id}/stages/{stage_id}/triggers",
	methods=["POST"],
	endpoint=controller.create_template_stage_triggers_handler,
	response_model=BaseResponse[List[TemplateStageTriggerDto]],
	status_code=status.HTTP_201_CREATED,
	summary="Create triggers for a workflow template stage",
)

workflow_router_v1.add_api_route(
	path="/templates/{template_id}/stages/{stage_id}/triggers/{trigger_id}",
	methods=["DELETE"],
	endpoint=controller.void_template_stage_trigger_handler,
	status_code=status.HTTP_204_NO_CONTENT,
	summary="Void a workflow template stage trigger",
)
