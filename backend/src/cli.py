import os
import subprocess
import sys

import click
import psycopg2
from dotenv import load_dotenv


def connection() -> psycopg2.extensions.connection:
	load_dotenv()
	conn = psycopg2.connect(
		host=os.getenv("POSTGRES_HOST"),
		port=os.getenv("POSTGRES_PORT"),
		user=os.getenv("POSTGRES_USER"),
		password=os.getenv("POSTGRES_PASSWORD"),
		dbname="postgres"	
	)
	conn.autocommit = True
	return conn


@click.group()
def cli():
	"""FastAPI custom commands"""
	pass


@cli.group()
def db():
	"""Database commands"""
	pass


@db.command(name="create")
def db_create():
	"""
	Create database if it does not exist.
	"""
	cur = connection().cursor()
	try:
		dbname = os.getenv("POSTGRES_DB")
		cur.execute(f"CREATE DATABASE {dbname};")
		print(f"✅ Database '{dbname}' created.")
	except psycopg2.errors.DuplicateDatabase:
		print(f"ℹ️ Database '{dbname}' already exists.")
	finally:
		cur.close()
		connection().close()


@db.command(name="drop")
def db_drop():
	"""drop database"""
	confirm = click.confirm("Are you sure you want to drop the database? THIS CANNOT BE UNDONE.", abort=True)
	
	if not confirm:
		return

	cur = connection().cursor()
	try:
		dbname = os.getenv("POSTGRES_DB")
		cur.execute(f"DROP DATABASE {dbname};")
		print(f"✅ Database '{dbname}' dropped.")
	except psycopg2.errors.InvalidCatalogName:
		print(f"ℹ️ Database '{dbname}' does not exist.")
	finally:
		cur.close()


@db.command(name="init")
def db_init():
	"""Runs db create and db migrate and seeds"""
	subprocess.run(["python", "-m", "src.cli", "db", "create"], check=True)
	subprocess.run(["python", "-m", "src.cli", "db", "migrate"], check=True)
	subprocess.run(["python", "-m", "src.cli", "db", "seed"], check=True)


@db.command(name="migrate")
def db_migrate():
	"""Run database migrations using Alembic"""
	try:
		click.echo("Running database migrations...")
		result = subprocess.run(["alembic", "upgrade", "head"], check=True, capture_output=True, text=True)
		click.echo("✅ Database migrations completed successfully!")
		if result.stdout:
			click.echo(result.stdout)
	except subprocess.CalledProcessError as e:
		click.echo(f"❌ Migration failed: {e}", err=True)
		if e.stderr:
			click.echo(e.stderr, err=True)
		sys.exit(1)
	except FileNotFoundError:
		click.echo("❌ Alembic not found. Make sure it's installed and in your PATH.", err=True)
		sys.exit(1)


@db.command(name="seed")
def db_seed():
	"""Seed the database with initial data"""
	try:
		click.echo("Seeding database...")

		from src.config.db.seed import run_seed

		run_seed()

		click.echo("✅ Database seeded successfully!")
	except ImportError as e:
		click.echo(f"❌ Could not import seed module: {e}", err=True)
		click.echo("Make sure src.config.db.seed exists and has a run_seed function")
		sys.exit(1)
	except Exception as e:
		click.echo(f"❌ Seeding failed: {e}", err=True)
		sys.exit(1)


if __name__ == "__main__":
	cli()
