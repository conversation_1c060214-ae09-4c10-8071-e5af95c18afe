from fastapi import <PERSON>AP<PERSON>
from fastapi.responses import RedirectResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi_pagination import add_pagination

from src.config.metadata import API_METADATA
from src.config.routes import configure_routes
from src.config.settings import IS_PRODUCTION, get_trusted_hosts
from src.core.exceptions.handlers import register_exception_handlers
from src.core.shared_schema import BaseResponse
from src.modules.system.system_service import SystemService


def create_app() -> FastAPI:
	app = FastAPI(**API_METADATA)

	app.add_middleware(
		CORSMiddleware,
		allow_origins=get_trusted_hosts(),
		allow_credentials=True,
		allow_methods=["*"],
		allow_headers=["*"],
	)

	add_pagination(app)

	register_exception_handlers(app)

	@app.get("/", include_in_schema=False, summary="Redirect to API documentation")
	async def root():
		if IS_PRODUCTION:
			return BaseResponse(data={"message": "myNGO API is operational."}, success=True, errors=[])
		return RedirectResponse(url="/v1/docs")

	@app.get("/health", tags=["System Health"], summary="Comprehensive API Health Check")
	async def health_check():
		try:
			system_service = SystemService()
			health_data = system_service.get_health_status()

			return BaseResponse(data=health_data, success=True, errors=[])
		except Exception as e:
			error_message = f"Health check failed due to an internal error: {str(e)}"
			print(f"Error during health check: {e}")
			return BaseResponse(
				data=None, success=False, errors=[{"message": error_message, "code": "system_health_error"}]
			)

	configure_routes(app)

	return app
