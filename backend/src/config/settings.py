from os import getenv

ENVIRONMENT = getenv("ENV", "development").lower()
SECRET_KEY = getenv("SECRET_KEY", "$3kur17y")
ACCESS_TOKEN_EXPIRE_MINUTES = float(getenv("REFRESH_TOKEN_EXPIRES_IN", "120000"))
REFRESH_TOKEN_EXPIRE_DAYS = float(getenv("ACCESS_TOKEN_EXPIRES_IN", "30"))
APP_NAME = getenv("APP_NAME", "myNGO")
SYSTEM_CODE = getenv("SYSTEM_CODE")
APP_VERSION = getenv("APP_VERSION", "1.0.0")
MAILJET_PUBLIC_KEY = getenv("MAILJET_PUBLIC_KEY")
MAILJET_PRIVATE_KEY = getenv("MAILJET_PRIVATE_KEY")

FRONTEND_URL = getenv("FRONTEND_URL")
BACKEND_URL = getenv("BACKEND_URL")
SENDER_EMAIL = getenv("SENDER_EMAIL")
SENDER_NAME = getenv("SENDER_NAME")

MINIO_ROOT_USER = getenv("MINIO_ROOT_USER", "ngora")
MINIO_ROOT_PASSWORD = getenv("MINIO_ROOT_PASSWORD", "ngora12345")
MINIO_ENDPOINT = getenv("MINIO_ENDPOINT", "localhost:9000")
MINIO_ACCESS_KEY = getenv("MINIO_ACCESS_KEY", "some-access-key")
MINIO_SECRET_KEY = getenv("MINIO_SECRET_KEY", "some-secret-key")
MINIO_BUCKET = getenv("MINIO_BUCKET", "myngo")
MINIO_SECURE = getenv("MINIO_SECURE", "false").lower() in ("true", "1", "yes")

IS_PRODUCTION = ENVIRONMENT in ["production", "prod", "staging"]

SESSION_ACCESS_COOKIE = getenv("SESSION_ACCESS_COOKIE")
SESSION_TYPE_KEY_COOKIE = getenv("SESSION_TYPE_KEY_COOKIE")
SESSION_REFRESH_COOKIE = getenv("SESSION_REFRESH_COOKIE")

COOKIE_SETTINGS = {
	"httponly": True,
	"secure": IS_PRODUCTION,
	"samesite": "lax",
	"path": "/",
}


"""
===============================================================================
							CACHE SETTINGS
===============================================================================
"""
REDIS_HOST = getenv("REDIS_HOST", "localhost")
REDIS_PORT = int(getenv("REDIS_PORT", "6379"))
REDIS_DB = int(getenv("REDIS_DB", "0"))

CURRENT_USER_TTL = 1800
ROLE_PERMISSION_TTL = 86400


def get_trusted_hosts() -> list[str]:
	hosts: str = getenv("ALLOWED_HOSTS", "*")
	return hosts.split(",")
