from fastapi import FastAPI

from src.modules.activities.activity_router import router as activities_router_v1
from src.modules.application.application_router import application_router_v1
from src.modules.auth.auth_router import router as auth_router_v1
from src.modules.complaint.complaint_router import complaint_router_v1
from src.modules.dashboard.dashboard_router import dashboard_router_v1
from src.modules.departments.departments_router import router as departments_router_v1
from src.modules.document.document_router import document_router_v1
from src.modules.financial.financial_router import financial_router_v1
from src.modules.notifications.notification_router import notification_router_v1
from src.modules.organization.organization_router import organization_router_v1
from src.modules.roles.roles_router import router as roles_router_v1
from src.modules.settings.settings_router import router as settings_router_v1
from src.modules.users.users_router import router as user_routes
from src.modules.workflow.workflow_router import workflow_router_v1


def configure_routes(app: FastAPI) -> None:
	app.include_router(auth_router_v1, prefix="/v1/auth")
	app.include_router(settings_router_v1, prefix="/v1/settings")
	app.include_router(user_routes, prefix="/v1/users")
	app.include_router(complaint_router_v1, prefix="/v1/complaints")
	app.include_router(organization_router_v1, prefix="/v1/organizations")
	app.include_router(activities_router_v1, prefix="/v1/activities")
	app.include_router(departments_router_v1, prefix="/v1/departments")
	app.include_router(roles_router_v1, prefix="/v1/roles")
	app.include_router(dashboard_router_v1, prefix="/v1/dashboard")
	app.include_router(notification_router_v1, prefix="/v1/notifications")
	app.include_router(document_router_v1, prefix="/v1/documents")
	app.include_router(workflow_router_v1, prefix="/v1/workflows")
	app.include_router(application_router_v1, prefix="/v1/applications")
	app.include_router(financial_router_v1, prefix="/v1/financials")
