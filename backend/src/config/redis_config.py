from typing import Optional

import redis.asyncio as redis

from src.config.settings import REDIS_DB, REDIS_HOST, REDIS_PORT


class RedisConfig:
	"""Redis configuration settings - Singleton"""

	_instance = None
	_initialized = False

	redis_host: str = REDIS_HOST
	redis_port: int = REDIS_PORT
	redis_db: int = REDIS_DB
	redis_password: Optional[str] = None
	redis_username: Optional[str] = None
	redis_ssl: bool = False
	redis_max_connections: int = 20
	redis_socket_timeout: float = 5.0
	redis_socket_connect_timeout: float = 5.0
	redis_health_check_interval: int = 30
	redis_retry_on_timeout: bool = True
	redis_decode_responses: bool = True

	def __new__(cls, *args, **kwargs):
		if cls._instance is None:
			cls._instance = super().__new__(cls)
		return cls._instance

	def __init__(self, *args, **kwargs):
		if not self._initialized:
			super().__init__(*args, **kwargs)
			self._initialized = True
			print("Redis instance initiated successfully")

	@property
	def redis_url(self) -> str:
		"""Generate Redis URL from configuration"""
		scheme = "rediss" if self.redis_ssl else "redis"
		auth = ""

		if self.redis_username and self.redis_password:
			auth = f"{self.redis_username}:{self.redis_password}@"
		elif self.redis_password:
			auth = f":{self.redis_password}@"

		return f"{scheme}://{auth}{self.redis_host}:{self.redis_port}/{self.redis_db}"

	def get_redis_client(self) -> redis.Redis:
		"""Create and return Redis client instance"""
		return redis.Redis(
			host=self.redis_host,
			port=self.redis_port,
			db=self.redis_db,
			password=self.redis_password,
			username=self.redis_username,
			ssl=self.redis_ssl,
			max_connections=self.redis_max_connections,
			socket_timeout=self.redis_socket_timeout,
			socket_connect_timeout=self.redis_socket_connect_timeout,
			health_check_interval=self.redis_health_check_interval,
			retry_on_timeout=self.redis_retry_on_timeout,
			decode_responses=self.redis_decode_responses,
		)


_redis_config_instance: Optional[RedisConfig] = None


def get_redis_config() -> RedisConfig:
	"""Returns the singleton RedisConfig instance, ensuring it's initialized."""
	global _redis_config_instance
	if _redis_config_instance is None:
		_redis_config_instance = RedisConfig()

	return _redis_config_instance
