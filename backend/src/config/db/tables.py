"""
Centralized table names configuration.
Usage: from src.config.db.database import tables
Access: tables.users, tables.roles, etc.
"""


class TableNames:
	"""Centralized table names for the application."""

	accounts = "accounts"
	users = "users"
	loadable_items = "loadable_items"
	roles = "roles"
	permissions = "permissions"
	countries = "countries"
	organizations = "organizations"
	audit_logs = "audit_logs"
	role_permissions = "role_permissions"
	members = "members"
	currencies = "currencies"
	districts = "districts"
	regions = "regions"
	zones = "zones"
	traditional_authorities = "traditional_authorities"
	villages = "villages"
	constituencies = "constituencies"
	departments = "departments"
	user_departments = "user_departments"
	user_roles = "user_roles"
	sessions = "sessions"
	contacts = "contacts"
	directors = "directors"
	funding_sources = "funding_sources"
	target_groups = "target_groups"
	organization_staffs = "organization_staffs"
	location_activities = "location_activities"
	organization_auditors = "organization_auditors"
	supporting_documents = "supporting_documents"
	documents = "documents"
	template_stage_triggers = "template_stage_triggers"
	applications = "applications"
	templates = "templates"
	template_stages = "template_stages"
	template_stage_roles = "template_stage_roles"
	workflows = "workflows"
	workflow_stages = "workflow_stages"
	workflow_stage_roles = "workflow_stage_roles"
	invoices = "invoices"
	application_documents = "application_documents"
	invoice_documents = "invoice_documents"
	invoice_items = "invoice_items"
	fees = "fees"
	fee_change_logs = "fee_change_logs"
	application_fees = "application_fees"
	bank_details = "bank_details"
	organization_sectors = "organization_sectors"
	licences = "licences"
	permits = "permits"
	print_histories = "print_histories"
	printed_licences = "printed_licences"
	printed_permits = "printed_permits"
	payments = "payments"
	notifications = "notifications"
	activities = "activities"
	activity_invitations = "activity_invitations"
	organization_versions = "organization_versions"
	notification_recipients = "notification_recipients"
	member_invitations = "member_invitations"
	attendances = "attendances"
	organization_projects = "organization_projects"
	organization_donors = "organization_donors"
	menus = "menus"
	global_properties = "global_properties"
	village_development_committees = "village_development_committees"
	area_development_committees = "area_development_committees"
	complaints = "complaints"
	complaint_statuses = "complaint_statuses"
	complaint_attachments = "complaint_attachments"
	account_verifications = "account_verifications"

	@classmethod
	def get_all_tables(cls):
		"""Get all table names as a list."""
		return [
			value
			for key, value in cls.__dict__.items()
			if not key.startswith("_") and isinstance(value, str) and not callable(getattr(cls, key))
		]

	@classmethod
	def get_table_dict(cls):
		"""Get all table names as a dictionary."""
		return {
			key: value
			for key, value in cls.__dict__.items()
			if not key.startswith("_") and isinstance(value, str) and not callable(getattr(cls, key))
		}


tables = TableNames()


def validate_table_name(table_name: str) -> bool:
	"""Validate if a table name exists in our configuration."""
	return table_name in tables.get_all_tables()


def get_table_name(table_attr: str) -> str:
	"""Get table name safely with error handling."""
	try:
		return getattr(tables, table_attr)
	except AttributeError:
		raise ValueError(f"Table '{table_attr}' not found in table configuration")
