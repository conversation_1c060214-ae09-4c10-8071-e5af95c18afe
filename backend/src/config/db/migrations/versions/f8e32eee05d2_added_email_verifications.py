"""added email verifications

Revision ID: f8e32eee05d2
Revises: 5fbc7929cd99
Create Date: 2025-07-30 22:40:47.810072

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "f8e32eee05d2"
down_revision: Union[str, None] = "5fbc7929cd99"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
	"""Upgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.create_table(
		"email_verifications",
		sa.Column("id", sa.UUID(), nullable=False, comment="Primary key UUID for this table"),
		sa.Column("email", sa.String(), nullable=False),
		sa.Column("code", sa.String(), nullable=False),
		sa.Column("verified", sa.Boolean(), nullable=True),
		sa.Column(
			"type",
			sa.Enum(
				"ACCOUNT_REGISTRATION",
				"EMAIL_CHANGE",
				"PASSWORD_RESET",
				"LOGIN_2FA",
				"UNUSUAL_LOGIN_ATTEMPT",
				"DEVICE_VERIFICATION",
				"ACCOUNT_DELETION_CONFIRMATION",
				"ADMIN_INVITE",
				"EMAIL_CONFIRMATION_REMINDER",
				"SECURITY_ALERT_CONFIRMATION",
				name="verificationtype",
			),
			nullable=False,
		),
		sa.Column("expires_at", sa.DateTime(), nullable=False),
		sa.Column("user_id", sa.UUID(), nullable=True),
		sa.Column("created_at", sa.TIMESTAMP(timezone=True), server_default=sa.text("now()"), nullable=False),
		sa.Column("updated_at", sa.TIMESTAMP(timezone=True), server_default=sa.text("now()"), nullable=False),
		sa.ForeignKeyConstraint(
			["user_id"],
			["users.id"],
		),
		sa.PrimaryKeyConstraint("id"),
		sa.UniqueConstraint("id"),
	)
	# ### end Alembic commands ###


def downgrade() -> None:
	"""Downgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.drop_table("email_verifications")
	# ### end Alembic commands ###
