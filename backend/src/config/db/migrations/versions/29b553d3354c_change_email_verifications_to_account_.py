"""change email_verifications to account verifications table

Revision ID: 29b553d3354c
Revises: db3f276d852d
Create Date: 2025-08-02 12:09:35.682180

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

revision: str = "29b553d3354c"
down_revision: Union[str, None] = "db3f276d852d"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
	"""Upgrade schema."""
	op.drop_table("email_verifications")

	op.execute("DROP TYPE IF EXISTS verificationtype CASCADE")

	op.create_table(
		"account_verifications",
		sa.Column("id", sa.UUID(), nullable=False, comment="Primary key UUID for this table"),
		sa.Column("code", sa.String(), nullable=False),
		sa.Column("verified", sa.<PERSON><PERSON>(), nullable=True),
		sa.Column(
			"type",
			sa.Enum(
				"ACCOUNT_REGISTRATION",
				"EMAIL_CHANGE",
				"PASSWORD_RESET",
				"LOGIN_2FA",
				"UNUSUAL_LOGIN_ATTEMPT",
				"DEVICE_VERIFICATION",
				"ACCOUNT_DELETION_CONFIRMATION",
				"ADMIN_INVITE",
				"EMAIL_CONFIRMATION_REMINDER",
				"SECURITY_ALERT_CONFIRMATION",
				"ORGANIZATION_MEMBER_INVITE",
				name="verificationtype",
			),
			nullable=False,
		),
		sa.Column("expires_at", sa.DateTime(), nullable=False),
		sa.Column("user_id", sa.UUID(), nullable=True),
		sa.Column("created_at", sa.TIMESTAMP(timezone=True), server_default=sa.text("now()"), nullable=False),
		sa.Column("updated_at", sa.TIMESTAMP(timezone=True), server_default=sa.text("now()"), nullable=False),
		sa.ForeignKeyConstraint(
			["user_id"],
			["users.id"],
		),
		sa.PrimaryKeyConstraint("id"),
		sa.UniqueConstraint("id"),
	)


def downgrade() -> None:
	"""Downgrade schema."""

	op.drop_table("account_verifications")
	op.execute("DROP TYPE IF EXISTS verificationtype CASCADE")

	op.create_table(
		"email_verifications",
		sa.Column("id", sa.UUID(), autoincrement=False, nullable=False, comment="Primary key UUID for this table"),
		sa.Column("email", sa.VARCHAR(), autoincrement=False, nullable=False),
		sa.Column("code", sa.VARCHAR(), autoincrement=False, nullable=False),
		sa.Column("verified", sa.BOOLEAN(), autoincrement=False, nullable=True),
		sa.Column(
			"type",
			postgresql.ENUM(
				"ACCOUNT_REGISTRATION",
				"EMAIL_CHANGE",
				"PASSWORD_RESET",
				"LOGIN_2FA",
				"UNUSUAL_LOGIN_ATTEMPT",
				"DEVICE_VERIFICATION",
				"ACCOUNT_DELETION_CONFIRMATION",
				"ADMIN_INVITE",
				"EMAIL_CONFIRMATION_REMINDER",
				"SECURITY_ALERT_CONFIRMATION",
				name="verificationtype",
			),
			autoincrement=False,
			nullable=False,
		),
		sa.Column("expires_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
		sa.Column("user_id", sa.UUID(), autoincrement=False, nullable=True),
		sa.Column(
			"created_at",
			postgresql.TIMESTAMP(timezone=True),
			server_default=sa.text("now()"),
			autoincrement=False,
			nullable=False,
		),
		sa.Column(
			"updated_at",
			postgresql.TIMESTAMP(timezone=True),
			server_default=sa.text("now()"),
			autoincrement=False,
			nullable=False,
		),
		sa.ForeignKeyConstraint(["user_id"], ["users.id"], name="email_verifications_user_id_fkey"),
		sa.PrimaryKeyConstraint("id", name="email_verifications_pkey"),
		sa.UniqueConstraint("id", name="email_verifications_id_key"),
	)
