"""add verified column to user

Revision ID: db3f276d852d
Revises: f8e32eee05d2
Create Date: 2025-07-31 14:26:55.497683

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "db3f276d852d"
down_revision: Union[str, None] = "f8e32eee05d2"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
	"""Upgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.create_unique_constraint(None, "email_verifications", ["id"])
	op.add_column("users", sa.Column("verified", sa.<PERSON>an(), nullable=True))
	# ### end Alembic commands ###


def downgrade() -> None:
	"""Downgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.drop_column("users", "verified")
	op.drop_constraint(None, "email_verifications", type_="unique")
	# ### end Alembic commands ###
