"""nullable vdc and adc on location activities

Revision ID: 0216900ab3e8
Revises: 65418b375b65
Create Date: 2025-07-14 20:09:12.282421

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "0216900ab3e8"
down_revision: Union[str, None] = "65418b375b65"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
	"""Upgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.alter_column("location_activities", "vdc_id", existing_type=sa.UUID(), nullable=True)
	op.alter_column("location_activities", "adc_id", existing_type=sa.UUID(), nullable=True)
	# ### end Alembic commands ###


def downgrade() -> None:
	"""Downgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.alter_column("location_activities", "adc_id", existing_type=sa.UUID(), nullable=False)
	op.alter_column("location_activities", "vdc_id", existing_type=sa.UUID(), nullable=False)
	# ### end Alembic commands ###
