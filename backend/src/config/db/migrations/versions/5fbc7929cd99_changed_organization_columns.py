"""changed organization columns

Revision ID: 5fbc7929cd99
Revises: 0216900ab3e8
Create Date: 2025-07-14 20:28:07.348818

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "5fbc7929cd99"
down_revision: Union[str, None] = "0216900ab3e8"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
	"""Upgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.alter_column(
		"organizations",
		"financial_start_month",
		existing_type=sa.DATE(),
		type_=sa.String(length=10),
		existing_nullable=False,
	)
	op.alter_column(
		"organizations",
		"financial_end_month",
		existing_type=sa.DATE(),
		type_=sa.String(length=10),
		existing_nullable=False,
	)
	# ### end Alembic commands ###


def downgrade() -> None:
	"""Downgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.alter_column(
		"organizations",
		"financial_end_month",
		existing_type=sa.String(length=10),
		type_=sa.DATE(),
		existing_nullable=False,
	)
	op.alter_column(
		"organizations",
		"financial_start_month",
		existing_type=sa.String(length=10),
		type_=sa.DATE(),
		existing_nullable=False,
	)
	# ### end Alembic commands ###
