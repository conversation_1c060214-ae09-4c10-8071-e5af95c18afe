import os

from dotenv import load_dotenv
from sqlalchemy import create_engine
from sqlalchemy.orm import Session, sessionmaker

from src.config.db.models.concerns.audit_concern import AuditConcern
from src.config.db.utils.sql_logger import sql_logger

load_dotenv()

DATABASE_URL = os.getenv("DATABASE_URL")

engine = create_engine(DATABASE_URL, echo=True)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine, class_=AuditConcern)


def get_db() -> Session:
	"""Get a database session"""
	db = SessionLocal()
	try:
		return db
	except:
		db.close()
		raise


sql_logger(engine=engine)
