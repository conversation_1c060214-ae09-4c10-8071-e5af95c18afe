from src.config.db import tables

from .base import AuditMixin, BaseModel, foreign_key, primary_key


class RolePermission(BaseModel, AuditMixin):
	"""RolePermission model representing the association between roles and permissions."""

	__tablename__ = tables.role_permissions

	id = primary_key()
	role_id = foreign_key(f"{tables.roles}.id", nullable=False)
	permission_id = foreign_key(f"{tables.loadable_items}.id", nullable=False)

	def __repr__(self):
		return f"<RolePermission(role_id={self.role_id}, permission_id={self.permission_id})>"
