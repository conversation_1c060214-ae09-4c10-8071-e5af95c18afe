from enum import Enum

from sqlalchemy import Column, Date
from sqlalchemy import Enum as SQLEnum
from sqlalchemy import String
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, foreign_key, primary_key


class LicenceType(str, Enum):
	CERTIFICATE = "CERTIFICATE"
	LICENCE = "LICENCE"


class Licence(BaseModel, AuditMixin):
	__tablename__ = tables.licences

	id = primary_key()
	licence_number = Column(String, nullable=False)
	organization_id = foreign_key(f"{tables.organizations}.id")
	invoice_item_id = foreign_key(f"{tables.invoice_items}.id")
	expires_at = Column(Date, nullable=False)
	type = Column(SQLEnum(LicenceType), nullable=False)

	# Relationships
	printed_licences = relationship(
		"PrintedLicence",
		back_populates="licence",
		cascade="all, delete-orphan",
		foreign_keys="PrintedLicence.licence_id",
	)
	organization = relationship(
		"Organization",
		back_populates="licences",
		uselist=False,
		foreign_keys=[organization_id],
	)
	invoice_item = relationship(
		"InvoiceItem",
		back_populates="licence",
		uselist=False,
		foreign_keys=[invoice_item_id],
	)

	def __repr__(self):
		return f"<Licence(id={self.id}, licence_number={self.licence_number}, organization_id={self.organization_id}, expires_at={self.expires_at}, type={self.type})>"
