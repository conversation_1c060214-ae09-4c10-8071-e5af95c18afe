from enum import Enum

from sqlalchemy import Column
from sqlalchemy import Enum as SQLEnum
from sqlalchemy import Float, String
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, foreign_key, primary_key


class PaymentStatus(str, Enum):
	"""Status for a payment transaction."""

	SUCCESS = "SUCCESS"
	"""Payment was successful."""

	FAILED = "FAILED"
	"""Payment failed."""


class Payment(BaseModel, AuditMixin):
	"""Model for payment transactions."""

	__tablename__ = tables.payments

	id = primary_key()
	amount = Column(Float, nullable=False)
	organization_id = foreign_key(f"{tables.organizations}.id", nullable=False)
	invoice_id = foreign_key(f"{tables.invoices}.id", nullable=True)
	transaction_number = Column(String(50), nullable=False, unique=True)
	payment_mode_id = foreign_key(f"{tables.loadable_items}.id", nullable=False)
	status = Column(SQLEnum(PaymentStatus), nullable=False)
	paid_by = Column(String(100), nullable=False)

	# Relationships
	organization = relationship(
		"Organization",
		back_populates="payments",
		uselist=False,
	)
	invoice = relationship(
		"Invoice",
		back_populates="payments",
		uselist=False,
	)
	payment_mode = relationship(
		"LoadableItem",
		back_populates="payments",
		uselist=False,
	)

	def __repr__(self):
		return f"<Payment(id={self.id}, amount={self.amount}, is_successful={self.is_successful})>"
