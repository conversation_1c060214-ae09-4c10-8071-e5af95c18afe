from sqlalchemy import Column, String
from sqlalchemy.orm import relationship

from src.config.db import tables

from .base import BaseModel, primary_key


class Country(BaseModel):
	__tablename__ = tables.countries

	id = primary_key()
	name = Column(String(50), unique=True, nullable=False)
	short_code = Column(String(5), unique=True, nullable=True)
	dial_code = Column(String(5), unique=False, nullable=True)
	flag = Column(String(500), unique=False, nullable=True)

	# Relationship
	organization_directors = relationship(
		"Director",
		back_populates="country",
		foreign_keys="Director.country_id",
		cascade="all, delete-orphan",
	)
