from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, foreign_key, primary_key


class LocationActivity(BaseModel, AuditMixin):
	__tablename__ = tables.location_activities

	id = primary_key()
	vdc_id = foreign_key(f"{tables.village_development_committees}.id", nullable=True)
	adc_id = foreign_key(f"{tables.area_development_committees}.id", nullable=True)
	organization_id = foreign_key(f"{tables.organizations}.id")
	district_id = foreign_key(f"{tables.districts}.id")

	# Relationships
	vdc = relationship(
		"VillageDevelopmentCommittee",
		back_populates="organization_activities",
		foreign_keys=[vdc_id],
		uselist=False,
	)
	adc = relationship(
		"AreaDevelopmentCommittee",
		back_populates="organization_activities",
		foreign_keys=[adc_id],
		uselist=False,
	)
	organization = relationship(
		"Organization",
		back_populates="location_activities",
		foreign_keys=[organization_id],
		uselist=False,
	)
	district = relationship(
		"District",
		back_populates="organization_activities",
		foreign_keys=[district_id],
		uselist=False,
	)
