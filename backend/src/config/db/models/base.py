import uuid
from enum import Enum

from sqlalchemy import T<PERSON><PERSON>TAMP, Column, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import declarative_base
from sqlalchemy.sql import func

from .concerns.audit_concern import AuditMixin, NonForeignKeyAuditMixin

Base = declarative_base()


def timestamp_column(nullable=False, timezone=True, use_default=False):
	if use_default:
		return Column(TIMESTAMP(timezone=timezone), server_default=func.now(), nullable=nullable)
	return Column(TIMESTAMP(timezone=timezone), nullable=nullable)


def uuid_column(nullable=True, set_default=False):
	return Column(
		UUID(as_uuid=True),
		default=uuid.uuid4 if set_default else None,
		unique=False,
		nullable=nullable,
	)


def primary_key() -> Column[UUID]:
	return Column(
		UUID(as_uuid=True),
		primary_key=True,
		default=uuid.uuid4,
		unique=True,
		nullable=False,
		comment="Primary key UUID for this table",
	)


def foreign_key(reference: str, nullable=False, primary_key=False) -> Column[UUID]:
	return Column(UUID(as_uuid=True), ForeignKey(reference), nullable=nullable, primary_key=primary_key)


class BasicStatus(str, Enum):
	ACTIVE = "ACTIVE"
	INACTIVE = "INACTIVE"


class Gender(str, Enum):
	MALE = "MALE"
	FEMALE = "FEMALE"


class InvitationStatus(str, Enum):
	PENDING = "PENDING"
	ACCEPTED = "ACCEPTED"
	REJECTED = "REJECTED"


class TimestampMixin:
	"""Mixin class for common timestamp columns"""

	created_at = Column(TIMESTAMP(timezone=True), server_default=func.now(), nullable=False)
	updated_at = Column(
		TIMESTAMP(timezone=True),
		server_default=func.now(),
		onupdate=func.now(),
		nullable=False,
	)


class BaseModel(Base, TimestampMixin):
	"""Abstract base model with common fields"""

	__abstract__ = True


class AuditMixin(AuditMixin):
	"""Mixin class for common audit columns"""

	pass


class NonForeignKeyAuditMixin(NonForeignKeyAuditMixin):
	"""Mixin class for common audit columns without foreign key constraints"""

	pass
