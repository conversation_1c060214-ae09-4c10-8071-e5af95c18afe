from enum import Enum

from sqlalchemy import Column, Date
from sqlalchemy import Enum as SQLEnum
from sqlalchemy import Float, String
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, foreign_key, primary_key


class InvoiceStatus(str, Enum):
	PENDING = "PENDING"
	PAID = "PAID"
	PARTIALLY_PAID = "PARTIALLY_PAID"


class Invoice(BaseModel, AuditMixin):
	__tablename__ = tables.invoices

	id = primary_key()
	reference_number = Column(String, nullable=False)
	status = Column(SQLEnum(InvoiceStatus), default=InvoiceStatus.PENDING, nullable=False)
	total_amount = Column(Float, default=0.0)
	due_date = Column(Date, nullable=False)
	description = Column(String)
	organization_id = foreign_key(f"{tables.organizations}.id", nullable=False)

	# relationships
	payments = relationship(
		"Payment",
		back_populates="invoice",
		cascade="all, delete-orphan",
		foreign_keys="Payment.invoice_id",
	)
	organization = relationship(
		"Organization",
		back_populates="invoices",
		uselist=False,
		foreign_keys=[organization_id],
	)
	invoice_documents = relationship(
		"InvoiceDocument",
		back_populates="invoice",
		cascade="all, delete-orphan",
		foreign_keys="InvoiceDocument.invoice_id",
	)
	items = relationship(
		"InvoiceItem",
		back_populates="invoice",
		cascade="all, delete-orphan",
		foreign_keys="InvoiceItem.invoice_id",
	)

	def __repr__(self):
		return f"<Invoice(id={self.id}, reference_number={self.reference_number}, status={self.status}, total_amount={self.total_amount}, due_date={self.due_date})>"
