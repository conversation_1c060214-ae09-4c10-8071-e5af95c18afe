from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, foreign_key, primary_key


class WorkflowStageRole(BaseModel, AuditMixin):
	"""Model for workflow stage users."""

	__tablename__ = tables.workflow_stage_roles

	id = primary_key()
	workflow_stage_id = foreign_key(f"{tables.workflow_stages}.id", nullable=False)
	role_id = foreign_key(f"{tables.roles}.id", nullable=False)

	# Relationships
	workflow_stage = relationship(
		"WorkflowStage",
		back_populates="roles",
		uselist=False,
	)

	role = relationship(
		"Role",
		back_populates="workflow_stage_roles",
		uselist=False,
	)

	def __repr__(self):
		return f"<WorkflowStageUser(id={self.id}, workflow_stage_id={self.workflow_stage_id}, role_id={self.role_id})>"
