from datetime import datetime
from enum import Enum

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Float, Integer, String
from sqlalchemy import Enum as SQLEnum
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, foreign_key, primary_key


class FeeStatus(Enum):
	ACTIVE = "ACTIVE"
	SCHEDULED = "SCHEDULED"
	EXPIRED = "EXPIRED"
	DRAFT = "DRAFT"


class Fee(BaseModel, AuditMixin):
	__tablename__ = tables.fees

	id = primary_key()
	amount = Column(Float, nullable=False)
	fee_category_id = foreign_key(f"{tables.loadable_items}.id")
	currency_id = foreign_key(f"{tables.currencies}.id")
	name = Column(String, nullable=False)
	based_on_income = Column(Boolean, nullable=False)
	min_income = Column(Float, nullable=False, default=0.00)
	max_income = Column(Float, nullable=False, default=0.00)
	description = Column(String)
	version = Column(Integer, default=1, nullable=False)
	effective_from = Column(DateTime, default=datetime.now(), nullable=False)
	effective_to = Column(DateTime, nullable=True)
	status = Column(SQLEnum(FeeStatus), default=FeeStatus.ACTIVE, nullable=False)
	replaces_fee_id = foreign_key(f"{tables.fees}.id", nullable=True)

	# Relationships
	application_fees = relationship(
		"ApplicationFee",
		back_populates="fee",
		cascade="all, delete-orphan",
		foreign_keys="ApplicationFee.fee_id",
	)
	currency = relationship("Currency", back_populates="fees", foreign_keys=[currency_id], uselist=False)
	category = relationship("LoadableItem", back_populates="fees", uselist=False, foreign_keys=[fee_category_id])
	previous_version = relationship("Fee", remote_side=[id], backref="next_versions")
	change_logs = relationship(
		"FeeChangeLog",
		back_populates="fee",
		foreign_keys="FeeChangeLog.fee_id",
		cascade="all, delete-orphan",
	)

	def __repr__(self):
		return f"<Fee(id={self.id}, code={self.fee_category_id}, name={self.name}, amount={self.amount}, status={self.status})>"
