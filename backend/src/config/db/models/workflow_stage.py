from enum import Enum

from sqlalchemy import Column, Connection, select
from sqlalchemy import Enum as SQLEnum
from sqlalchemy.orm import relationship

from src.config.db.models.workflow import Workflow
from src.config.db.models.application import Application, ApplicationStatus
from src.config.db.models.template_stage import TemplateStage
from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, foreign_key, primary_key
from sqlalchemy import event


class WorkflowStageStatus(str, Enum):
	"""Enum for workflow stage status."""

	PENDING = "PENDING"
	IN_REVIEW = "IN_REVIEW"
	COMPLETED = "COMPLETED"
	APPROVED = "APPROVED"
	REJECTED = "REJECTED"


class WorkflowStage(BaseModel, AuditMixin):
	"""Model for workflow stages."""

	__tablename__ = tables.workflow_stages

	id = primary_key()
	workflow_id = foreign_key(f"{tables.workflows}.id", nullable=False)
	template_stage_id = foreign_key(f"{tables.template_stages}.id", nullable=True)
	approved_by = foreign_key(f"{tables.users}.id", nullable=True)
	status = Column(
		SQLEnum(WorkflowStageStatus, nullable=False),
		default=WorkflowStageStatus.PENDING,
	)

	# Relationships
	workflow = relationship(
		"Workflow",
		back_populates="stages",
		uselist=False,
	)
	approver = relationship(
		"User",
		back_populates="approved_workflow_stages",
		foreign_keys=[approved_by],
		uselist=False,
	)
	template_stage = relationship(
		"TemplateStage",
		back_populates="workflow_stages",
		uselist=False,
	)
	roles = relationship(
		"WorkflowStageRole",
		back_populates="workflow_stage",
		lazy="joined",
		cascade="all, delete-orphan",
		foreign_keys="WorkflowStageRole.workflow_stage_id",
	)

	def __repr__(self):
		return f"<WorkflowStage(id={self.id}, workflow_id={self.workflow_id})>"


	"""
		trigger to update application status
		1. If any stage is rejected, the application is rejected
		2. If all stages are completed, the application is approved
		3. If any stage is in review, the application is in review
	
	"""
@event.listens_for(WorkflowStage, "after_insert")
def handle_after_insert(mapper, connection: Connection, target: "WorkflowStage"):
	application = connection.execute(
		select(Application).where(Application.id == target.workflow.application_id)
	).fetchone()
 
	stages_required: list[TemplateStage] = connection.execute(
		select(TemplateStage).where(TemplateStage.template_id == application.type)
	).fetchall()
 
	stages_done: list[WorkflowStage] = connection.execute(
		select(WorkflowStage).where(WorkflowStage.workflow_id == target.workflow_id)
	).fetchall()
 
	if any(stage.status == WorkflowStageStatus.REJECTED for stage in stages_done):
		connection.execute(
			Application.__table__.update()
			.where(Application.id == application.id)
			.values(status=ApplicationStatus.REJECTED)
		)
 
	elif len(stages_required) == len(stages_done):
		connection.execute(
			Application.__table__.update()
			.where(Application.id == application.id)
			.values(status=ApplicationStatus.REGISTERED)
		)
  
	elif len(stages_done) > 0:
		connection.execute(
			Workflow.__table__.update()
			.where(Workflow.id == target.workflow_id)
			.values(status=WorkflowStageStatus.IN_REVIEW)
		)
 
	
  
