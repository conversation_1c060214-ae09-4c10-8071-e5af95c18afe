import sqlalchemy as sa
from sqlalchemy import BinaryExpression, <PERSON>olean, Column, ForeignKey, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import ORMExecuteState, Session, relationship, with_loader_criteria


class NonForeignKeyAuditMixin:
	created_by = Column(UUID(as_uuid=True), nullable=True)
	updated_by = Column(UUID(as_uuid=True), nullable=True)
	voided = Column(Boolean, default=False, nullable=False)
	voided_by = Column(UUID(as_uuid=True), nullable=True)
	void_reason = Column(Text, nullable=True)

	@classmethod
	def make_where_criteria(cls) -> BinaryExpression[bool]:
		return getattr(cls, "voided").is_(False)


class AuditMixin:
	created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
	updated_by = Column(UUID(as_uuid=True), Foreign<PERSON>ey("users.id"), nullable=True)
	voided = Column(Boolean, default=False, nullable=False)
	voided_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
	void_reason = Column(Text, nullable=True)

	@classmethod
	def make_where_criteria(cls) -> BinaryExpression[bool]:
		return getattr(cls, "voided").is_(False)

	@declared_attr
	def created_by_user(cls):
		return relationship("User", foreign_keys=[cls.created_by], overlaps="_created_by_user")

	@declared_attr
	def updated_by_user(cls):
		return relationship("User", foreign_keys=[cls.updated_by], overlaps="_updated_by_user")

	@declared_attr
	def voided_by_user(cls):
		return relationship("User", foreign_keys=[cls.voided_by], overlaps="_voided_by_user")


class AuditConcern(Session):
	def delete(self, instance):
		if hasattr(instance, "voided"):
			setattr(instance, "voided", True)
			self.add(instance)  # mark as dirty so it gets updated
		else:
			super().delete(instance)


@sa.event.listens_for(Session, "do_orm_execute")
def _do_orm_execute(orm_execute_state: ORMExecuteState) -> None:
	if not (
		orm_execute_state.is_select
		and not orm_execute_state.is_column_load
		and not orm_execute_state.is_relationship_load
	):
		return

	for mixin in [AuditMixin, NonForeignKeyAuditMixin]:
		orm_execute_state.statement = orm_execute_state.statement.options(
			with_loader_criteria(
				mixin,
				lambda cls: cls.make_where_criteria() if hasattr(cls, "voided") else None,
				include_aliases=True,
				propagate_to_loaders=True,
			),
		)
