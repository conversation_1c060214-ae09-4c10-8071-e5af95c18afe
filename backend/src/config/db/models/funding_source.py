from sqlalchemy import Column, Float, String
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, foreign_key, primary_key


class FundingSource(BaseModel, AuditMixin):
	__tablename__ = tables.funding_sources

	id = primary_key()
	organization_id = foreign_key(f"{tables.organizations}.id")
	donor_id = foreign_key(f"{tables.loadable_items}.id")
	currency_id = foreign_key(f"{tables.currencies}.id")
	contact_person = Column(String, nullable=False)
	amount = Column(Float, nullable=False, default=0.0)

	# Relationships
	donor = relationship("LoadableItem", back_populates="funding_sources_donors", foreign_keys=[donor_id])
	organization = relationship(
		"Organization",
		back_populates="funding_sources",
		uselist=False,
		foreign_keys=[organization_id],
	)
	currency = relationship(
		"Currency",
		back_populates="funding_sources",
		uselist=False,
		foreign_keys=[currency_id],
	)

	def __repr__(self):
		return f"<FundingSource(id={self.id}, organization_id={self.organization_id}, donor_id={self.donor_id}, currency_id={self.currency_id}, contact_person={self.contact_person}, amount={self.amount})>"
