from sqlalchemy import Column, Float
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, foreign_key, primary_key


class ApplicationFee(BaseModel, AuditMixin):
	__tablename__ = tables.application_fees

	id = primary_key()
	amount = Column(Float, nullable=False)
	fee_id = foreign_key(f"{tables.fees}.id")
	application_id = foreign_key(f"{tables.applications}.id")
	organization_income = Column(Float, nullable=True)

	# Relationships
	application = relationship(
		"Application",
		back_populates="application_fees",
		uselist=False,
		foreign_keys=[application_id],
	)
	fee = relationship(
		"Fee",
		back_populates="application_fees",
		uselist=False,
		foreign_keys=[fee_id],
	)

	def __repr__(self):
		return f"<ApplicationFee(id={self.id}, amount={self.amount}, fee_id={self.fee_id})>"

	@property
	def organization_id(self):
		return self.application.organization_id if self.application else None
