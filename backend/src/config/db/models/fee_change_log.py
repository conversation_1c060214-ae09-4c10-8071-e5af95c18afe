from enum import Enum

from sqlalchemy import J<PERSON><PERSON>, Column, DateTime, Text
from sqlalchemy import Enum as SQLEnum
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import BaseModel, foreign_key, primary_key
from src.config.db.models.concerns.audit_concern import AuditMixin


class ChangeLogType(str, Enum):
	CREATED = "CREATED"
	UPDATED = "UPDATED"
	SCHEDULED = "SCHEDULED"
	ACTIVATED = "ACTIVATED"
	EXPIRED = "EXPIRED"


class FeeChangeLog(BaseModel, AuditMixin):
	"""Audit trail for fee changes"""

	__tablename__ = tables.fee_change_logs

	id = primary_key()
	fee_id = foreign_key(f"{tables.fees}.id")
	change_type = Column(SQLEnum(ChangeLogType), nullable=False)
	old_values = Column(JSON)
	new_values = Column(JSON)
	change_reason = Column(Text)
	scheduled_date = Column(DateTime, nullable=True)

	# relationship
	fee = relationship("Fee", back_populates="change_logs", foreign_keys=[fee_id], uselist=False)

	def __repr__(self):
		return f"<FeeChangeLog id={self.id} fee_id={self.fee_id} change_type={self.change_reason} >"
