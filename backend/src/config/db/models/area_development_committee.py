from sqlalchemy import Column, Enum, String
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (
	AuditMixin,
	BaseModel,
	BasicStatus,
	foreign_key,
	primary_key,
)


class AreaDevelopmentCommittee(BaseModel, AuditMixin):
	__tablename__ = tables.area_development_committees

	id = primary_key()
	district_id = foreign_key(f"{tables.districts}.id")
	name = Column(String, nullable=False)
	status = Column(Enum(BasicStatus), default=BasicStatus.ACTIVE)

	organization_activities = relationship(
		"LocationActivity",
		back_populates="adc",
		foreign_keys="LocationActivity.adc_id",
		uselist=True,
	)
	district = relationship(
		"District",
		back_populates="area_development_committees",
		foreign_keys=[district_id],
		uselist=False,
	)

	def __repr__(self):
		return f"<AreaDevelopmentCommittee(id={self.id}, name={self.name}, status={self.status})>"
