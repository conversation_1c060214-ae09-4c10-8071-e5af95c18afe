from sqlalchemy import <PERSON><PERSON><PERSON>, Column
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, foreign_key, primary_key


class NotificationRecipient(BaseModel, AuditMixin):
	"""Model for notification recipients."""

	__tablename__ = tables.notification_recipients

	id = primary_key()
	notification_id = foreign_key(f"{tables.notifications}.id")
	account_id = foreign_key(f"{tables.accounts}.id")
	is_read = Column(Boolean, default=False, nullable=False)
	is_archived = Column(Boolean, default=False, nullable=False)

	# Relationships
	notification = relationship(
		"Notification",
		back_populates="recipients",
		foreign_keys=[notification_id],
		uselist=False,
	)
	recipient = relationship(
		"Account",
		back_populates="received_notifications",
		foreign_keys=[account_id],
		uselist=False,
	)

	def __repr__(self):
		return f"<NotificationRecipient(id={self.id}, notification_id={self.notification_id}, account_id={self.account_id}, is_read={self.is_read}, is_archived={self.is_archived})>"
