from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Date
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, foreign_key, primary_key


class Permit(AuditMixin, BaseModel):
	"""Model for permits issued to organizations."""

	__tablename__ = tables.permits

	id = primary_key()
	organization_id = foreign_key(f"{tables.organizations}.id", nullable=False)
	permit_type_id = foreign_key(f"{tables.loadable_items}.id", nullable=False)
	invoice_item_id = foreign_key(f"{tables.invoice_items}.id", nullable=True)
	is_active = Column(Boolean, default=True, nullable=False)
	expires_at = Column(Date, nullable=True)

	# Relationships
	organization = relationship(
		"Organization",
		back_populates="permits",
		uselist=False,
	)
	permit_type = relationship(
		"LoadableItem",
		back_populates="permits",
		uselist=False,
	)
	invoice_item = relationship(
		"InvoiceItem",
		back_populates="permit",
		uselist=False,
	)
	printed_permits = relationship(
		"PrintedPermit",
		back_populates="permit",
		cascade="all, delete-orphan",
		foreign_keys="PrintedPermit.permit_id",
	)

	def __repr__(self):
		return f"<Permit(id={self.id}, organization_id={self.organization_id}, is_active={self.is_active})>"
