from sqlalchemy import Column, String
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, foreign_key, primary_key


class InvoiceDocument(BaseModel, AuditMixin):
	__tablename__ = tables.invoice_documents

	id = primary_key()
	invoice_id = foreign_key(f"{tables.invoices}.id")
	document_id = foreign_key(f"{tables.documents}.id")
	description = Column(String)

	# Relationships
	document = relationship("Document", back_populates="invoice_documents", foreign_keys=[document_id])
	invoice = relationship(
		"Invoice",
		back_populates="invoice_documents",
		uselist=False,
		foreign_keys=[invoice_id],
	)

	def __repr__(self):
		return f"<InvoiceDocument(id={self.id}, invoice_id={self.invoice_id}, document_id={self.document_id}, description={self.description})>"
