from sqlalchemy import <PERSON><PERSON><PERSON>, Column
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, foreign_key, primary_key


class TemplateStageRole(BaseModel, AuditMixin):
	"""Model for template stage users."""

	__tablename__ = tables.template_stage_roles

	id = primary_key()
	template_stage_id = foreign_key(f"{tables.template_stages}.id", nullable=False)
	role_id = foreign_key(f"{tables.roles}.id", nullable=False)
	is_active = Column(Boolean, default=True)

	# Relationships
	template_stage = relationship(
		"TemplateStage",
		back_populates="roles",
		uselist=False,
	)
	role = relationship(
		"Role",
		back_populates="template_stage_roles",
		uselist=False,
	)

	def __repr__(self):
		return f"<TemplateStageUser(id={self.id}, template_stage_id={self.template_stage_id}, role_id={self.role_id})>"
