from sqlalchemy import <PERSON><PERSON><PERSON><PERSON><PERSON>, Column, String, Boolean, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from src.config.db.tables import tables

from .base import BaseModel, foreign_key, primary_key


class Session(BaseModel):
	__tablename__ = tables.sessions

	id = primary_key()
	user_id = foreign_key("users.id")
	session_token = Column(String(255), unique=True, nullable=False)
	refresh_token_hash = Column(String(255), nullable=True)
	device = Column(String(50))
	ip_address = Column(String(20))
	user_agent = Column(Text, nullable=True)
	is_active = Column(Boolean, default=True, nullable=False)
	last_activity = Column(TIMESTAMP(timezone=True), server_default=func.now(), nullable=False)
	logged_out_at = Column(TIMESTAMP(timezone=True), nullable=True)
	expires_at = Column(TIMESTAMP(timezone=True), nullable=False)
	remember_me = Column(Boolean, default=False, nullable=True)

	# Relationships
	user = relationship("User", foreign_keys=[user_id], uselist=False, back_populates="sessions")
