from sqlalchemy import <PERSON><PERSON><PERSON>, Column, String
from sqlalchemy import Enum as SQLEnum
from sqlalchemy.orm import relationship

from src.config.db import tables

from .base import BaseModel, Gender, NonForeignKeyAuditMixin, foreign_key, primary_key


class User(BaseModel, NonForeignKeyAuditMixin):
	__tablename__ = tables.users

	id = primary_key()
	first_name = Column(String(50), nullable=False)
	middle_name = Column(String(50), nullable=True)
	last_name = Column(String(50), nullable=False)
	email = Column(String(100), unique=True, nullable=False)
	hashed_password = Column(String(255), nullable=False)
	account_id = foreign_key("accounts.id", nullable=False)
	is_external = Column(Boolean, default=True)
	verified = Column(Boolean, default=False)
	gender = Column(SQLEnum(Gender), nullable=True)
	phone = Column(String(20), nullable=True, default=None)

	# Relationships
	account = relationship("Account", foreign_keys=[account_id], back_populates="user", innerjoin=True)

	organization_memberships = relationship(
		"Member",
		back_populates="user",
		foreign_keys="[Member.user_id]",
		cascade="all, delete-orphan",
	)

	departments = relationship(
		"Department",
		secondary=tables.user_departments,
		back_populates="users",
		primaryjoin="User.id == UserDepartment.user_id",
		secondaryjoin="Department.id == UserDepartment.department_id",
		viewonly=True,
		lazy="joined",
	)
	roles = relationship(
		"Role",
		secondary=tables.user_roles,
		back_populates="users",
		primaryjoin="User.id == UserRole.user_id",
		secondaryjoin="Role.id == UserRole.role_id",
		viewonly=True,
		lazy="joined",
	)
	sessions = relationship("Session", foreign_keys="Session.user_id", uselist=False, back_populates="user")
	approved_workflow_stages = relationship(
		"WorkflowStage",
		foreign_keys="WorkflowStage.approved_by",
		back_populates="approver",
	)
	sent_notifications = relationship(
		"Notification",
		foreign_keys="Notification.sender_id",
		back_populates="sender",
		uselist=True,
		cascade="all, delete-orphan",
	)
	sent_member_invitations = relationship(
		"MemberInvitation",
		foreign_keys="MemberInvitation.inviter_user_id",
		back_populates="inviter",
		uselist=True,
		cascade="all, delete-orphan",
	)
	audit_logs = relationship(
		"AuditLog",
		back_populates="user",
		foreign_keys="AuditLog.user_id",
		uselist=True,
		cascade="all, delete-orphan",
	)
	account_verifications = relationship(
		"AccountVerification",
		back_populates="user",
		cascade="all, delete-orphan",
	)

	@property
	def organizations(self):
		"""Get all active organizations for this user"""
		return [membership.organization for membership in self.organization_memberships if membership.is_active]

	# Organizations created by this user
	created_organizations = relationship(
		"Organization", primaryjoin="User.id == Organization.created_by", viewonly=True
	)

	def __repr__(self):
		return f"<User(id={self.id}, username={self.username}, account_id={self.account_id})>"
