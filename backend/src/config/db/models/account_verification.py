import enum

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, DateTime, Enum, String
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import BaseModel, foreign_key, primary_key


class VerificationType(enum.Enum):
	ACCOUNT_REGISTRATION = "ACCOUNT_REGISTRATION"
	EMAIL_CHANGE = "EMAIL_CHANGE"
	PASSWORD_RESET = "PASSWORD_RESET"
	LOGIN_2FA = "LOGIN_2FA"
	UNUSUAL_LOGIN_ATTEMPT = "UNUSUAL_LOGIN_ATTEMPT"
	DEVICE_VERIFICATION = "DEVICE_VERIFICATION"
	ACCOUNT_DELETION_CONFIRMATION = "ACCOUNT_DELETION_CONFIRMATION"
	ADMIN_INVITE = "ADMIN_INVITE"
	EMAIL_CONFIRMATION_REMINDER = "EMAIL_CONFIRMATION_REMINDER"
	SECURITY_ALERT_CONFIRMATION = "SECURITY_ALERT_CONFIRMATION"
	ORGANIZATION_MEMBER_INVITE = "ORGANIZATION_MEMBER_INVITE"


class AccountVerification(BaseModel):
	__tablename__ = tables.account_verifications

	id = primary_key()
	code = Column(String, nullable=False)
	verified = Column(Boolean, default=False)
	type = Column(Enum(VerificationType), nullable=False)
	expires_at = Column(DateTime, nullable=False)
	user_id = foreign_key(f"{tables.users}.id", nullable=True)

	user = relationship("User", back_populates="account_verifications", uselist=False)

	def __repr__(self):
		return f"<AccountVerification(type={self.type}, used={self.is_used})>"
