from sqlalchemy import Column, String
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, foreign_key, primary_key


class District(BaseModel, AuditMixin):
	__tablename__ = tables.districts

	id = primary_key()
	name = Column(String(50), nullable=False, unique=True)
	code = Column(String(5), nullable=False, unique=True)
	region_id = foreign_key(f"{tables.regions}.id")

	# Relationships
	region = relationship("Region", back_populates="districts", foreign_keys=[region_id])
	traditional_authorities = relationship(
		"TraditionalAuthority",
		back_populates="district",
		foreign_keys="TraditionalAuthority.district_id",
		cascade="all, delete-orphan",
	)
	villages = relationship(
		"Village",
		back_populates="district",
		foreign_keys="Village.district_id",
		cascade="all, delete-orphan",
	)
	village_development_committees = relationship(
		"VillageDevelopmentCommittee",
		back_populates="district",
		foreign_keys="VillageDevelopmentCommittee.district_id",
		cascade="all, delete-orphan",
	)
	organizations = relationship(
		"Organization",
		back_populates="district",
		foreign_keys="Organization.district_id",
		cascade="all, delete-orphan",
	)
	activities = relationship(
		"Activity",
		back_populates="district",
		foreign_keys="Activity.district_id",
		cascade="all, delete-orphan",
	)
	organization_activities = relationship(
		"LocationActivity",
		back_populates="district",
		foreign_keys="LocationActivity.district_id",
		cascade="all, delete-orphan",
	)
	area_development_committees = relationship(
		"AreaDevelopmentCommittee",
		back_populates="district",
		foreign_keys="AreaDevelopmentCommittee.district_id",
		cascade="all, delete-orphan",
	)

	def __repr__(self):
		return f"<District(id={self.id}, name={self.name}, code={self.code})>"
