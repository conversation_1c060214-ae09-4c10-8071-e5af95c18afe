from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String

from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, foreign_key, primary_key


class Menu(BaseModel, AuditMixin):
	"""Model for menu items."""

	__tablename__ = tables.menus

	id = primary_key()
	name = Column(String(255), nullable=False)
	path = Column(String(255), nullable=True)
	icon = Column(String(255), nullable=True)
	position = Column(Integer, nullable=False, default=0)
	parent_id = foreign_key(f"{tables.menus}.id", nullable=True)
	is_active = Column(Boolean, default=True, nullable=False)
