from sqlalchemy import Column, String
from sqlalchemy.orm import relationship

from src.config.db import tables

from .base import BaseModel, AuditMixin, primary_key


class Role(BaseModel, AuditMixin):
	__tablename__ = tables.roles

	id = primary_key()
	code = Column(String, nullable=False, unique=True)
	name = Column(String, nullable=False)
	description = Column(String, nullable=True)

	# Relationships
	role_permissions = relationship(
		"LoadableItem",
		secondary=tables.role_permissions,
		back_populates="role_permissions",
		primaryjoin="Role.id == RolePermission.role_id",
		secondaryjoin="LoadableItem.id == RolePermission.permission_id",
		viewonly=True,
	)

	users = relationship(
		"User",
		secondary=tables.user_roles,
		back_populates=tables.roles,
		primaryjoin="Role.id == UserRole.role_id",
		secondaryjoin="User.id == UserRole.user_id",
		viewonly=True,
	)
	workflow_stage_roles = relationship(
		"WorkflowStageRole",
		back_populates="role",
		cascade="all, delete-orphan",
		foreign_keys="WorkflowStageRole.role_id",
	)

	template_stage_roles = relationship(
		"TemplateStageRole",
		back_populates="role",
		cascade="all, delete-orphan",
		foreign_keys="TemplateStageRole.role_id",
	)

	def __repr__(self):
		return f"<Role(id={self.id}, code={self.code}, name={self.name}, description={self.description})>"
