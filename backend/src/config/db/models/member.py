from enum import Enum

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime
from sqlalchemy import Enum as SQLEnum
from sqlalchemy import func
from sqlalchemy.orm import relationship

from src.config.db import tables

from .base import AuditMixin, BaseModel, foreign_key, primary_key


class MemberRole(str, Enum):
	"""Roles a user can have in an organization"""

	OWNER = "OWNER"
	MEMBER = "MEMBER"


class Member(BaseModel, AuditMixin):
	"""Junction table tracking users in organizations with roles"""

	__tablename__ = tables.members

	id = primary_key()
	organization_id = foreign_key("organizations.id", nullable=False)
	user_id = foreign_key("users.id", nullable=False)
	role = Column(SQLEnum(MemberRole), nullable=False, default=MemberRole.MEMBER)
	joined_at = Column(DateTime(timezone=True), server_default=func.now())
	is_active = Column(Boolean, default=True)

	# Relationships
	organization = relationship("Organization", back_populates="members", foreign_keys=[organization_id])
	user = relationship("User", back_populates="organization_memberships", foreign_keys=[user_id])

	def __repr__(self):
		return f"<Member(org_id={self.organization_id}, user_id={self.user_id}, role={self.role})>"
