from sqlalchemy import Column, Date
from sqlalchemy import Enum as SQLEnum
from sqlalchemy import Float, String, Text
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, foreign_key, primary_key
from src.config.db.models.organization import OrganizationStatus


class OrganizationVersion(BaseModel, AuditMixin):
	"""Model for organization versions."""

	__tablename__ = tables.organization_versions

	id = primary_key()
	organization_id = foreign_key(f"{tables.organizations}.id", nullable=False)
	name = Column(Text, nullable=False, unique=True)
	abbreviation = Column(String(20), nullable=False)
	organization_type_id = foreign_key(f"{tables.loadable_items}.id", nullable=False)
	registration_number = Column(String(20), nullable=False)
	district_id = foreign_key(f"{tables.districts}.id", nullable=False)
	financial_start_month = Column(Date, nullable=False)
	financial_end_month = Column(Date, nullable=False)
	charity_number = Column(String(50), unique=True, nullable=True)
	annual_income = Column(Float, default=0.0)
	registration_type_id = foreign_key(f"{tables.loadable_items}.id", nullable=False)
	account_id = foreign_key(f"{tables.accounts}.id", nullable=False)
	biography = Column(Text, nullable=True)
	vision = Column(Text, nullable=True)
	motto = Column(Text, nullable=True)
	objectives = Column(JSONB, nullable=True)
	status = Column(SQLEnum(OrganizationStatus))

	# Relationships
	organization = relationship(
		"Organization",
		back_populates="versions",
		uselist=False,
	)

	def __repr__(self):
		return f"<OrganizationVersion(id={self.id}, organization_id={self.organization_id}, version_number={self.version_number}, is_active={self.is_active})>"
