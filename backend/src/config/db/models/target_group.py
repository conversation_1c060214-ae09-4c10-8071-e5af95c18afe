from sqlalchemy import <PERSON>olean, Column
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, foreign_key, primary_key


class TargetGroup(BaseModel, AuditMixin):
	"""Model for target groups."""

	__tablename__ = tables.target_groups

	id = primary_key()
	organization_id = foreign_key(f"{tables.organizations}.id", nullable=False)
	type_id = foreign_key(f"{tables.loadable_items}.id", nullable=False)
	is_active = Column(Boolean, default=True)

	# Relationships
	organization = relationship(
		"Organization",
		back_populates="target_groups",
		uselist=False,
	)
	type = relationship(
		"LoadableItem",
		back_populates="target_groups",
		uselist=False,
	)

	def __repr__(self):
		return f"<TargetGroup(id={self.id}, organization_id={self.organization_id}, type_id={self.type_id})>"
