from sqlalchemy import <PERSON><PERSON><PERSON>, Column, String
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, foreign_key, primary_key


class OrganizationAuditor(BaseModel, AuditMixin):
	"""Model for organization auditors."""

	__tablename__ = tables.organization_auditors

	id = primary_key()
	organization_id = foreign_key(f"{tables.organizations}.id")
	name = Column(String(255), nullable=True)
	email = Column(String(255), nullable=True)
	phone = Column(String(20), nullable=True)
	address = Column(JSONB, nullable=True)
	is_active = Column(Boolean, default=True, nullable=False)

	# Relationships
	organization = relationship(
		"Organization",
		back_populates="auditors",
		uselist=False,
	)

	def __repr__(self):
		return (
			f"<OrganizationAuditor(id={self.id}, organization_id={self.organization_id}, is_active={self.is_active})>"
		)
