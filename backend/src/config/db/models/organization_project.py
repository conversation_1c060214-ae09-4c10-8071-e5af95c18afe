from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, foreign_key, primary_key


class OrganizationProject(BaseModel, AuditMixin):
	"""Model for organization projects."""

	__tablename__ = tables.organization_projects

	id = primary_key()
	organization_id = foreign_key(f"{tables.organizations}.id")
	name = Column(String(255), nullable=True)
	thematic_area_id = foreign_key(f"{tables.loadable_items}.id", nullable=False)
	number_of_beneficiaries = Column(Integer, nullable=True)
	is_active = Column(Boolean, default=True, nullable=False)

	# Relationships
	organization = relationship(
		"Organization",
		back_populates="projects",
		uselist=False,
	)
	thematic_area = relationship("LoadableItem", back_populates="organization_thematic_areas", uselist=False)

	def __repr__(self):
		return f"<OrganizationProject(id={self.id}, organization_id={self.organization_id}, name={self.name}, is_active={self.is_active})>"
