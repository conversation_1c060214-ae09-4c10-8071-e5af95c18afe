from sqlalchemy import <PERSON><PERSON><PERSON>, Column, String
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, primary_key


class Template(BaseModel, AuditMixin):
	"""Model for templates."""

	__tablename__ = tables.templates

	id = primary_key()
	name = Column(String, nullable=False, unique=True)
	code = Column(String, nullable=False, unique=True)
	description = Column(String, nullable=True)
	is_active = Column(Boolean, default=True)

	# Relationships
	stages = relationship(
		"TemplateStage",
		back_populates="template",
		lazy="joined",
		cascade="all, delete-orphan",
		foreign_keys="TemplateStage.template_id",
	)
	workflows = relationship(
		"Workflow",
		back_populates="template",
		lazy="joined",
		cascade="all, delete-orphan",
		foreign_keys="Workflow.template_id",
	)

	def __repr__(self):
		return f"<Template(id={self.id}, name={self.name})>"
