from sqlalchemy import Column, String
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, foreign_key, primary_key


class Village(BaseModel, AuditMixin):
	"""Model for traditional authorities."""

	__tablename__ = tables.villages

	id = primary_key()
	name = Column(String, nullable=False)
	district_id = foreign_key(f"{tables.districts}.id", nullable=False)
	ta_id = foreign_key(f"{tables.traditional_authorities}.id", nullable=True)
	description = Column(String, nullable=True)

	# Relationships
	district = relationship(
		"District",
		back_populates="villages",
		uselist=False,
	)
	ta = relationship(
		"TraditionalAuthority",
		back_populates="villages",
		uselist=False,
	)

	def __repr__(self):
		return f"<TraditionalAuthority(id={self.id}, name={self.name})>"
