<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Two-Factor Authentication Code</title>
    <style>
      @import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Inter", Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        padding: 0px;
        margin: 0;
      }

      .email-container {
        max-width: 600px;
        margin: 0 auto;
        background: #ffffff;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(3, 111, 238, 0.08);
        overflow: hidden;
        border: 1px solid rgba(3, 111, 238, 0.1);
      }

      .header {
        background: linear-gradient(135deg, #036fee 0%, #0052cc 100%);
        padding: 40px 30px;
        text-align: center;
        position: relative;
        overflow: hidden;
      }

      .header::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(
          circle,
          rgba(255, 255, 255, 0.1) 0%,
          transparent 70%
        );
        animation: float 6s ease-in-out infinite;
      }

      @keyframes float {
        0%,
        100% {
          transform: translate(0, 0) rotate(0deg);
        }
        50% {
          transform: translate(-20px, -20px) rotate(180deg);
        }
      }

      .logo {
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 16px;
        margin: 0 auto 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        position: relative;
        z-index: 1;
      }

      .logo::before {
        content: "🔐";
        font-size: 28px;
      }

      .header h1 {
        color: white;
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 8px;
        position: relative;
        z-index: 1;
      }

      .header p {
        color: rgba(255, 255, 255, 0.9);
        font-size: 16px;
        font-weight: 400;
        position: relative;
        z-index: 1;
      }

      .content {
        padding: 50px 40px;
        text-align: center;
      }

      .greeting {
        font-size: 18px;
        color: #475569;
        margin-bottom: 30px;
        font-weight: 500;
      }

      .message {
        font-size: 16px;
        color: #64748b;
        margin-bottom: 40px;
        line-height: 1.7;
      }

      .code-container {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border: 2px dashed #036fee;
        border-radius: 16px;
        padding: 30px;
        margin: 40px 0;
        position: relative;
        overflow: hidden;
      }

      .code-label {
        font-size: 14px;
        color: #64748b;
        font-weight: 500;
        margin-bottom: 15px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .verification-code {
        font-size: 36px;
        font-weight: 700;
        color: #036fee;
        letter-spacing: 8px;
        font-family: "Courier New", monospace;
        background: linear-gradient(135deg, #036fee 0%, #f31261 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 4px 8px rgba(3, 111, 238, 0.2);
        margin: 10px 0;
        position: relative;
      }

      .code-digits {
        display: inline-flex;
        gap: 8px;
        align-items: center;
        justify-content: center;
      }

      .code-digit {
        background: white;
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        font-weight: bolder;
        color: #036fee;
        transition: all 0.3s ease;
        padding: 10px 15px;
      }

      .expiry-info {
        background: linear-gradient(135deg, #fef7cd 0%, #fef3c7 100%);
        border: 1px solid #f59e0b;
        border-radius: 12px;
        padding: 20px;
        margin: 30px 0;
        color: #92400e;
        font-size: 14px;
        font-weight: 500;
      }

      .expiry-info::before {
        content: "⏰";
        margin-right: 8px;
        font-size: 16px;
      }

      .security-notice {
        background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
        border: 1px solid #f31261;
        border-radius: 12px;
        padding: 20px;
        margin: 30px 0;
        color: #991b1b;
        font-size: 14px;
        font-weight: 500;
        line-height: 1.6;
      }

      .security-notice::before {
        content: "🛡️";
        margin-right: 8px;
        font-size: 16px;
      }

      .help-section {
        margin-top: 40px;
        padding-top: 30px;
        border-top: 1px solid #e2e8f0;
      }

      .help-text {
        color: #64748b;
        font-size: 14px;
        margin-bottom: 20px;
        line-height: 1.6;
      }

      .support-link {
        display: inline-flex;
        align-items: center;
        color: #036fee;
        text-decoration: none;
        font-weight: 500;
        font-size: 14px;
        border: 1px solid #036fee;
        padding: 8px 16px;
        border-radius: 8px;
        transition: all 0.3s ease;
      }

      .support-link:hover {
        background: #036fee;
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(3, 111, 238, 0.3);
      }

      .footer {
        background: #f8fafc;
        padding: 30px;
        text-align: center;
        border-top: 1px solid #e2e8f0;
      }

      .footer p {
        color: #64748b;
        font-size: 12px;
        line-height: 1.5;
        margin-bottom: 10px;
      }

      .footer-links {
        margin-top: 15px;
      }

      .footer-links a {
        color: #036fee;
        text-decoration: none;
        font-size: 12px;
        margin: 0 10px;
        transition: color 0.3s ease;
      }

      .footer-links a:hover {
        color: #f31261;
      }

      @media (max-width: 600px) {
        .email-container {
          margin: 10px;
          border-radius: 16px;
        }

        .content {
          padding: 30px 20px;
        }

        .header {
          padding: 30px 20px;
        }

        .header h1 {
          font-size: 24px;
        }

        .verification-code {
          font-size: 28px;
          letter-spacing: 4px;
        }
        .code-digits {
          gap: 4px;
        }
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <div class="header">
        <div class="logo"></div>
        <h1>Verification Code</h1>
        <p>Secure your account with two-factor authentication</p>
      </div>

      <div class="content">
        <div class="greeting">Hie there {{username}}! 👋</div>

        <div class="message">
          We received a request to sign in to your account. To complete the
          sign-in process, please use the verification code below:
        </div>

        <div class="code-container">
          <div class="code-label">Your Verification Code</div>
          <div class="code-digits">
            <div class="code-digit">{{code}}</div>
          </div>
        </div>

        <div class="expiry-info">
          This code expires in {{expires_in_minutes}} minutes for your security
        </div>

        <div class="security-notice">
          <strong>Important:</strong> If you didn't request this code, please
          ignore this email and consider changing your password. Never share
          this code with anyone.
        </div>

        <div class="help-section">
          <div class="help-text">
            Having trouble? Our support team is here to help you 24/7.
          </div>
          <a href="email:{{support_email}}" class="support-link"
            >Contact Support</a
          >
        </div>
      </div>

      <div class="footer">
        <p>This is an automated message. Please do not reply to this email.</p>
        <p>&copy; {{year}} {{app_name}}. All rights reserved.</p>
        <div class="footer-links">
          <a href="#">Privacy Policy</a>
          <a href="#">Terms of Service</a>
          <a href="#">Unsubscribe</a>
        </div>
      </div>
    </div>
  </body>
</html>
