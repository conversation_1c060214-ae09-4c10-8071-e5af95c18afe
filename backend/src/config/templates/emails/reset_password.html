<!-- Password Reset Email Template -->
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset - {{app_name}}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f6f8;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .content {
            padding: 40px 30px;
            line-height: 1.6;
        }
        .greeting {
            font-size: 18px;
            margin-bottom: 20px;
            color: #2c3e50;
        }
        .message {
            font-size: 16px;
            margin-bottom: 30px;
            color: #555;
        }
        .reset-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            margin: 20px 0;
            transition: transform 0.2s;
        }
        .reset-button:hover {
            transform: translateY(-2px);
        }
        .security-notice {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
        }
        .security-notice h3 {
            color: #856404;
            margin-top: 0;
            font-size: 16px;
        }
        .security-notice p {
            color: #856404;
            margin-bottom: 0;
            font-size: 14px;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px 30px;
            text-align: center;
            border-top: 1px solid #eee;
        }
        .footer p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
        .link-fallback {
            word-break: break-all;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 20px 0;
            font-size: 14px;
            color: #666;
        }
        @media (max-width: 600px) {
            .content {
                padding: 20px;
            }
            .reset-button {
                display: block;
                text-align: center;
                margin: 20px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Password Reset</h1>
        </div>
        
        <div class="content">
            <div class="greeting">
                Hello {{user_name}},
            </div>
            
            <div class="message">
                We received a request to reset your password for your {{app_name}} account. 
                If you made this request, click the button below to reset your password:
            </div>
            
            <div style="text-align: center;">
                <a href="{{reset_url}}" class="reset-button">Reset Your Password</a>
            </div>
            
            <div class="security-notice">
                <h3>🛡️ Security Notice!</h3>
                <p>
                    This password reset link will expire in {{expiry_hours}} hours for your security. 
                    If you didn't request this reset, please ignore this email or contact our support team.
                </p>
            </div>
            
            <div class="message">
                If the button above doesn't work, copy and paste this link into your browser:
            </div>
            
            <div class="link-fallback">
                {{reset_url}}
            </div>
        </div>
        
        <div class="footer">
            <p>© {{year}} {{app_name}}. All rights reserved.</p>
            <p>If you have any questions, please contact our support team.</p>
        </div>
    </div>
</body>
</html>