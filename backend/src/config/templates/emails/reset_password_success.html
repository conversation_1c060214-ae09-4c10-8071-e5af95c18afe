<!-- Password Reset Confirmation Email Template -->
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset Successful - {{app_name}}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f6f8;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .content {
            padding: 40px 30px;
            line-height: 1.6;
        }
        .success-message {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .success-message h2 {
            color: #155724;
            margin-top: 0;
            font-size: 20px;
        }
        .success-message p {
            color: #155724;
            margin-bottom: 0;
        }
        .login-button {
            display: inline-block;
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            margin: 20px 0;
            transition: transform 0.2s;
        }
        .login-button:hover {
            transform: translateY(-2px);
        }
        .security-tips {
            background-color: #e7f3ff;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
        }
        .security-tips h3 {
            color: #0c5460;
            margin-top: 0;
            font-size: 16px;
        }
        .security-tips ul {
            color: #0c5460;
            margin-bottom: 0;
            font-size: 14px;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px 30px;
            text-align: center;
            border-top: 1px solid #eee;
        }
        .footer p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
        @media (max-width: 600px) {
            .content {
                padding: 20px;
            }
            .login-button {
                display: block;
                text-align: center;
                margin: 20px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✅ Password Reset Successful</h1>
        </div>
        
        <div class="content">
            <div class="success-message">
                <h2>🎉 Your password has been reset successfully!</h2>
                <p>Hello {{user_name}}, your {{app_name}} account password was reset on {{reset_time}}.</p>
            </div>
            
            <div style="text-align: center;">
                <a href="{{login_url}}" class="login-button">Log In Now</a>
            </div>
            
            <div class="security-tips">
                <h3>🔒 Security Tips</h3>
                <ul>
                    <li>Use a strong, unique password for your account</li>
                    <li>Enable two-factor authentication if available</li>
                    <li>Never share your password with anyone</li>
                    <li>Log out from shared or public devices</li>
                </ul>
            </div>
            
            <p>
                If you didn't reset your password, please contact our support team immediately 
                as your account may have been compromised.
            </p>
        </div>
        
        <div class="footer">
            <p>© {{year}} {{app_name}}. All rights reserved.</p>
            <p>If you have any questions, please contact our support team.</p>
        </div>
    </div>
</body>
</html>