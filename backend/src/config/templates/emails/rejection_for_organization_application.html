<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Organization Application Update</title>
    <style>
      @import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Inter", Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        padding: 20px;
      }

      .email-container {
        max-width: 600px;
        margin: 0 auto;
        background: #ffffff;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(3, 111, 238, 0.08);
        overflow: hidden;
        border: 1px solid rgba(3, 111, 238, 0.1);
      }

      .header {
        background: linear-gradient(135deg, #64748b 0%, #475569 100%);
        padding: 40px 30px;
        text-align: center;
        position: relative;
        overflow: hidden;
      }

      .header::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(
          circle,
          rgba(255, 255, 255, 0.1) 0%,
          transparent 70%
        );
        animation: float 6s ease-in-out infinite;
      }

      @keyframes float {
        0%,
        100% {
          transform: translate(0, 0) rotate(0deg);
        }
        50% {
          transform: translate(-20px, -20px) rotate(180deg);
        }
      }

      .logo {
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 16px;
        margin: 0 auto 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        position: relative;
        z-index: 1;
      }

      .logo::before {
        content: "📋";
        font-size: 28px;
      }

      .header h1 {
        color: white;
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 8px;
        position: relative;
        z-index: 1;
      }

      .header p {
        color: rgba(255, 255, 255, 0.9);
        font-size: 16px;
        font-weight: 400;
        position: relative;
        z-index: 1;
      }

      .content {
        padding: 50px 40px;
      }

      .greeting {
        font-size: 18px;
        color: #475569;
        margin-bottom: 30px;
        font-weight: 500;
      }

      .status-container {
        background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        border: 1px solid #f31261;
        border-radius: 16px;
        padding: 30px;
        margin: 30px 0;
        text-align: center;
        position: relative;
        overflow: hidden;
      }

      .status-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #f31261 0%, #dc2626 100%);
        border-radius: 50%;
        margin: 0 auto 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 24px;
      }

      .status-icon::before {
        content: "✕";
      }

      .status-title {
        font-size: 24px;
        font-weight: 700;
        color: #991b1b;
        margin-bottom: 10px;
      }

      .status-subtitle {
        font-size: 16px;
        color: #7f1d1d;
        font-weight: 500;
      }

      .application-details {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 25px;
        margin: 30px 0;
      }

      .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #e2e8f0;
      }

      .detail-row:last-child {
        border-bottom: none;
      }

      .detail-label {
        font-weight: 600;
        color: #475569;
        font-size: 14px;
      }

      .detail-value {
        color: #64748b;
        font-size: 14px;
      }

      .rejection-message {
        background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
        border: 1px solid #d97706;
        border-radius: 12px;
        padding: 25px;
        margin: 30px 0;
        color: #92400e;
        font-size: 15px;
        line-height: 1.7;
      }

      .rejection-title {
        font-weight: 600;
        margin-bottom: 15px;
        color: #92400e;
        display: flex;
        align-items: center;
      }

      .rejection-title::before {
        content: "💡";
        margin-right: 10px;
        font-size: 18px;
      }

      .reasons-list {
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 25px;
        margin: 25px 0;
      }

      .reasons-title {
        font-weight: 600;
        color: #475569;
        margin-bottom: 15px;
        font-size: 16px;
      }

      .reason-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;
        font-size: 14px;
        color: #64748b;
        line-height: 1.6;
      }

      .reason-item:last-child {
        margin-bottom: 0;
      }

      .reason-bullet {
        color: #f31261;
        font-weight: 700;
        margin-right: 10px;
        margin-top: 2px;
      }

      .next-steps {
        background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
        border: 1px solid #036fee;
        border-radius: 12px;
        padding: 25px;
        margin: 30px 0;
      }

      .next-steps-title {
        font-weight: 600;
        color: #036fee;
        margin-bottom: 15px;
        font-size: 16px;
        display: flex;
        align-items: center;
      }

      .next-steps-title::before {
        content: "🚀";
        margin-right: 10px;
        font-size: 18px;
      }

      .next-steps p {
        color: #1e40af;
        font-size: 14px;
        line-height: 1.6;
        margin-bottom: 20px;
      }

      .action-buttons {
        display: flex;
        gap: 15px;
        justify-content: center;
        margin: 30px 0;
        flex-wrap: wrap;
      }

      .btn {
        padding: 12px 24px;
        border-radius: 10px;
        text-decoration: none;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.3s ease;
        border: 2px solid;
        text-align: center;
        min-width: 140px;
      }

      .btn-primary {
        background: linear-gradient(135deg, #036fee 0%, #0052cc 100%);
        color: white;
        border-color: #036fee;
      }

      .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(3, 111, 238, 0.3);
      }

      .btn-secondary {
        background: white;
        color: #64748b;
        border-color: #e2e8f0;
      }

      .btn-secondary:hover {
        border-color: #036fee;
        color: #036fee;
        transform: translateY(-1px);
      }

      .support-section {
        text-align: center;
        margin-top: 40px;
        padding-top: 30px;
        border-top: 1px solid #e2e8f0;
      }

      .support-text {
        color: #64748b;
        font-size: 14px;
        margin-bottom: 20px;
        line-height: 1.6;
      }

      .support-link {
        display: inline-flex;
        align-items: center;
        color: #036fee;
        text-decoration: none;
        font-weight: 500;
        font-size: 14px;
        border: 1px solid #036fee;
        padding: 8px 16px;
        border-radius: 8px;
        transition: all 0.3s ease;
      }

      .support-link:hover {
        background: #036fee;
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(3, 111, 238, 0.3);
      }

      .footer {
        background: #f8fafc;
        padding: 30px;
        text-align: center;
        border-top: 1px solid #e2e8f0;
      }

      .footer p {
        color: #64748b;
        font-size: 12px;
        line-height: 1.5;
        margin-bottom: 10px;
      }

      .footer-links {
        margin-top: 15px;
      }

      .footer-links a {
        color: #036fee;
        text-decoration: none;
        font-size: 12px;
        margin: 0 10px;
        transition: color 0.3s ease;
      }

      .footer-links a:hover {
        color: #f31261;
      }

      @media (max-width: 600px) {
        .email-container {
          margin: 10px;
          border-radius: 16px;
        }

        .content {
          padding: 30px 20px;
        }

        .header {
          padding: 30px 20px;
        }

        .header h1 {
          font-size: 24px;
        }

        .detail-row {
          flex-direction: column;
          align-items: flex-start;
          gap: 5px;
        }

        .action-buttons {
          flex-direction: column;
          align-items: center;
        }

        .btn {
          width: 100%;
          max-width: 250px;
        }
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <div class="header">
        <div class="logo"></div>
        <h1>Application Update</h1>
        <p>Your organization verification status</p>
      </div>

      <div class="content">
        <div class="greeting">Hello {{ applicant_name }},</div>

        <div class="status-container">
          <div class="status-icon"></div>
          <div class="status-title">Application Not Approved</div>
          <div class="status-subtitle">
            Your organization application requires additional review
          </div>
        </div>

        <div class="application-details">
          <div class="detail-row">
            <span class="detail-label">Organization Name:</span>
            <span class="detail-value">{{ organization_name }}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Application ID:</span>
            <span class="detail-value">{{ application_id }}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Submitted On:</span>
            <span class="detail-value">{{ submitted_date }}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Reviewed On:</span>
            <span class="detail-value">{{ reviewed_date }}</span>
          </div>
        </div>

        <div class="rejection-message">
          <div class="rejection-title">Why wasn't my application approved?</div>
          <p>
            Thank you for your interest in creating an organization on our
            platform. After careful review of your application, we found that
            some requirements need to be addressed before we can approve your
            organization.
          </p>
        </div>

        <div class="reasons-list">
          <div class="reasons-title">Specific areas that need attention:</div>
          {% for reason in rejection_reasons %}
          <div class="reason-item">
            <span class="reason-bullet">•</span>
            <span>{{ reason.content | safe }}</span>
          </div>
          {% endfor %}
        </div>

        <div class="next-steps">
          <div class="next-steps-title">What's next?</div>
          <p>
            Don't worry! You can address these items and resubmit your
            application. We're here to help you succeed and would love to have
            you join our platform once all requirements are met.
          </p>
          <p>
            <strong>You can reapply immediately</strong> after addressing the
            items mentioned above. There's no waiting period required.
          </p>
        </div>

        <div class="action-buttons">
          <a href="#" class="btn btn-primary">Submit New Application</a>
          <a href="#" class="btn btn-secondary">View Requirements</a>
        </div>

        <div class="support-section">
          <div class="support-text">
            Have questions about the requirements or need help with your
            application?<br />
            Our team is ready to assist you through the process.
          </div>
          <a href="#" class="support-link">Get Help with Application</a>
        </div>
      </div>

      <div class="footer">
        <p>
          This is an automated message regarding your organization application.
        </p>
        <p>&copy; {{year}} {{app_name}}. All rights reserved.</p>
        <div class="footer-links">
          <a href="#">Privacy Policy</a>
          <a href="#">Terms of Service</a>
          <a href="#">Contact Support</a>
        </div>
      </div>
    </div>
  </body>
</html>
