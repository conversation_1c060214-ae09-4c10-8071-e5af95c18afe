from src.config.settings import IS_PRODUCTION

API_METADATA = {
	"title": "myNGO API - Ngora.mw Backend",
	"description": (
		"**Backend API for Ngora.mw**\n\n"
		"This API empowers the myngo.ngora.mw platform to efficiently manage Non-Governmental Organizations (NGOs), "
		"their projects, and operational data. It provides a robust and secure foundation for "
		"tracking activities, resources, and impact.\n\n"
		"Key Features:\n"
		"- **Comprehensive NGO Management:** Create, retrieve, update, and delete NGO profiles.\n"
		"- **Project Lifecycle Tracking:** Manage projects from inception to completion, including phases, budgets, and participants.\n"
		"- **Operational Efficiency:** Tools for managing activities, reporting, and resource allocation.\n"
		"- **Health Checks:** Dedicated endpoints for monitoring API status and health.\n"
		"- **Pagination:** Consistent pagination across list endpoints for efficient data retrieval using `fastapi-pagination`.\n"
		"- **Centralized Error Handling:** Uniform and informative error responses for both expected and unexpected issues (managed by `src.core.exceptions.handlers`).\n"
		"- **Secured CORS Policy:** Configured to allow secure cross-origin requests from specified frontends (`get_trusted_hosts()`).\n"
		"- **Authentication & Authorization:** Implemented using JWT-based tokens for secure user access and role-based permissions.\n\n"
		"**Access the interactive API documentation below (Swagger UI) or via ReDoc.**"
	),
	"version": "1.0.0",
	"debug": not IS_PRODUCTION,
	"openapi_url": "/v1/openapi.json",
	"docs_url": "/v1/docs",
	"redoc_url": "/v1/redoc",
	"terms_of_service": "https://www.ngora.mw/terms",
	"contact": {
		"name": "Ngora.mw Technical Support",
		"url": "https://www.ngora.mw/contact",
		"email": "<EMAIL>",
	},
	"servers": [
		{"url": "http://localhost:8000", "description": "Local Development Server"},
		{"url": "https://api.myngo.ngora.mw/v1", "description": "Production API Server (Malawi)"},
		{"url": "https://dev.myngo.ngora.mw/v1", "description": "Development/Staging API Server"},
	],
	"swagger_ui_oauth2_redirect_url": "/v1/docs/oauth2-redirect",
}
